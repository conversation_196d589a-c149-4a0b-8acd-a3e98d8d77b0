package com.hengjian.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.hengjian.common.core.constant.TenantConstants;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.common.web.core.BaseController;
import com.hengjian.system.domain.bo.SysTenantBo;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.ISysTenantService;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 租户管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/tenant")
public class SysTenantController extends BaseController {

    private final ISysTenantService tenantService;

    /**
     * 查询租户列表
     */
//    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission(value = "system:tenant:list", orRole = TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @GetMapping("/list")
    public TableDataInfo<SysTenantVo> list(SysTenantBo bo, PageQuery pageQuery) {
        return tenantService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出租户列表
     */
//    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission(value = "system:tenant:export", orRole = TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @Log(title = "租户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<Void> export(@RequestBody SysTenantBo bo) {
        // List<SysTenantVo> list = tenantService.queryPageListForExport(bo);
        // ExcelUtil.exportExcel(list, "租户", SysTenantVo.class, response);
        return tenantService.queryPageListForExport(bo);
    }

    /**
     * 获取租户详细信息
     *
     * @param id 主键
     */
//    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission(value = "system:tenant:query", orRole = TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @GetMapping("/{id}")
    public R<SysTenantVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(tenantService.queryById(id));
    }

    /**
     * 新增租户
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:add")
    @Log(title = "租户", businessType = BusinessType.INSERT)
    @Lock4j
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysTenantBo bo) {
        if (!tenantService.checkCompanyNameUnique(bo)) {
            return R.fail("新增租户'" + bo.getCompanyName() + "'失败，企业名称已存在");
        }
        return toAjax(TenantHelper.ignore(() -> tenantService.insertByBo(bo)));
    }

    /**
     * 修改租户
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:edit")
    @Log(title = "租户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysTenantBo bo) {
        tenantService.checkTenantAllowed(bo.getTenantId());
        if (!tenantService.checkCompanyNameUnique(bo)) {
            return R.fail("修改租户'" + bo.getCompanyName() + "'失败，公司名称已存在");
        }
        return toAjax(tenantService.updateByBo(bo));
    }

    /**
     * 状态修改
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:edit")
    @Log(title = "租户", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysTenantBo bo) {
        tenantService.checkTenantAllowed(bo.getTenantId());
        return toAjax(tenantService.updateTenantStatus(bo));
    }

    /**
     * 删除租户
     *
     * @param ids 主键串
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:remove")
    @Log(title = "租户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        // 禁止删除租户
        // return toAjax(tenantService.deleteWithValidByIds(List.of(ids), true));
        return R.ok();
    }

    /**
     * 动态切换租户
     *
     * @param tenantId 租户ID
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @GetMapping("/dynamic/{tenantId}")
    public R<Void> dynamicTenant(@NotBlank(message = "租户ID不能为空") @PathVariable String tenantId) {
        TenantHelper.setDynamic(tenantId);
        return R.ok();
    }

    /**
     * 清除动态租户
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @GetMapping("/dynamic/clear")
    public R<Void> dynamicClear() {
        TenantHelper.clearDynamic();
        return R.ok();
    }


    /**
     * 同步租户套餐
     *
     * @param tenantId  租户id
     * @param packageId 套餐id
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:tenant:edit")
    @Log(title = "租户", businessType = BusinessType.UPDATE)
    @GetMapping("/syncTenantPackage")
    public R<Void> syncTenantPackage(@NotBlank(message = "租户ID不能为空") String tenantId, @NotBlank(message = "套餐ID不能为空") String packageId) {
        return toAjax(TenantHelper.ignore(() -> tenantService.syncTenantPackage(tenantId, packageId)));
    }

    @GetMapping("/mine")
    public R<SysTenantVo> getMyTenant() {
        return R.ok(tenantService.getMyTenant());
    }

    @GetMapping("/itExist")
    public R<Boolean> itExist(String tenantId) {
        return R.ok(tenantService.existTenantId(tenantId));
    }
    /**
     * @description: 根据租户ID/昵称/是否需要测算查询已经审核通过的租户列表
     * @author: Len
     * @date: 2024/9/26 14:36
     * @param: tenantId
     * @param: nickName
     * @return: com.hengjian.common.core.domain.R<java.lang.Void>
     **/
    @GetMapping("/getApprovedTenant")
    public R getApprovedTenant( String tenantId, String nickName,Integer isCalculation) {
        return R.ok(tenantService.getApprovedTenant(tenantId,nickName,isCalculation));
    }
    /**
     * @description: 更新租户是否测算状态
     * @author: Len
     * @date: 2024/9/26 14:52
     * @param: tenantIds
     * @param: isCalculation
     * @return: com.hengjian.common.core.domain.R
     **/
    @Log(title = "分销商设置是否测算", businessType = BusinessType.UPDATE)
    @PostMapping("/updateTenantIsCalculation")
    @Transactional
    public R updateTenantIsCalculation(@RequestBody List<String> tenantIds,Integer isCalculation) {
        //校验
        if (CollectionUtil.isEmpty(tenantIds)){
            tenantService.updateTenantIsCalculation(null,2);
            return R.ok("操作成功");
        }else {
            tenantService.updateTenantIsCalculation(null,2);
            tenantService.updateTenantIsCalculation(tenantIds,isCalculation);
        }
        return R.ok("操作成功");
    }
    /**
     * @description: 判断指定的租户ID是否支持测算
     * @author: Len
     * @date: 2024/9/27 11:09
     * @param: tenantId
     * @param: isCalculation
     * @return: com.hengjian.common.core.domain.R
     **/
    @GetMapping("/getIsApprovedByTenant")
    public R getApprovedTenant( String tenantId, Integer isCalculation) {
        return R.ok(tenantService.getIsApprovedTenant(tenantId,isCalculation));
    }

}
