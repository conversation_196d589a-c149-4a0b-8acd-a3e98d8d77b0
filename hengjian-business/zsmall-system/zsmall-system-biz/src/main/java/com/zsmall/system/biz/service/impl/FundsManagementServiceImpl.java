package com.zsmall.system.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.*;
import com.hengjian.common.excel.convert.ExcelBigNumberConvert;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.system.domain.SysTenant;
import com.hengjian.system.domain.SysUser;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.domain.vo.SysUserVo;
import com.hengjian.system.mapper.SysUserMapper;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.constant.MallConstants;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.payment.WalletStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.TransactionMethodEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.exception.WalletException;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.order.entity.domain.bo.order.OrderExportListDTO;
import com.zsmall.order.entity.domain.event.StatsPlatformOrderPaymentRefundAmountEvent;
import com.zsmall.order.entity.mapper.OrdersMapper;
import com.zsmall.order.entity.mapper.TemporaryTableMapper;
import com.zsmall.system.biz.service.FundsManagementService;
import com.zsmall.system.biz.service.TenantWalletService;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.domain.TenantWallet;
import com.zsmall.system.entity.domain.TransactionReceipt;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.TransactionRecordAttachment;
import com.zsmall.system.entity.domain.bo.funds.*;
import com.zsmall.system.entity.domain.dto.TenantSiteCountryCurrency;
import com.zsmall.system.entity.domain.vo.funds.*;
import com.zsmall.system.entity.domain.vo.payment.ReceiptTotalAmountVo;
import com.zsmall.system.entity.domain.vo.receipt.TenantReceiptAccountCreditVo;
import com.zsmall.system.entity.domain.vo.receipt.TenantReceiptAccountPayoneerVo;
import com.zsmall.system.entity.domain.vo.transaction.TransactionRecordExportVo;
import com.zsmall.system.entity.enums.TransactionBusinessRelationEnum;
import com.zsmall.system.entity.iservice.*;
import com.zsmall.system.entity.mapper.TenantSalesChannelMapper;
import com.zsmall.system.entity.mapper.TransactionReceiptMapper;
import com.zsmall.system.entity.mapper.TransactionRecordMapper;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class FundsManagementServiceImpl implements FundsManagementService {

    private final IBillService iBillService;
    private final ITenantWalletService iTenantWalletService;
    private final ITransactionRecordService iTransactionRecordService;
    private final ITenantReceiptAccountService iTenantReceiptAccountService;
//    private final IProductActivityCheckoutService iProductActivityCheckoutService;
    private  final ITransactionsOrdersService iTransactionsOrdersService;
    private final TenantWalletService tenantWalletService;

    private final TransactionReceiptMapper transactionReceiptMapper;
    private final TransactionRecordMapper transactionRecordMapper;

    private final MallSystemCodeGenerator mallSystemCodeGenerator;
    private final BillSupport billSupport;
    private final TenantSalesChannelMapper tenantSalesChannelMapper;
    private final SysUserMapper sysUserMapper;
    private final TemporaryTableMapper temporaryTableMapper;
    private final ISysTenantService iSysTenantService;
    private final OrdersMapper ordersMapper;
    private final ITransactionRecordAttachmentService iTransactionRecordAttachmentService;
    private final ITenantSiteService tenantSiteService;

    /**
     * 资金总览统计接口
     *
     * @return
     */
    @Override
    public PlatformFundsStatisticsVo getPlatformFundStatistics() {
        //计算平台在账总金额
        ReceiptTotalAmountVo depositTotalAmount = transactionReceiptMapper.statsPlatformTotalAmount(TransactionTypeEnum.Recharge.getValue());
        ReceiptTotalAmountVo settledTotalAmount = transactionReceiptMapper.statsPlatformTotalAmount(TransactionTypeEnum.Withdrawal.getValue());
        BigDecimal platformTotalAmount = NumberUtil.sub(depositTotalAmount.getTotalAmount(), settledTotalAmount.getTotalAmount());
        log.info("platformTotalAmount = {}, depositTotalAmount = {}, settledTotalAmount = {}",
            platformTotalAmount, JSONUtil.toJsonStr(depositTotalAmount), JSONUtil.toJsonStr(settledTotalAmount));
        //计算供应商可提现总金额
        BigDecimal supBalace = iBillService.sumUnsettledTotalAmount(null);
        //计算分销商钱包在账总金额
        BigDecimal distrBalace = iTenantWalletService.sumDistributorBalance();
        //计算循环保证金总金额
        BigDecimal circularDeposit = iBillService.sumCircularDeposit(null);
        log.info("supBalace = {}, distrBalace = {}, circularDeposit = {}", supBalace, distrBalace, circularDeposit);

        BigDecimal recentPlatformIncomeTotalAmount = BigDecimal.ZERO;
        // 计算近期平台收入
        //计算近30天平台收入总金额
        Date endDate = DateUtil.endOfDay(new Date());
        Date startDate = DateUtil.beginOfDay(DateUtil.offsetDay(endDate, -30));
        // 1、获取订单相关支付、退款总金额
        // 事件调用
        StatsPlatformOrderPaymentRefundAmountEvent orderPaymentRefundAmountEvent = new StatsPlatformOrderPaymentRefundAmountEvent(startDate, endDate);
        SpringUtils.context().publishEvent(orderPaymentRefundAmountEvent);
        BigDecimal orderTotalAmount = orderPaymentRefundAmountEvent.getOrderTotalAmount();
        BigDecimal refundTotalAmount = orderPaymentRefundAmountEvent.getRefundTotalAmount();

        // 3、平台扣款/平台汇款
        // 平台扣款
        BigDecimal deductTotalAmount = TenantHelper.ignore(() -> iTransactionRecordService.sumTransactionAmount(TransactionTypeEnum.Expenditure, TransactionSubTypeEnum.PlatformDeduct, startDate, endDate));
        // 活动违约金
//        BigDecimal penaltyFeeAmount = TenantHelper.ignore(() -> iProductActivityCheckoutService.sumCheckoutAmountByParams(ActivityCheckoutTypeEnum.PenaltyFee, startDate, endDate));

        // 平台汇款
        BigDecimal remitTotalAmount = TenantHelper.ignore(() -> iTransactionRecordService.sumTransactionAmount(TransactionTypeEnum.Income, TransactionSubTypeEnum.PlatformRemit, startDate, endDate));

        // 平台收入
//        BigDecimal incomeTotalAmount = NumberUtil.add(orderTotalAmount, deductTotalAmount, penaltyFeeAmount);
//        log.info("incomeTotalAmount = {}, orderTotalAmount = {}, deductTotalAmount = {}", incomeTotalAmount, orderTotalAmount, deductTotalAmount);
//        // 平台当前收入
//        recentPlatformIncomeTotalAmount = NumberUtil.sub(incomeTotalAmount, refundTotalAmount, remitTotalAmount);
//        log.info("refundTotalAmount = {}, incomeTotalAmount = {}, refundTotalAmount = {}",
//            recentPlatformIncomeTotalAmount, incomeTotalAmount, refundTotalAmount);

        PlatformFundsStatisticsVo respBody = new PlatformFundsStatisticsVo();
        respBody.setPlatformTotalAmount(DecimalUtil.bigDecimalToString(platformTotalAmount));
        respBody.setRecentPlatformIncomeTotalAmount(DecimalUtil.bigDecimalToString(recentPlatformIncomeTotalAmount));
        respBody.setSupWithdrawnTotalAmount(DecimalUtil.bigDecimalToString(supBalace));
        respBody.setDistrTotalAmount(DecimalUtil.bigDecimalToString(distrBalace));
        respBody.setCircularDepositTotalAmount(DecimalUtil.bigDecimalToString(circularDeposit));

        return respBody;
    }

    /**
     * 翻页查询资金流水
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<PlatformFundsFlowVo> getFundsOverviewPage(FundsFlowQueryBo bo, PageQuery pageQuery) {
        bo.setTransactionState(TransactionStateEnum.Success.getValue());

        // 这里只记录充值和取现两种类型的流水。如果涉及到钱包相关金额，平台扣款、平台汇款 应该也是需要考虑在范围内的。目前暂不考虑
        bo.setTransactionTypes(Set.of(TransactionTypeEnum.Recharge.getValue(), TransactionTypeEnum.Withdrawal.getValue()));
        LambdaQueryWrapper<TransactionRecord> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(NoDeptBaseEntity::getCreateTime);
        Page<TransactionRecord> result = TenantHelper.ignore(() -> iTransactionRecordService.page(pageQuery.build(), lqw));
        long total = result.getTotal();
        List<TransactionRecord> records = result.getRecords();

        List<PlatformFundsFlowVo> platformFundsFlowVos = convertFundsFlowVo(records);

        return TableDataInfo.build(platformFundsFlowVos, total);
    }

    @NotNull
    private List<PlatformFundsFlowVo> convertFundsFlowVo(List<TransactionRecord> records) {
        List<PlatformFundsFlowVo> platformFundsFlowVos = new ArrayList<>();
        List<String> tenantIds = records.stream().map(TransactionRecord::getTenantId).distinct().collect(Collectors.toList());
        Map<String, SysTenant> tenantMapByTenantIds = iSysTenantService.getTenantMapByTenantIds(tenantIds);

        if (CollUtil.isNotEmpty(records)) {
            PlatformFundsFlowVo platformFundsFlowVo = null;
            for (TransactionRecord transactionRecord : records) {
                platformFundsFlowVo = MapstructUtils.convert(transactionRecord, PlatformFundsFlowVo.class);

                Long transactionRecordId = transactionRecord.getId();
                TransactionTypeEnum transactionType = transactionRecord.getTransactionType();
                String tenantId = transactionRecord.getTenantId();

                // 如果是充值类型，则到账账户为 钱包名词
                String receiptAccount = "";
                String payer = null;
                String payee = null;
                if (ObjectUtil.equals(transactionType, TransactionTypeEnum.Recharge)) {
                    payer = tenantId;
                    payee = MallConstants.MALL_NAME;
                    receiptAccount = MallConstants.MALL_WALLET_NAME;
                } else if (ObjectUtil.equals(transactionType, TransactionTypeEnum.Withdrawal)) {
                    payer = MallConstants.MALL_NAME;
                    payee = tenantId;
                } else {
                    payer = MallConstants.MALL_NAME;
                    payee = MallConstants.MALL_NAME;
                }

                String paymentMethod = "";
                TransactionReceipt transactionReceipt = transactionReceiptMapper.selectByTransactionId(transactionRecordId);
                if(transactionReceipt != null) {
                    TransactionMethodEnum transactionMethod = transactionReceipt.getTransactionMethod();
                    Long receiptAccountId = transactionReceipt.getReceiptAccountId();
                    if(receiptAccountId != null) {

                        TenantReceiptAccountCreditVo receiptAccountCreditVo = iTenantReceiptAccountService.getReceiptAccountCreditVo(receiptAccountId);
                        if(receiptAccountCreditVo == null) {
                            TenantReceiptAccountPayoneerVo receiptAccountPayoneerVo = iTenantReceiptAccountService.getReceiptAccountPayoneerVo(receiptAccountId);
                            if(receiptAccountPayoneerVo != null) {
                                receiptAccount = receiptAccountPayoneerVo.getPayoneerEmail();
                            }
                        } else {
                            receiptAccount = receiptAccountCreditVo.getAccountNumber();
                        }

                    }
                    paymentMethod = transactionMethod.getValue();
                }
                if (ObjectUtil.isNotNull(tenantMapByTenantIds)){
                    SysTenant sysTenant = tenantMapByTenantIds.get(transactionRecord.getTenantId());
                    if (ObjectUtil.isNotNull(sysTenant)){
                        platformFundsFlowVo.setThirdChannelFlag(sysTenant.getThirdChannelFlag());
                        platformFundsFlowVo.setTenantType(sysTenant.getTenantType());
                    }
                }
                platformFundsFlowVo.setPayer(payer);
                platformFundsFlowVo.setPayee(payee);
                platformFundsFlowVo.setReceiptAccount(receiptAccount);
                platformFundsFlowVo.setPaymentMethod(paymentMethod);
                platformFundsFlowVos.add(platformFundsFlowVo);
            }
        }
        return platformFundsFlowVos;
    }

    /**
     * 查询资金流水
     *
     * @param bo
     * @return
     */
    @Override
    public List<PlatformFundsFlowVo> queryList(FundsFlowQueryBo bo) {
        bo.setTransactionState(TransactionStateEnum.Success.getValue());
        bo.setTransactionTypes(Set.of(TransactionTypeEnum.Recharge.getValue(), TransactionTypeEnum.Withdrawal.getValue()));
        LambdaQueryWrapper<TransactionRecord> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(NoDeptBaseEntity::getCreateTime);
        List<TransactionRecord> records = TenantHelper.ignore(() -> iTransactionRecordService.list(lqw));

        return convertFundsFlowVo(records);
    }

    /**
     * 获取分销商充值在账金额
     *
     * @return
     */
    @Override
    public String getDistributorBalance(String currency) {
        //计算分销商钱包在账总金额
        BigDecimal distrBalace = iTenantWalletService.sumDistributorBalanceByCurrency(currency);
        return DecimalUtil.bigDecimalToString(distrBalace);
    }

    /**
     * 资金流水条件拼接
     * @param bo
     * @return
     */
    private LambdaQueryWrapper<TransactionRecord> buildQueryWrapper(FundsFlowQueryBo bo) {
        LambdaQueryWrapper<TransactionRecord> lqw = Wrappers.lambdaQuery();

        String orderNo = bo.getOrderNo();
        String paymentType = bo.getPaymentType();
        List<String> tradingDates = bo.getTradingDates();
        String tradingType = bo.getTradingType();
        String payer = bo.getPayer();
        String payee = bo.getPayee();

        Set<String> transactionTypes = new HashSet<>();
        if(StringUtils.isNotBlank(tradingType)) {
            transactionTypes.add(tradingType);
        }

        lqw.eq(StringUtils.isNotBlank(bo.getTransactionState()), TransactionRecord::getTransactionState, bo.getTransactionState());
        lqw.eq(StringUtils.isNotBlank(orderNo), TransactionRecord::getTransactionNo, orderNo);
        lqw.exists(StringUtils.isNotBlank(paymentType),
            "select 1 from transaction_receipt tr where tr.transactions_id = transaction_record.id " +
                "and tr.transaction_method = {0}", paymentType);

        if (CollUtil.isNotEmpty(tradingDates)) {
            String start = tradingDates.get(0);
            String end = tradingDates.get(1);
            lqw.ge(TransactionRecord::getTransactionTime, DateUtil.beginOfDay(DateUtil.parseDate(start)))
                .le(TransactionRecord::getTransactionTime, DateUtil.endOfDay(DateUtil.parseDate(end)));
        }

        if (StringUtils.isNotBlank(payer)) {
            if (StrUtil.equalsIgnoreCase(payer, MallConstants.MALL_NAME)) {
                transactionTypes.add(TransactionTypeEnum.Withdrawal.getValue());
            } else {
                transactionTypes.add(TransactionTypeEnum.Recharge.getValue());
                lqw.eq(NoDeptTenantEntity::getTenantId, payer);
            }
        }
        if (StringUtils.isNotBlank(payee)) {
            if (StrUtil.equalsIgnoreCase(payee, MallConstants.MALL_NAME)) {
                transactionTypes.add(TransactionTypeEnum.Recharge.getValue());
            } else {
                transactionTypes.add(TransactionTypeEnum.Withdrawal.getValue());
                lqw.eq(NoDeptTenantEntity::getTenantId, payee);
            }
        }

        if(CollUtil.isNotEmpty(transactionTypes)) {
            lqw.in(TransactionRecord::getTransactionType, transactionTypes);
        } else if(CollUtil.isEmpty(transactionTypes) && CollUtil.isNotEmpty(bo.getTransactionTypes())) {
            // 如果上述逻辑条件中无交易类型，则取传入的交易类型
            lqw.in(TransactionRecord::getTransactionType, bo.getTransactionTypes());
        }

        return lqw;
    }


    /**
     * 翻页查询钱包使用记录
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<WalletBalanceUsageVo> getWalletUsagePage(WalletBalanceUsageQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TenantWallet> lqw = buildTenantWalletLqw(bo);
        lqw.orderByDesc(NoDeptBaseEntity::getCreateTime);
        Page<TenantWallet> result = TenantHelper.ignore(() -> iTenantWalletService.page(pageQuery.build(), lqw));
        long total = result.getTotal();
        List<TenantWallet> records = result.getRecords();

        List<WalletBalanceUsageVo> walletBalanceUsageVos = convertWalletBalanceUsageVos(records);

        return TableDataInfo.build(walletBalanceUsageVos, total);
    }

    @NotNull
    private List<WalletBalanceUsageVo> convertWalletBalanceUsageVos(List<TenantWallet> records) {
        List<WalletBalanceUsageVo> walletBalanceUsageVos = new ArrayList<>();
        if(CollUtil.isNotEmpty(records)) {
            records.forEach(record -> {
                String tenantId = record.getTenantId();
                BigDecimal walletBalance = record.getWalletBalance();

                // 充值记录
                TransactionRecord rechargeRecord = iTransactionRecordService.selectTheLatestRecord(tenantId, TransactionTypeEnum.Recharge, TransactionStateEnum.Success,record.getCurrency());
                // 支出记录
                TransactionRecord expenditureRecord = iTransactionRecordService.selectTheLatestRecord(tenantId, TransactionTypeEnum.Expenditure, TransactionStateEnum.Success,record.getCurrency());

                WalletBalanceUsageVo walletBalanceUsageVo = new WalletBalanceUsageVo();
                walletBalanceUsageVo.setBalance(DecimalUtil.bigDecimalToString(walletBalance));
                walletBalanceUsageVo.setTenantId(tenantId);
                walletBalanceUsageVo.setWalletState(record.getWalletState().getValue());
                walletBalanceUsageVo.setCurrency(record.getCurrency());
                walletBalanceUsageVo.setCurrencySymbol(record.getCurrencySymbol());

                if (rechargeRecord != null) {
                    String latest = DateUtil.formatDateTime(rechargeRecord.getTransactionTime());
                    walletBalanceUsageVo.setLastRechargeTime(latest);
                }
                if (expenditureRecord != null) {
                    String latest = DateUtil.formatDateTime(expenditureRecord.getTransactionTime());
                    walletBalanceUsageVo.setLastPaymentTime(latest);
                }
                walletBalanceUsageVos.add(walletBalanceUsageVo);
            });
        }
        return walletBalanceUsageVos;
    }

    /**
     * 查询钱包使用记录
     *
     * @param bo
     * @return
     */
    @Override
    public List<WalletBalanceUsageVo> queryWalletUsageList(WalletBalanceUsageQueryBo bo) {
        LambdaQueryWrapper<TenantWallet> lqw = buildTenantWalletLqw(bo);
        lqw.orderByDesc(NoDeptBaseEntity::getCreateTime);
        List<TenantWallet> records = TenantHelper.ignore(() -> iTenantWalletService.list(lqw));

        return convertWalletBalanceUsageVos(records);
    }

    @NotNull
    private static LambdaQueryWrapper<TenantWallet> buildTenantWalletLqw(WalletBalanceUsageQueryBo bo) {
        LambdaQueryWrapper<TenantWallet> lqw = Wrappers.lambdaQuery();

        String tenantId = bo.getTenantId();
        String currency = bo.getCurrency();
        List<String> paymentTimes = bo.getPaymentTimes();
        List<String> rechargeTimes = bo.getRechargeTimes();

        lqw.eq(StringUtils.isNotBlank(tenantId), TenantWallet::getTenantId, tenantId);
        lqw.eq(StringUtils.isNotBlank(currency), TenantWallet::getCurrency, currency);
        lqw.eq(Objects.nonNull(bo.getWalletState()), TenantWallet::getWalletState, bo.getWalletState());

        if (CollUtil.isNotEmpty(paymentTimes)) {
            String start = paymentTimes.get(0);
            String end = paymentTimes.get(1);

            lqw.exists("select 1 from transaction_record tr where tr.tenant_id = tenant_wallet.tenant_id " +
                "and tr.transaction_time BETWEEN {0} and {1} and transaction_state = 'Success' and tr.transaction_type = 'Expenditure'",
                start, end);
        }

        if (CollUtil.isNotEmpty(rechargeTimes)) {
            String start = rechargeTimes.get(0);
            String end = rechargeTimes.get(1);
            lqw.exists("select 1 from transaction_record tr where tr.tenant_id = tenant_wallet.tenant_id " +
                "and tr.transaction_time BETWEEN {0} and {1} and transaction_state = 'Success' and tr.transaction_type = 'Recharge'",
                start, end);
        }
        return lqw;
    }


    /**
     * 更新钱包状态
     *
     * @param bo
     * @return
     */
    @Override
    public boolean updateWalletState(WalletFreezeBo bo) {
        String tenantId = bo.getTenantId();
        Integer walletState = bo.getWalletState();

        TenantWallet tenantWallet = iTenantWalletService.queryByTenantId(tenantId,bo.getCurrency());
        if(tenantWallet == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.WALLET_NOT_FOUND);
        }

        WalletStateEnum walletStateEnum = WalletStateEnum.fromValue(walletState);

        TenantWallet updateWallet = new TenantWallet();
        updateWallet.setWalletState(walletStateEnum);
        updateWallet.setId(tenantWallet.getId());
        return iTenantWalletService.updateByEntity(updateWallet);
    }

    /**
     * 充值到钱包
     *
     * @param bo
     * @return
     */
    @Override
    public boolean rechargeToWallet(WalletRechargeBo bo) throws WalletException {
        BigDecimal amount = bo.getAmount();
        String tenantId = bo.getTenantId();
        String note = bo.getNote();

        TenantWallet tenantWallet = iTenantWalletService.queryByTenantId(tenantId,bo.getCurrency());
        if(tenantWallet == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.WALLET_NOT_FOUND);
        }
        if(amount == null || NumberUtil.equals(amount, BigDecimal.ZERO)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.AMOUNT_CANNOT_BE_ZERO);
        }

        String transactionNo = mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo);
        TransactionRecord transactions = new TransactionRecord();
        transactions.setTenantId(tenantId);
        transactions.setTransactionNo(transactionNo);
        transactions.setTransactionType(TransactionTypeEnum.Income);
        String transactionSubType = bo.getTransactionSubType();
        if(StringUtils.isNotEmpty(transactionSubType)){
            TransactionSubTypeEnum transactionSubTypeEnum = TransactionSubTypeEnum.valueOf(transactionSubType);
            if(null != transactionSubTypeEnum){
                List<TransactionSubTypeEnum> rechargeType = TransactionSubTypeEnum.getRechargeType();
                if(rechargeType.contains(transactionSubTypeEnum)){
                    transactions.setTransactionSubType(transactionSubTypeEnum);
                }
            }
        }else {
            // 交易子类型不可为空
            throw new RStatusCodeException(ZSMallStatusCodeEnum.TRANSACTION_SUB_TYPE_NOT_NULL);
        }
        transactions.setTransactionAmount(amount);
        transactions.setTransactionState(TransactionStateEnum.Processing);
        transactions.setTransactionNote(note);
        transactions.setAfterBalance(new BigDecimal("0.00"));
        transactions.setBeforeBalance(new BigDecimal("0.00"));
        transactions.setCurrency(bo.getCurrency());
        transactions.setCurrencySymbol(bo.getCurrencySymbol());

        try {
            // 处理钱包余额相关操作
            tenantWalletService.walletChanges(tenantId, transactions, true);
        } catch (WalletException e) {
            log.error("processResponseWithType => payoneer reconfirm WalletException. {}", e.getMessage(), e);
            // 更新交易记录
            insertOrUpdateRecordWithWalletException(transactions, e);
            throw e;
        } catch (Exception e) {
            log.error("processResponseWithType => payoneer reconfirm Exception. {}", e.getMessage(), e);
            // 更新交易记录
            insertOrUpdateRecordWithException(transactions, e);
        }
        // 交易记录附件处理
        List<TransactionRecordAttachmentBo> imageList = bo.getImageList();
        if(CollUtil.isNotEmpty(imageList)) {
            List<TransactionRecordAttachment> transactionRecordAttachmentList = new ArrayList<>();
            for (TransactionRecordAttachmentBo image : imageList) {
                TransactionRecordAttachment transactionRecordAttachment = new TransactionRecordAttachment();
                transactionRecordAttachment.setOssId(Long.valueOf(image.getOssId())).setTransactionNo(transactionNo).setAttachmentName(image.getAttachmentName())
                                           .setAttachmentOriginalName(image.getAttachmentName()).setAttachmentSuffix(FileUtil.getSuffix(image.getAttachmentName()))
                                           .setAttachmentSavePath(image.getAttachmentSavePath()).setAttachmentShowUrl(image.getAttachmentShowUrl())
                                           .setAttachmentType(AttachmentTypeEnum.Image.getValue()).setCreateTime(DateUtil.date());
                transactionRecordAttachment.setCreateBy(LoginHelper.getUserId());
                transactionRecordAttachmentList.add(transactionRecordAttachment);
            }
            if(CollUtil.isNotEmpty(transactionRecordAttachmentList)) {
                iTransactionRecordAttachmentService.insertBatch(transactionRecordAttachmentList);
            }
        }
        return true;
    }

    /**
     * 异常时，更新交易记录
     * @param transactionRecord
     * @param e
     */
    private void insertOrUpdateRecordWithWalletException(TransactionRecord transactionRecord, WalletException e) {
        transactionRecord.setTransactionState(TransactionStateEnum.Failure);
        transactionRecord.setFailureReason(e.getLocaleMessage().toJSON());
        iTransactionRecordService.insertOrUpdateByEntity(transactionRecord);
    }

    /**
     * 异常时，更新交易记录
     * @param transactionRecord
     * @param e
     */
    private void insertOrUpdateRecordWithException(TransactionRecord transactionRecord, Exception e) {
        transactionRecord.setTransactionState(TransactionStateEnum.Failure);
        JSONObject message = JSONUtil.createObj().putOnce("message", e.getMessage());
        transactionRecord.setFailureReason(message);
        iTransactionRecordService.insertOrUpdateByEntity(transactionRecord);
    }

    /**
     * 异常时，更新交易记录
     * @param transactionRecord
     * @param e
     */
    private void insertOrUpdateRecordWithStatusCodeException(TransactionRecord transactionRecord, RStatusCodeException e) {
        transactionRecord.setTransactionState(TransactionStateEnum.Failure);
        JSONObject message = JSONUtil.createObj().putOnce("message", MessageUtils.message(Locale.US, e.getStatusCode().getMessageCode()));
        transactionRecord.setFailureReason(message);
        iTransactionRecordService.insertOrUpdateByEntity(transactionRecord);
    }

    /**
     * 翻页查询交易记录
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<TransactionsVo> getFundsTransactionsPage(TransactionsQueryBo bo, PageQuery pageQuery) {
//        LambdaQueryWrapper<TransactionRecord> lqw = buildTransactionRecordLqw(bo);
//        lqw.orderByDesc(TransactionRecord::getId);
//        return baseMapper.queryPageList(bo, page);
//        Page<TransactionRecord> result = TenantHelper.ignore(() -> iTransactionRecordService.page(pageQuery.build(), lqw));
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<TransactionRecord> result = TenantHelper.ignore(() -> iTransactionRecordService.queryPage(bo,pageQuery));
        stopWatch.stop();
        log.info("交易列表翻页查询方法结束,耗时信息:{}", stopWatch.prettyPrint());
        long total = result.getTotal();
        List<TransactionRecord> records = result.getRecords();
        if (CollUtil.isEmpty(records)){
            return TableDataInfo.build(new ArrayList<>(), total);
        }
        Set<Long> transactionNoSet = records.stream().map(TransactionRecord::getId).collect(Collectors.toSet());
        // 查询订单
        List<Map<String, String>> channelName = TenantHelper.ignore(() -> iTransactionsOrdersService.getBaseMapper().getChannelName(transactionNoSet));
        // 创建一个Map来存储transaction_id和channel_name的映射关系
        Map<String, String> channelNameMap = channelName.stream().collect(Collectors.toMap(
            entry -> String.valueOf(entry.get("transactions_id")), // key映射函数
            entry -> entry.get("channel_name") // value映射函数
        ));
        records.forEach(transactions -> {
            transactions.setChannelName(channelNameMap.get(transactions.getId().toString()));
        });
        List<TransactionsVo> transactionsVos = convertTransactionsVos(records);
        return TableDataInfo.build(transactionsVos, total);
    }

    @NotNull
    private List<TransactionsVo> convertTransactionsVos(List<TransactionRecord> records) {
        List<TransactionsVo> transactionsVos = new ArrayList<>();
        Set<Long> collect = records.stream().map(TransactionRecord::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<TransactionReceipt> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CollUtil.isNotEmpty(collect),TransactionReceipt::getTransactionsId,collect);
        List<TransactionReceipt> transactionReceipts = TenantHelper.ignore(() -> transactionReceiptMapper.selectList(lambdaQueryWrapper));
        Map<Long, TransactionReceipt> transactionReceiptMap = transactionReceipts.stream()
                                                               .collect(Collectors.toMap(TransactionReceipt::getTransactionsId, transactionReceipt -> transactionReceipt));
            //查询tenant_site
            List<TenantSiteCountryCurrency> tenantSites = TenantHelper.ignore(() -> tenantSiteService.getBaseMapper()
                                                                                                     .selectTenantSiteJoinCountryCurrency());
        Map<String, TenantSiteCountryCurrency> tenantSitesMap = tenantSites.stream().collect(Collectors.toMap(
                tenantSite -> tenantSite.getTenantId() + "-" + tenantSite.getCurrencyCode(),
                tenantSite -> tenantSite,
                (existingValue, newValue) -> newValue
            ));


        Set<Long> transactionNoSet = records.stream().map(TransactionRecord::getId).collect(Collectors.toSet());
        //处理订单号
        List<TransactionsV2Vo> orderList=   transactionRecordMapper.getOrderList(transactionNoSet);
        List<TransactionsV2Vo> orderRefoundList=   transactionRecordMapper.getOrderRefoundList(transactionNoSet);
        orderList.addAll(orderRefoundList);
        Map<Long, TransactionsV2Vo> orderMap = orderList.stream().
                                                        collect(Collectors.toMap(TransactionsV2Vo::getId, transactions -> transactions));
        //查询所有的
        if(CollUtil.isNotEmpty(records)) {
            records.forEach(record -> {
                TransactionsVo transactionsVo = MapstructUtils.convert(record, TransactionsVo.class);
                if (transactionsVo != null) {
                    transactionsVo.setChannelName(record.getChannelName());
                }
                Long id = record.getId();
                TransactionTypeEnum transactionType = record.getTransactionType();
                String tenantId = record.getTenantId();

                String role = "Manager";
                if(StrUtil.startWith(tenantId, "D")) {
                    role = TenantType.Distributor.name();
                } else if(StrUtil.startWith(tenantId, "S")) {
                    role = TenantType.Supplier.name();
                }
                transactionsVo.setRole(role);

                setTransactionFromAndTo(transactionReceiptMap.get(id), transactionsVo, transactionType);

                // 这里暂不查询，自己从详情里面获取
//                TransactionBusinessRelationVo businessRelationVo = transactionRecordMapper.selectBusinessRelationVo(id);
//                if(businessRelationVo != null) {
//                    if(StrUtil.equalsIgnoreCase(businessRelationVo.getBusinessType(), "Bill")) {
//                        transactionsVo.setBillNo(businessRelationVo.getBusinessNo());
//                    }else if(StrUtil.equalsIgnoreCase(businessRelationVo.getBusinessType(), "Order")) {
//                        transactionsVo.setOrderNo(businessRelationVo.getBusinessNo());
//                    }
//                }
                TransactionsV2Vo transactionsV2Vo = orderMap.get(record.getId());
                if (ObjectUtil.isNotNull(transactionsV2Vo)){
                    TenantSiteCountryCurrency tenantSiteCountryCurrency = tenantSitesMap.get(transactionsV2Vo.getTenantId()+"-"+transactionsV2Vo.getCurrency());
                    if (ObjectUtil.isNotNull(tenantSiteCountryCurrency)){
                        transactionsVo.setErpChannelName(tenantSiteCountryCurrency.getChannelFlag());
                    }
                }else {
                    TenantSiteCountryCurrency tenantSiteCountryCurrency = tenantSitesMap.get(record.getTenantId()+"-"+record.getCurrency());
                    if (ObjectUtil.isNotNull(tenantSiteCountryCurrency)){
                        transactionsVo.setErpChannelName(tenantSiteCountryCurrency.getChannelFlag());
                    }
                }
                transactionsVos.add(transactionsVo);
            });
        }
        return transactionsVos;
    }
    @NotNull
    private List<TransactionsV2Vo> convertTransactionsRecordVos(List<TransactionRecordExportVo> records) {
        List<TransactionsV2Vo> transactionsVos = new ArrayList<>();
        Set<Long> collect = records.stream().map(TransactionRecordExportVo::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<TransactionReceipt> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CollUtil.isNotEmpty(collect),TransactionReceipt::getTransactionsId,collect);
        List<TransactionReceipt> transactionReceipts = TenantHelper.ignore(() -> transactionReceiptMapper.selectList(lambdaQueryWrapper));
        Map<Long, TransactionReceipt> transactionReceiptMap = transactionReceipts.stream()
                                                                                 .collect(Collectors.toMap(TransactionReceipt::getTransactionsId, transactionReceipt -> transactionReceipt));
        //查询所有的
        if(CollUtil.isNotEmpty(records)) {
            records.forEach(record -> {
                TransactionsV2Vo transactionsVo = MapstructUtils.convert(record, TransactionsV2Vo.class);
                if (transactionsVo != null) {
                    transactionsVo.setChannelName(record.getChannelName());
                }
                Long id = record.getId();
                TransactionTypeEnum transactionType = record.getTransactionType();
                String tenantId = record.getTenantId();

                String role = "Manager";
                if(StrUtil.startWith(tenantId, "D")) {
                    role = TenantType.Distributor.name();
                } else if(StrUtil.startWith(tenantId, "S")) {
                    role = TenantType.Supplier.name();
                }
                transactionsVo.setRole(role);

                setTransactionFromAndTo(transactionReceiptMap.get(id), transactionsVo, transactionType);

                transactionsVos.add(transactionsVo);
            });
        }
        return transactionsVos;
    }

    private void setTransactionFromAndTo(TransactionReceipt transactionReceipt, TransactionsV2Vo transactionsVo, TransactionTypeEnum transactionType) {
        String from = "";
        String to = "";
        if(ObjectUtil.equal(transactionType, TransactionTypeEnum.Recharge)) {
            if(transactionReceipt != null) {
                TransactionMethodEnum transactionMethod = transactionReceipt.getTransactionMethod();
                from = transactionMethod.getValue();
            }
            to = MallConstants.MALL_WALLET_NAME;
        } else if(ObjectUtil.equal(transactionType, TransactionTypeEnum.Withdrawal)) {
            from = MallConstants.MALL_SYSTEM_NAME;
            if(transactionReceipt != null) {
                TransactionMethodEnum transactionMethod = transactionReceipt.getTransactionMethod();
                to = transactionMethod.getValue();
            }
        } else if(ObjectUtil.equal(transactionType, TransactionTypeEnum.Income)) {
            from = MallConstants.MALL_SYSTEM_NAME;
            to = MallConstants.MALL_WALLET_NAME;
        } else if(ObjectUtil.equal(transactionType, TransactionTypeEnum.Expenditure)) {
            from = MallConstants.MALL_WALLET_NAME;
            to = MallConstants.MALL_SYSTEM_NAME;
        }
        transactionsVo.setFrom(from);
        transactionsVo.setTo(to);
    }

    private void setTransactionFromAndTo(Long transactionId, TransactionsVo transactionsVo, TransactionTypeEnum transactionType) {
        String from = "";
        String to = "";
        if(ObjectUtil.equal(transactionType, TransactionTypeEnum.Recharge)) {
            TransactionReceipt transactionReceipt = transactionReceiptMapper.selectByTransactionId(transactionId);
            if(transactionReceipt != null) {
                TransactionMethodEnum transactionMethod = transactionReceipt.getTransactionMethod();
                from = transactionMethod.getValue();
            }
            to = MallConstants.MALL_WALLET_NAME;
        } else if(ObjectUtil.equal(transactionType, TransactionTypeEnum.Withdrawal)) {
            from = MallConstants.MALL_SYSTEM_NAME;
            TransactionReceipt transactionReceipt = transactionReceiptMapper.selectByTransactionId(transactionId);
            if(transactionReceipt != null) {
                TransactionMethodEnum transactionMethod = transactionReceipt.getTransactionMethod();
                to = transactionMethod.getValue();
            }
        } else if(ObjectUtil.equal(transactionType, TransactionTypeEnum.Income)) {
            from = MallConstants.MALL_SYSTEM_NAME;
            to = MallConstants.MALL_WALLET_NAME;
        } else if(ObjectUtil.equal(transactionType, TransactionTypeEnum.Expenditure)) {
            from = MallConstants.MALL_WALLET_NAME;
            to = MallConstants.MALL_SYSTEM_NAME;
        }
        transactionsVo.setFrom(from);
        transactionsVo.setTo(to);
    }

    private void setTransactionFromAndTo(TransactionReceipt transactionReceipt, TransactionsVo transactionsVo, TransactionTypeEnum transactionType) {
        String from = "";
        String to = "";
        if(ObjectUtil.equal(transactionType, TransactionTypeEnum.Recharge)) {
            if(transactionReceipt != null) {
                TransactionMethodEnum transactionMethod = transactionReceipt.getTransactionMethod();
                from = transactionMethod.getValue();
            }
            to = MallConstants.MALL_WALLET_NAME;
        } else if(ObjectUtil.equal(transactionType, TransactionTypeEnum.Withdrawal)) {
            from = MallConstants.MALL_SYSTEM_NAME;
            if(transactionReceipt != null) {
                TransactionMethodEnum transactionMethod = transactionReceipt.getTransactionMethod();
                to = transactionMethod.getValue();
            }
        } else if(ObjectUtil.equal(transactionType, TransactionTypeEnum.Income)) {
            from = MallConstants.MALL_SYSTEM_NAME;
            to = MallConstants.MALL_WALLET_NAME;
        } else if(ObjectUtil.equal(transactionType, TransactionTypeEnum.Expenditure)) {
            from = MallConstants.MALL_WALLET_NAME;
            to = MallConstants.MALL_SYSTEM_NAME;
        }
        transactionsVo.setFrom(from);
        transactionsVo.setTo(to);
    }

    @NotNull
    private LambdaQueryWrapper<TransactionRecord> buildTransactionRecordLqw(TransactionsQueryBo bo) {
        LambdaQueryWrapper<TransactionRecord> lqw = Wrappers.lambdaQuery();

        String tenantId = bo.getTenantId();
        List<String> searchDates = bo.getSearchDates();
        String tenantType = bo.getTenantType();
        String billNo = bo.getBillNo();
        String orderNo = bo.getOrderNo();
        String orderRefundNo = bo.getOrderRefundNo();

        lqw.eq(StringUtils.isNotBlank(tenantId), TransactionRecord::getTenantId, tenantId);
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionNo()), TransactionRecord::getTransactionNo, bo.getTransactionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionType()), TransactionRecord::getTransactionType, bo.getTransactionType());
        lqw.in(CollUtil.isNotEmpty(bo.getTransactionSubTypes()), TransactionRecord::getTransactionSubType, bo.getTransactionSubTypes());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionState()), TransactionRecord::getTransactionState, bo.getTransactionState());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrency()), TransactionRecord::getCurrency, bo.getCurrency());

        if(CollUtil.isNotEmpty(searchDates)) {
            String start = searchDates.get(0);
            String end = searchDates.get(1);
            lqw.ge(TransactionRecord::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDate(start)))
                .le(TransactionRecord::getCreateTime, DateUtil.endOfDay(DateUtil.parseDate(end)));
        }

        lqw.exists(StringUtils.isNotBlank(tenantType),"select 1 from sys_tenant st where st.tenant_id = transaction_record.tenant_id " +
            "and st.tenant_type = {0} ", tenantType);

        lqw.exists(StringUtils.isNotBlank(billNo),"select 1 from transactions_bill tb where transaction_record.id = tb.transactions_id " +
            "and exists (select 1 from bill b where b.id = tb.bill_id and b.bill_no = {0}) ", billNo);

        if(StringUtils.isNotBlank(bo.getTransactionType()) && TransactionTypeEnum.Income.name().equals(bo.getTransactionType()) && StringUtils.isNotBlank(orderNo)){
            lqw.exists(StringUtils.isNotBlank(orderNo),"select 1 from transactions_order_refund tor where transaction_record.id = tor.transactions_id " +
                "and exists (select 1 from order_refund orr where orr.id = tor.order_refund_id and orr.order_no = {0})", orderNo);
        }else if((StringUtils.isNotBlank(orderNo))){
            lqw.apply("id IN (SELECT transactions_id FROM transactions_order_refund WHERE order_refund_id IN (SELECT id FROM order_refund WHERE order_no = {0} AND del_flag = '0') union SELECT transactions_id FROM transactions_orders WHERE order_id IN ( SELECT id FROM orders WHERE order_no = {0} AND del_flag = '0'))", orderNo);
        }

        lqw.exists(StringUtils.isNotBlank(orderRefundNo),"select 1 from transactions_order_refund tor where transaction_record.id = tor.transactions_id " +
            "and exists (select 1 from order_refund orr where orr.id = tor.order_refund_id and orr.order_refund_no = {0})", orderRefundNo);
        return lqw;
    }

    /**
     * 查询交易记录
     *
     * @param bo
     * @return
     */
    @Override
    public List<TransactionsV2Vo> queryFundsTransactions(TransactionsQueryBo bo) {
        return  new ArrayList<>();
    }
    @Override
    public List<TransactionsV2Vo> queryFundsTransactionsV2(TransactionsQueryBo bo) {

//        List<TransactionRecordExportVo> records = TenantHelper.ignore(iTransactionRecordService::selectListNotSysUserDel) ;
//        List<TransactionsV2Vo> transactionsVos=new ArrayList<>();
//        // 确定批处理大小
//        final int batchSize = 500;
//        // 创建一个用于存储所有批次的列表
//        List<List<TransactionRecordExportVo>> batches = new ArrayList<>();
//        // 将records分割成多个批次
//        for (int i = 0; i < records.size(); i += batchSize) {
//            int end = Math.min(i + batchSize, records.size());
//            batches.add(records.subList(i, end));
//        }
//        // 遍历每个批次，进行处理
//        for (List<TransactionRecordExportVo> batch : batches) {
//            // 筛选出订单号，关联查询店铺名称
//            Set<Long> transactionNoSet = batch.stream().map(TransactionRecordExportVo::getId).collect(Collectors.toSet());
//            // 查询订单
//            List<Map<String, String>> channelName = TenantHelper.ignore(() -> iTransactionsOrdersService.getBaseMapper().getChannelName(transactionNoSet));
//            // 创建一个Map来存储transaction_id和channel_name的映射关系
//            Map<String, String> channelNameMap = channelName.stream().collect(Collectors.toMap(
//                entry -> String.valueOf(entry.get("transactions_id")), // key映射函数
//                entry -> entry.get("channel_name") // value映射函数
//            ));
//            // 遍历批次中的交易记录，并设置对应的渠道名称
//            batch.forEach(transactionsVo -> {
//                transactionsVo.setChannelName(channelNameMap.get(transactionsVo.getId().toString()));
//            });
//            transactionsVos.addAll(convertTransactionsRecordVos(batch));
//        }
//        return transactionsVos;
        return null;
    }
    /**
     * 获取交易记录详情
     *
     * @param transactionNo
     * @return
     */
    @Override
    public TransactionsVo getTransactionDetail(String transactionNo) {
        TransactionRecord transactionRecord = iTransactionRecordService.findByTransactionNo(transactionNo);
        if(transactionRecord != null) {
            TransactionsVo transactionsVo = MapstructUtils.convert(transactionRecord, TransactionsVo.class);
//            TransactionsVo transactionsVo = BeanUtil.toBean(transactionRecord, TransactionsVo.class);

            Long id = transactionRecord.getId();
            TransactionTypeEnum transactionType  = transactionRecord.getTransactionType();
//            String transactionTypeString = transactionRecord.getTransactionType();
            String tenantId = transactionRecord.getTenantId();

//            TransactionTypeEnum transactionType = TransactionTypeEnum.valueOf(transactionTypeString);
            // 设置from/to
            setTransactionFromAndTo(id, transactionsVo, transactionType);

            // 获取不同维度关联的信息
            List<TransactionBusinessRelationVo> businessRelations = transactionRecordMapper.selectBusinessRelations(id);
            if(CollUtil.isNotEmpty(businessRelations)) {
                List<String> orders = new ArrayList<>();
                List<String> bills = new ArrayList<>();
                List<String> orderRefunds = new ArrayList<>();
                businessRelations.forEach(relation -> {
                    if(StrUtil.equalsIgnoreCase(relation.getBusinessType(), TransactionBusinessRelationEnum.Bill.name())) {
                        bills.add(relation.getBusinessNo());
                    }else if(StrUtil.equalsIgnoreCase(relation.getBusinessType(), TransactionBusinessRelationEnum.Order.name())) {
                        orders.add(relation.getBusinessNo());
                    }else if(StrUtil.equalsIgnoreCase(relation.getBusinessType(), TransactionBusinessRelationEnum.OrderRefund.name())) {
                        orderRefunds.add(relation.getBusinessNo());
                    }
                });
                transactionsVo.setOrderNos(orders);
                transactionsVo.setBillNos(bills);
                transactionsVo.setOrderRefundNos(orderRefunds);
            }

            return transactionsVo;
        }

        return null;
    }

    /**
     * 翻页查询扣款记录
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<DebitRecordVo> getDebitRecordsPage(DebitRecordQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TransactionRecord> lqw = buildDebitRecordLqw(bo);

        Page<TransactionRecord> result = TenantHelper.ignore(() -> iTransactionRecordService.page(pageQuery.build(), lqw));
        long total = result.getTotal();
        List<TransactionRecord> records = result.getRecords();

        List<DebitRecordVo> transactionsVos = convertDebitRecordVos(records);

        return TableDataInfo.build(transactionsVos, total);
    }

    @NotNull
    private LambdaQueryWrapper<TransactionRecord> buildDebitRecordLqw(DebitRecordQueryBo bo) {
        LambdaQueryWrapper<TransactionRecord> lqw = Wrappers.lambdaQuery();

        String tenantId = bo.getTenantId();
        List<String> searchDates = bo.getSearchDates();

        lqw.eq(StringUtils.isNotBlank(tenantId), TransactionRecord::getTenantId, tenantId);
        lqw.eq(StringUtils.isNotBlank(bo.getCurrency()), TransactionRecord::getCurrency, bo.getCurrency());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionNo()), TransactionRecord::getTransactionNo, bo.getTransactionNo());
        if(StringUtils.isNotBlank(bo.getTenantType())) {
            List<String> tenantIds = iSysTenantService.listTenantIdByTenantType(bo.getTenantType());
            if(CollUtil.isNotEmpty(tenantIds)) {
                lqw.in(TransactionRecord::getTenantId, tenantIds);
            }
        }
        lqw.eq(TransactionRecord::getTransactionType, TransactionTypeEnum.Expenditure);
        lqw.eq(TransactionRecord::getTransactionSubType, TransactionSubTypeEnum.PlatformDeduct);

        if(CollUtil.isNotEmpty(searchDates)) {
            String start = searchDates.get(0);
            String end = searchDates.get(1);
            lqw.ge(TransactionRecord::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDate(start)))
                .le(TransactionRecord::getCreateTime, DateUtil.endOfDay(DateUtil.parseDate(end)));
        }
        lqw.orderByDesc(NoDeptBaseEntity::getCreateTime);
        return lqw;
    }

    private List<DebitRecordVo> convertDebitRecordVos(List<TransactionRecord> records) {
        List<DebitRecordVo> recordVos = new ArrayList<>();
        if(CollUtil.isNotEmpty(records)) {
            records.forEach(record -> {
                String tenantId = record.getTenantId();
                Date createTime = record.getCreateTime();
                BigDecimal transactionAmount = record.getTransactionAmount();
                String transactionNote = record.getTransactionNote();

                String role = "Manager";
                if(StrUtil.startWith(tenantId, "D")) {
                    role = TenantType.Distributor.name();
                } else if(StrUtil.startWith(tenantId, "S")) {
                    role = TenantType.Supplier.name();
                }

                DebitRecordVo debitRecordVo = new DebitRecordVo();
                debitRecordVo.setTransactionNo(record.getTransactionNo());
                debitRecordVo.setRemarks(transactionNote);
                debitRecordVo.setDeductionAmount(DecimalUtil.bigDecimalToString(transactionAmount));
                debitRecordVo.setApplicationTime(createTime);
                debitRecordVo.setTenantId(tenantId);
                debitRecordVo.setTenantType(role);
                debitRecordVo.setOperatorId(record.getCreateBy());
                debitRecordVo.setCurrency(record.getCurrency());
                debitRecordVo.setCurrencySymbol(record.getCurrencySymbol());

                recordVos.add(debitRecordVo);
            });
        }
        return recordVos;
    }


    /**
     * 查询扣款记录
     *
     * @param bo
     * @return
     */
    @Override
    public List<DebitRecordVo> getDebitRecords(DebitRecordQueryBo bo) {
        LambdaQueryWrapper<TransactionRecord> lqw = buildDebitRecordLqw(bo);

        List<TransactionRecord> records = TenantHelper.ignore(() -> iTransactionRecordService.list(lqw));

        return convertDebitRecordVos(records);
    }

    /**
     * 扣款
     *
     * @param bo
     * @return
     */
    @Override
    public boolean debitOperation(TransactionDebitBo bo) throws WalletException {
        BigDecimal amount = bo.getAmount();
        String tenantId = bo.getTenantId();
        String note = bo.getNote();

        SysTenantVo sysTenantVo = SystemEventUtils.getTenant(tenantId);
        if(sysTenantVo == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.USER_NOT_SUP_OR_NOT_BULK);
        }
        if(StrUtil.equals(sysTenantVo.getTenantType(), TenantType.Distributor.name())) {
            TenantWallet tenantWallet = iTenantWalletService.queryByTenantId(tenantId,bo.getCurrency());
            if(tenantWallet == null) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.WALLET_NOT_FOUND);
            }
            WalletStateEnum walletState = tenantWallet.getWalletState();
            if(Objects.equals(walletState, WalletStateEnum.Freeze)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.WALLET_FREEZE);
            }
            if(amount == null || NumberUtil.equals(amount, BigDecimal.ZERO)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.AMOUNT_CANNOT_BE_ZERO);
            }
            if (NumberUtil.isLess(tenantWallet.getWalletBalance(), amount)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.BULK_WALLET_BALANCE_INSUFFICIENT);
            }
        } else if(StrUtil.equals(sysTenantVo.getTenantType(), TenantType.Supplier.name())) {
            // 暂无校验
        } else {
            // 管理员抛出异常
            throw new RStatusCodeException(ZSMallStatusCodeEnum.USER_NOT_SUP_OR_NOT_BULK);
        }

        String transactionNo = mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo);
        TransactionRecord transactions = new TransactionRecord();
        transactions.setTenantId(tenantId);
        transactions.setTransactionNo(transactionNo);
        transactions.setTransactionType(TransactionTypeEnum.Expenditure);
        transactions.setTransactionSubType(TransactionSubTypeEnum.PlatformDeduct);
        transactions.setTransactionAmount(amount);
        transactions.setTransactionState(TransactionStateEnum.Processing);
        transactions.setTransactionNote(note);
        transactions.setAfterBalance(new BigDecimal("0.00"));
        transactions.setBeforeBalance(new BigDecimal("0.00"));
        transactions.setCurrency(bo.getCurrency());
        transactions.setCurrencySymbol(bo.getCurrencySymbol());

        try {
            if(StrUtil.equals(sysTenantVo.getTenantType(), TenantType.Distributor.name())) {
                // 处理钱包余额相关操作
                tenantWalletService.walletChanges(tenantId, transactions, true);
            }

            if(StrUtil.equals(sysTenantVo.getTenantType(), TenantType.Supplier.name())) {
                transactions.setTransactionState(TransactionStateEnum.Success);
                transactionRecordMapper.insert(transactions);
                // 处理账单相关操作
                billSupport.generateBillDTOByRechargeOrDeduct(LoginHelper.getTenantId(), transactions);
            }

        } catch (RStatusCodeException e) {
            log.error("debitOperation => RStatusCodeException. {}", e.getStatusCode(), e);
            // 更新交易记录
            insertOrUpdateRecordWithStatusCodeException(transactions, e);
            throw e;
        }  catch (WalletException e) {
            log.error("debitOperation => WalletException. {}", e.getMessage(), e);
            // 更新交易记录
            insertOrUpdateRecordWithWalletException(transactions, e);
            throw e;
        } catch (Exception e) {
            log.error("debitOperation => Exception. {}", e.getMessage(), e);
            // 更新交易记录
            insertOrUpdateRecordWithException(transactions, e);
        }

        return true;
    }

    @Override
    public void exportFundsTransactions(TransactionsQueryBo bo, HttpServletResponse response) {
        Locale headerLocale = ServletUtils.getHeaderLocale();
        String fileName = StrUtil.format(FileNameConstants.TRANSACTIONS_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.TransactionsExport, tempFileSavePath -> {
            ArrayList<TransactionsV2Vo> list = new ArrayList<>();
            try {
                if (CollUtil.isNotEmpty(bo.getSearchDates())){
                    bo.setCreateTimeStart(bo.getSearchDates().get(0));
                    bo.setCreateTimeEnd(bo.getSearchDates().get(1));
                }
                File tempFile = new File(tempFileSavePath);
                BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                Integer rowCount = TenantHelper.ignore(() -> iTransactionRecordService.selectListNotSysUserCount(bo));
                if (rowCount != 0) {
                    // 一次查询的数据量
                    int pageSize = 5000;
                    // 总页数
                    int totalPage = rowCount % pageSize == 0 ? rowCount / pageSize : (rowCount / pageSize + 1);
                    ExcelWriter excelWriter = EasyExcel.write(outputStream, OrderExportListDTO.class).build();
                    for (int currentPage = 1; currentPage <= totalPage; currentPage++) {
                        // 计算当前页的起始行
                        int startRow = (currentPage - 1) * pageSize;
                        // 如果是最后一页且不足pageSize条数据，调整pageSize为剩余的数据量
                        if (currentPage == totalPage && rowCount % pageSize != 0) {
                            pageSize = rowCount % pageSize;
                        }
                        int finalPageSize = pageSize;
                        try {
                            writeFundsTransactionsExcel(startRow, finalPageSize, currentPage, excelWriter, bo,null);
                        }catch (Exception e){
                            log.error(StrUtil.format(StrUtil.format("[交易列表导出],处理数据异常，原因：", e)));
                            throw new RuntimeException(e);
                        }
                    }
                    excelWriter.finish();
                } else {
                    ExcelUtil.exportExcelWithLocale(list, "交易列表导出", TransactionsV2Vo.class, false, outputStream, headerLocale);
                }
                IoUtil.close(outputStream);
                return tempFile;
            }catch (Exception e){
                log.error(StrUtil.format(StrUtil.format("[交易列表导出],处理数据异常，原因：", e)));
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public void export(DebitRecordQueryBo bo) {
        Locale headerLocale = ServletUtils.getHeaderLocale();
        String fileName = StrUtil.format(FileNameConstants.TRANSACTION_RECEIPT_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordUtil.generate(fileName,DownloadTypePlusEnum.TransactionsReceiptExport, tempFileSavePath -> {
            List<DebitRecordVo> debitRecordVoList = getDebitRecords(bo);
            for (DebitRecordVo debitRecordVo : debitRecordVoList) {
                debitRecordVo.setDeductionAmount(debitRecordVo.getCurrencySymbol()+debitRecordVo.getDeductionAmount());
            }
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            com.hengjian.common.excel.utils.ExcelUtil.exportExcelWithLocale(debitRecordVoList, "扣款记录", DebitRecordVo.class, false, outputStream, headerLocale);
            IoUtil.close(outputStream);
            return tempFile;
        });
    }


    private void writeFundsTransactionsExcel(int page, int pageSize, int currentPage, ExcelWriter excelWriter,
                                             TransactionsQueryBo bo, String temporaryTableName) {
        try {
            List<TransactionRecord> records = TenantHelper.ignore(() -> iTransactionRecordService.selectListNotSysUserDel(bo, page, pageSize));
            if (CollUtil.isEmpty(records)){
                return;
            }
            List<TransactionsVo> transactionsVos = new ArrayList<>();

            Set<Long> collect = records.stream().map(TransactionRecord::getId).collect(Collectors.toSet());
            LambdaQueryWrapper<TransactionReceipt> lambdaQueryWrapper=new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(CollUtil.isNotEmpty(collect),TransactionReceipt::getTransactionsId,collect);
            List<TransactionReceipt> transactionReceipts = TenantHelper.ignore(() -> transactionReceiptMapper.selectList(lambdaQueryWrapper));
            Map<Long, TransactionReceipt> transactionReceiptMap = transactionReceipts.stream()
                                                                                     .collect(Collectors.toMap(TransactionReceipt::getTransactionsId, transactionReceipt -> transactionReceipt));
            //查询tenant_site
            List<TenantSiteCountryCurrency> tenantSites = TenantHelper.ignore(() -> tenantSiteService.getBaseMapper()
                                                                                                     .selectTenantSiteJoinCountryCurrency());
            Map<String, TenantSiteCountryCurrency> tenantSitesMap = tenantSites.stream().collect(Collectors.toMap(
                tenantSite -> tenantSite.getTenantId() + "-" + tenantSite.getCurrencyCode(),
                tenantSite -> tenantSite,
                (existingValue, newValue) -> newValue
            ));
            List<TransactionRecordAttachment> t=new ArrayList<>();
            if (CollUtil.isNotEmpty(collect)){
                //查询transaction_receipt_attachment
                t=TenantHelper.ignore(() -> iTransactionRecordAttachmentService.getBaseMapper().getByTransactionRecordId(collect));
                List<TransactionRecordAttachment> ts=TenantHelper.ignore(() -> iTransactionRecordAttachmentService.getBaseMapper().getAttachmentByTransactionRecordId(collect));
                t.addAll(ts);
            }
            // 转换为Map, key是id, value是attachmentShowUrl，相同id的attachmentShowUrl用逗号拼接
            Map<Long, String> attachmentUrlMap = t.stream()
                .collect(Collectors.groupingBy(
                    TransactionRecordAttachment::getOssId,
                    Collectors.mapping(
                        TransactionRecordAttachment::getAttachmentShowUrl,
                        Collectors.joining(",")
                    )
                ));

            //查询所有的
            records.forEach(record -> {
                TransactionsVo transactionsVo = MapstructUtils.convert(record, TransactionsVo.class);
                if (transactionsVo != null) {
                    transactionsVo.setChannelName(record.getChannelName());
                }
                Long id = record.getId();
                TransactionTypeEnum transactionType = record.getTransactionType();
                String tenantId = record.getTenantId();

                String role = "Manager";
                if(StrUtil.startWith(tenantId, "D")) {
                    role = TenantType.Distributor.name();
                } else if(StrUtil.startWith(tenantId, "S")) {
                    role = TenantType.Supplier.name();
                }
                transactionsVo.setRole(role);
                setTransactionFromAndTo(transactionReceiptMap.get(id), transactionsVo, transactionType);
                transactionsVos.add(transactionsVo);
            });

            List<TransactionsV2Vo> transactionsV2Vos = BeanUtil.copyToList(transactionsVos, TransactionsV2Vo.class);

            if (CollUtil.isNotEmpty(transactionsV2Vos)){
                //处理渠道名称
                Set<Long> transactionNoSet = transactionsV2Vos.stream().map(TransactionsV2Vo::getId).collect(Collectors.toSet());
                List<Map<String, String>> channelName = TenantHelper.ignore(() -> iTransactionsOrdersService.getBaseMapper().getChannelName(transactionNoSet));
                // 创建一个Map来存储transaction_id和channel_name的映射关系
                Map<String, String> channelNameMap = channelName.stream().collect(Collectors.toMap(
                    entry -> String.valueOf(entry.get("transactions_id")), // key映射函数
                    entry -> entry.get("channel_name") // value映射函数
                ));

                //处理操作人
                LambdaQueryWrapper<SysUser> ss = new LambdaQueryWrapper<>();
                List<SysUserVo> sysUserVos =TenantHelper.ignore(()->sysUserMapper.selectVoList(ss));
                Map<Long, SysUserVo> sysUserVoMap = sysUserVos.stream()
                                                              .collect(Collectors.toMap(
                                                                  SysUserVo::getUserId, // key是SysUserVo的id
                                                                  sysUserVo -> sysUserVo, // value是SysUserVo对象本身
                                                                  (existing, replacement) -> existing // 在遇到重复的id时保留现有的值
                                                              ));
                //处理订单号
                List<TransactionsV2Vo> orderList=   transactionRecordMapper.getOrderList(transactionNoSet);
                List<TransactionsV2Vo> orderRefoundList=   transactionRecordMapper.getOrderRefoundList(transactionNoSet);
                orderList.addAll(orderRefoundList);
                Map<Long, TransactionsV2Vo> orderMap = orderList.stream().
                                                                collect(Collectors.toMap(TransactionsV2Vo::getId, transactions -> transactions));
                if (CollUtil.isNotEmpty(transactionsV2Vos)) {
                    for (TransactionsV2Vo v2Vo : transactionsV2Vos) {
                        v2Vo.setChannelName(channelNameMap.get(v2Vo.getId().toString()));
                        SysUserVo sysUserVo = sysUserVoMap.get(v2Vo.getCreateBy());
                        if (ObjectUtil.isNotNull(sysUserVo)){
                            v2Vo.setCreateByName(sysUserVo.getNickName());
                        }
                        TransactionsV2Vo transactionsV2Vo = orderMap.get(v2Vo.getId());
                        if (ObjectUtil.isNotNull(transactionsV2Vo)){
                            v2Vo.setOrderNo(transactionsV2Vo.getOrderNo());
                            TenantSiteCountryCurrency tenantSiteCountryCurrency = tenantSitesMap.get(transactionsV2Vo.getTenantId()+"-"+transactionsV2Vo.getCurrency());
                            if (ObjectUtil.isNotNull(tenantSiteCountryCurrency)){
                                v2Vo.setErpChannelName(tenantSiteCountryCurrency.getChannelFlag());
                            }
                        }else {
                            TenantSiteCountryCurrency tenantSiteCountryCurrency = tenantSitesMap.get(v2Vo.getTenantId()+"-"+v2Vo.getCurrency());
                            if (ObjectUtil.isNotNull(tenantSiteCountryCurrency)){
                                v2Vo.setErpChannelName(tenantSiteCountryCurrency.getChannelFlag());
                            }
                        }
                        v2Vo.setCreateTimeString(DateUtil.formatDateTime(v2Vo.getCreateTime()));
                        // 设置金额币种符号
                        v2Vo.setTransactionAmountString(v2Vo.getCurrencySymbol()+ " " + v2Vo.getTransactionAmount());
                        v2Vo.setAfterBalanceString(v2Vo.getCurrencySymbol()+ " " + v2Vo.getAfterBalance());

                        String s = attachmentUrlMap.get(v2Vo.getId());
                        if (StrUtil.isNotEmpty(s)){
                            v2Vo.setAttachmentShowUrl(s);
                        }
                    }
                }
            }
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "交易列表导出")
                                             // 这里放入动态头
                                             .head(TransactionsV2Vo.class)
                                             //传入样式
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             .registerConverter(new ExcelBigNumberConvert())
                                             // 当然这里数据也可以用 List<List<String>> 去传入
                                             .build();
            excelWriter.write(transactionsV2Vos, writeSheet);

        }catch (Exception e){
            log.error(e.getMessage());
        }

    }
}
