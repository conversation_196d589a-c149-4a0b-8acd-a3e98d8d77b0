package com.zsmall.system.biz.listener;

import cn.hutool.core.util.ObjectUtil;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.rabbitmq.client.Channel;
import com.zsmall.system.biz.service.IAnnouncementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 公告过期消息消费者
 * 监听公告过期消息，基于 RabbitMQ 延迟消息插件实现
 *
 * <AUTHOR> Assistant
 * @date 2024-12-27
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AnnouncementExpireConsumer {

    private final IAnnouncementService announcementService;

    /**
     * 监听公告过期消息
     * 当延迟时间到达时，消息会被投递到这个队列
     *
     * @param message 消息对象
     * @param channel RabbitMQ通道
     * @param deliveryTag 消息投递标签
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.ANNOUNCEMENT_EXPIRE_PROCESS_QUEUE)
    public void handleAnnouncementExpire(Message message,
                                       @Header(AmqpHeaders.CHANNEL) Channel channel,
                                       @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        Long announcementId = null;
        try {
            // 获取消息内容（直接是公告ID字符串）
            String messageBody = new String(message.getBody());
            log.info("收到公告过期消息: {}", messageBody);

            if (ObjectUtil.isEmpty(messageBody)) {
                throw new RuntimeException("消息内容为空");
            }

            announcementId = Long.valueOf(messageBody);

            // 处理公告过期（包含过期时间验证）
            announcementService.handleAnnouncementExpire(announcementId);

            log.info("公告过期处理完成: {}", announcementId);
        } catch (Exception e) {
            log.error("处理公告过期消息失败: {}", announcementId, e);
        } finally {
            try {
                // 手动确认消息
                channel.basicAck(deliveryTag, false);
            } catch (IOException e) {
                log.error("确认消息失败", e);
            }
        }
    }

}
