package com.zsmall.system.biz.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.system.biz.service.ITenantDistrExtendService;
import com.zsmall.system.entity.domain.TenantDistrExtendLog;
import com.zsmall.system.entity.domain.bo.settleInBasic.ReviewRecordBo;
import com.zsmall.system.entity.iservice.TenantDistrExtendLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/8/5 16:33
 */
@Slf4j
@Service
public class TenantDistrExtendJob {

    @Resource
    private ITenantDistrExtendService tenantDistrExtendService;

    @Resource
    private TenantDistrExtendLogService tenantDistrExtendLogService;

    @XxlJob("pushCompanyToErpHandler")
    public void pushCompanyToErpHandler() throws Exception {
        // 遍历log
        LambdaQueryWrapper<TenantDistrExtendLog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TenantDistrExtendLog::getPushResult,Boolean.FALSE);
        lambdaQueryWrapper.eq(TenantDistrExtendLog::getDelFlag,0);
        List<TenantDistrExtendLog> list = tenantDistrExtendLogService.list(lambdaQueryWrapper);
        for (TenantDistrExtendLog tenantDistrExtendLog : list) {
            String tenantId = tenantDistrExtendLog.getTenantId();
            ReviewRecordBo reviewRecordBo = new ReviewRecordBo();
            reviewRecordBo.setTenantId(tenantId);
            TenantHelper.ignore(()->tenantDistrExtendService.pushCompanyToErp(R.ok(),reviewRecordBo,Boolean.TRUE));
            // 更新成功后将重复的删除
        }

    }
}
