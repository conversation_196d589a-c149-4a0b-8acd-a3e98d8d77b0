package com.zsmall.system.biz.service;

/**
 * 账单明细修补表服务接口
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
public interface IBillDetailsRepairService {

    /**
     * 修补账单数据
     * @param billNo 账单编号（单个账单编号）
     * @param orderNo 订单编号（多个订单号用逗号拼接）
     */
    void repairBillData(String billNo, String orderNo);

    /**
     * 重新推送差值数据到ERP
     * @param billNo 账单编号
     */
    void resendDifferenceToErp(String billNo);

    /**
     * 推送修补表中未推送的全量数据到ERP（定时任务使用）
     */
    void pushUnsentRepairDataToErp();

}
