package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 告警租户表对象 warning_message_tenant
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "warning_message_tenant", autoResultMap = true)
public class WarningMessageTenant extends NoDeptBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 预警id
     */
    private Long warningMessageId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 是否已读0未读 1已读
     */
    private Integer isRead;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;

}
