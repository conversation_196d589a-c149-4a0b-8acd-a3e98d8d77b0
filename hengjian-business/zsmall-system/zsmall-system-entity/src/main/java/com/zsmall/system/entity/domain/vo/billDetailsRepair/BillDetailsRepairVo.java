package com.zsmall.system.entity.domain.vo.billDetailsRepair;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.system.entity.domain.BillDetailsRepair;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单明细修补表视图对象 BillDetailsRepairVo
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BillDetailsRepair.class)
public class BillDetailsRepairVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 账单id
     */
    @ExcelProperty(value = "账单id")
    private Long billId;

    /**
     * 账单编号
     */
    @ExcelProperty(value = "账单编号")
    private String billNo;

    /**
     * 渠道订单号
     */
    @ExcelProperty(value = "渠道订单号")
    private String channelOrderNo;

    /**
     * 分销内部订单编号
     */
    @ExcelProperty(value = "分销内部订单编号")
    private String orderNo;

    /**
     * 外部订单号
     */
    @ExcelProperty(value = "外部订单号")
    private String orderExtendId;

    /**
     * 分销商租户ID
     */
    @ExcelProperty(value = "分销商租户ID")
    private String tenantId;

    /**
     * 供应商租户id
     */
    @ExcelProperty(value = "供应商租户id")
    private String supperTenantId;

    /**
     * 退款单编号
     */
    @ExcelProperty(value = "退款单编号")
    private String orderRefundNo;

    /**
     * 下单时间
     */
    @ExcelProperty(value = "下单时间")
    private Date orderTime;

    /**
     * 订单状态 1发货单 2退款单
     */
    @ExcelProperty(value = "订单状态")
    private Integer orderStatus;

    /**
     * 支付方式 1钱包 2派安盈
     */
    @ExcelProperty(value = "支付方式")
    private String orderPaymentMethods;

    /**
     * 商品ItemNo
     */
    @ExcelProperty(value = "商品ItemNo")
    private String productSkuCode;

    /**
     * 商品数量
     */
    @ExcelProperty(value = "商品数量")
    private Integer productQuantity;

    /**
     * 操作费
     */
    @ExcelProperty(value = "操作费")
    private BigDecimal operationFee;

    /**
     * 尾程派送费
     */
    @ExcelProperty(value = "尾程派送费")
    private BigDecimal finalDeliveryFee;

    /**
     * 产品小计
     */
    @ExcelProperty(value = "产品小计")
    private BigDecimal productSkuPrice;

    /**
     * 订单金额
     */
    @ExcelProperty(value = "订单金额")
    private BigDecimal orderTotalAmount;

    /**
     * 订单退款金额
     */
    @ExcelProperty(value = "订单退款金额")
    private BigDecimal orderRefundTotalAmount;

    /**
     * 商品单价
     */
    @ExcelProperty(value = "商品单价")
    private BigDecimal unitPrice;

    /**
     * 站点国家
     */
    @ExcelProperty(value = "站点国家")
    private String countryCode;

    /**
     * 币种
     */
    @ExcelProperty(value = "币种")
    private String currencyCode;

    /**
     * 币种符号
     */
    @ExcelProperty(value = "币种符号")
    private String currencySymbol;

    /**
     * 发货方式
     */
    @ExcelProperty(value = "发货方式")
    private String supportedLogistics;

}
