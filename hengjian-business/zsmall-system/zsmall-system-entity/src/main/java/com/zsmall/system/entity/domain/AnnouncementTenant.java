package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 公告已读表对象 announcement_read
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
@TableName(value = "announcement_tenant", autoResultMap = true)
public class AnnouncementTenant implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 公告id
     */
    private Long announcementId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 是否已读 0未读 1已读
     */
    private Integer isRead;

    /**
     * 是否弹窗(只弹一次) 0未弹窗 1已弹窗
     */
    private Integer isShowWindow;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Integer delFlag;

}
