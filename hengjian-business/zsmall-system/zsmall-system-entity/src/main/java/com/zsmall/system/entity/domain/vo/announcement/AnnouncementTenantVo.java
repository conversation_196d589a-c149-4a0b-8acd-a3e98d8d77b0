package com.zsmall.system.entity.domain.vo.announcement;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.system.entity.domain.AnnouncementTenant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;

/**
 * 公告已读视图对象 announcement_read
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AnnouncementTenant.class)
public class AnnouncementTenantVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 公告id
     */
    @ExcelProperty(value = "公告ID")
    private Long announcementId;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 是否已读 0未读 1已读
     */
    @ExcelProperty(value = "是否已读")
    private Integer isRead;

    /**
     * 是否弹窗(只弹一次) 0未弹窗 1已弹窗
     */
    @ExcelProperty(value = "是否弹窗")
    private Integer isShowWindow;

}
