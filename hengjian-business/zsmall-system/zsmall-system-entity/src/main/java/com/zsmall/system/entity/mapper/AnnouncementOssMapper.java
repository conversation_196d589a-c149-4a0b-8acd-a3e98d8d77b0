package com.zsmall.system.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.AnnouncementOss;
import com.zsmall.system.entity.domain.vo.announcement.AnnouncementOssVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公告附件Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
public interface AnnouncementOssMapper extends BaseMapperPlus<AnnouncementOss, AnnouncementOssVo> {

    /**
     * 根据公告ID查询附件列表
     * @param announcementId 公告ID
     * @return 附件列表
     */
    List<AnnouncementOss> selectByAnnouncementId(@Param("announcementId") Long announcementId);

    /**
     * 根据公告ID删除附件关联（逻辑删除）
     * @param announcementId 公告ID
     * @return 影响行数
     */
    int deleteByAnnouncementId(@Param("announcementId") Long announcementId);

    /**
     * 批量插入附件关联
     * @param announcementOssList 附件关联列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<AnnouncementOss> announcementOssList);

}
