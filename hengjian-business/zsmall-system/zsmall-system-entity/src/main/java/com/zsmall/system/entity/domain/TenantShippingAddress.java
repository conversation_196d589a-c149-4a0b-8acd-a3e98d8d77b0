package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 用户收货地址对象 tenant_shipping_address
 *
 * <AUTHOR>
 * @date 2023-08-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenant_shipping_address")
public class TenantShippingAddress extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 手机号码国际区号
     */
    private String areaCode;

    /**
     * 默认状态 1默认地址 0非默认地址
     */
    private Integer defaultState;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 姓名
     */
    private String fullName;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 收货地址
     */
    private String shippingAddress;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 省/州
     */
    private String state;

    /**
     * 省/州代码
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String stateCode;

    /**
     * 城市
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String city;

    /**
     * 详细地址1
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String address1;

    /**
     * 详细地址2
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String address2;

    /**
     * 详细地址3
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String address3;

    /**
     * zipCode
     */
    private String zipCode;

    /**
     * 是否使用当前手机号
     */
    private Long isSameMember;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
