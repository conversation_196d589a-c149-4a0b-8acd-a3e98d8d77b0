package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;



/**
 * 账单明细修补表对象 bill_details_repair
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bill_details_repair")
public class BillDetailsRepair extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 账单id
     */
    private Long billId;
    /**
     * 账单编号
     */
    private String billNo;

    /**
     * 渠道订单号
     */
    private String channelOrderNo;

    /**
     * 分销内部订单编号
     */
    private String orderNo;
    /**
     * 外部订单号
     */
    private String orderExtendId;
    /**
     * 分销商租户ID
     */

    private String tenantId;

    /**
     * 供应商租户id
     */
    private String supperTenantId;

    /**
     * 退款单编号
     */
    private String orderRefundNo;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 订单状态 1发货单 2退款单
     */
    private Integer orderStatus;

    /**
     * 支付方式 1钱包 2派安盈
     */
    private String orderPaymentMethods;

    /**
     * 商品ItemNo
     */
    private String productSkuCode;

    /**
     * 商品数量
     */
    private Integer productQuantity;

    /**
     * 操作费
     */
    private BigDecimal operationFee;

    /**
     * 尾程派送费
     */
    private BigDecimal finalDeliveryFee;

    /**
     * 产品小计
     */
    private BigDecimal productSkuPrice;

    /**
     * 订单金额
     */
    private BigDecimal orderTotalAmount;

    /**
     * 订单退款金额
     */
    private BigDecimal orderRefundTotalAmount;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;
    /**
     * 商品单价
     */
    private BigDecimal unitPrice;

    /**
     * 站点国家
     */
    private String countryCode;
    /**
     * 币种
     */
    private String currencyCode;
    /**
     * 币种符号
     */
    private String currencySymbol;
    /**
     * 发货方式
     */
    private String supportedLogistics;

    /**
     * 账单是否推送ERP 0未推送/1已推送
     */
    private Integer isSendErp;

    /**
     * 账单推送ERP时间
     */
    private Date sendErpTime;


    public BigDecimal getOperationFee() {
        return (operationFee != null) ? operationFee : BigDecimal.ZERO;
    }

    public BigDecimal getFinalDeliveryFee() {
        return (finalDeliveryFee != null) ? finalDeliveryFee : BigDecimal.ZERO;
    }

    public BigDecimal getProductSkuPrice() {
        return (productSkuPrice != null) ? productSkuPrice : BigDecimal.ZERO;
    }

    public BigDecimal getOrderTotalAmount() {
        return (orderTotalAmount != null) ? orderTotalAmount : BigDecimal.ZERO;
    }

    public BigDecimal getOrderRefundTotalAmount() {
        return (orderRefundTotalAmount != null) ? orderRefundTotalAmount : BigDecimal.ZERO;
    }

}
