package com.zsmall.system.entity.domain.vo.transaction;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.system.entity.domain.TransactionRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 交易记录视图对象 transaction_record
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TransactionRecord.class)
public class TransactionRecordVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
//    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 交易记录编号
     */
    @ExcelProperty(value = "交易单号")
    @ExcelI18nFormat(code = "zsmall.excel.transactionNo")
    private String transactionNo;
    @ExcelProperty(value = "订单号")
    private String orderNo;
    /**
     * 交易主类型（本平台与外部平台资金流动：Recharge-充值，Withdrawal-提现；本平台内部资金流动：Income-收入，Expenditure-支出）
     */
    @ExcelProperty(value = "交易类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "Recharge=充值,Withdrawal=提现,Income=收入,Expenditure=支出")
    @ExcelI18nFormat(code = "zsmall.excel.transactionType")
    private String transactionType;

    /**
     * 交易子类型（记录交易具体的业务类型，如主类型为支出，子类型可以为订单支付、仓储费支付、订金支付等）
     */
    @ExcelProperty(value = "交易子类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "OrderPay=订单支付,OrderRefund=订单退款,PlatformRemit=平台汇款,PlatformDeduct=平台扣除,WholesaleBalance=国外现货批发尾款,WholesaleDepositRefund=国外现货批发订金退还,WholesaleDeposit=国外现货批发订金,StockLockDeposit=锁货押金,BuyoutDeposit=圈货订金,StockLockBuyoutFee=锁货买断费,ProductActivityRefund=商品活动退还,ProductActivityStorageFee=仓储费,ProductActivityDeduction=商品活动扣除")
    @ExcelI18nFormat(code = "zsmall.excel.transactionSubType")
    private String transactionSubType;

//    @ExcelProperty(value = "下单时间")
//    @ExcelI18nFormat(code = "zsmall.excel.orderTime")
    private Date createTime;

    private Date updateTime;

    @ExcelProperty(value = "交易时间")
    @ExcelI18nFormat(code = "zsmall.excel.transactionTime")
    private Date transactionTime;
    /**
     * 币种
     */
    private String currencyCode;
    /**
     * 币种 导出用
     */
    @ExcelProperty(value = "币种")
    private String currency;
    /**
     * 交易前余额
     */
    @ExcelProperty(value = "交易前余额")
    @ExcelI18nFormat(code = "zsmall.excel.beforeBalance")
    private BigDecimal beforeBalance;

    /**
     * 交易金额
     */
    @ExcelProperty(value = "交易金额")
    @ExcelI18nFormat(code = "zsmall.excel.transactionAmount")
    private BigDecimal transactionAmount;

    /**
     * 交易后余额
     */
    @ExcelProperty(value = "交易后余额")
    @ExcelI18nFormat(code = "zsmall.excel.afterBalance")
    private BigDecimal afterBalance;

    /**
     * 交易备注
     */
    @ExcelProperty(value = "交易备注")
    @ExcelI18nFormat(code = "zsmall.excel.transactionNote")
    private String transactionNote;

    /**
     * 交易状态
     */
    @ExcelProperty(value = "交易状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "Processing=处理中,Success=交易成功,Failure=交易失败,Canceled=已取消")
    @ExcelI18nFormat(code = "zsmall.excel.transactionState")
    private String transactionState;

    private String orderTime;

    /**
     * 币种符号
     */
    private String currencySymbol;

}
