package com.zsmall.system.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo;

import java.util.List;

/**
 * 站点国家币种信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface SiteCountryCurrencyMapper extends BaseMapperPlus<SiteCountryCurrency, SiteCountryCurrencyVo> {

    /**
     * 获取币种列表
     * @return
     */
    List<SiteCountryCurrencyVo> getCurrencyList();

    /**
     * 获取站点币种列表
     * @return
     */
    List<SiteCountryCurrencyVo> getSiteCurrencyList();

    /**
     * 获取站点币种列表
     * @return
     */
    String getCurrencySymbolByCurrencyCode(String currencyCode);

    SiteCountryCurrencyVo queryByCountryCode(String countryCode);
}
