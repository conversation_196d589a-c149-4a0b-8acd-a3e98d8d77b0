package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.Announcement;
import com.zsmall.system.entity.domain.bo.announcement.AnnouncementBo;
import com.zsmall.system.entity.domain.vo.announcement.AnnouncementMessageVo;
import com.zsmall.system.entity.domain.vo.announcement.AnnouncementVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 公告Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
public interface AnnouncementMapper extends BaseMapperPlus<Announcement, AnnouncementVo> {

    /**
     * 分页查询公告列表（通用接口）
     * @param page 分页参数
     * @param bo 查询条件
     * @param tenantId 租户ID（可选，用于分销商/供应商查询）
     * @return 分页结果
     */
    IPage<AnnouncementVo> queryPageList(Page<AnnouncementVo> page, @Param("bo") AnnouncementBo bo, @Param("tenantId") String tenantId);

    /**
     * 查询公告详情（包含附件信息）
     * @param id 公告ID
     * @return 公告详情
     */
    AnnouncementVo selectAnnouncementWithAttachments(@Param("id") Long id);

    List<AnnouncementVo> selectAnnouncementWithAttachmentList(@Param("id") Set<Long> id);


    /**
     * 查询用户端消息列表（分页）
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param tenantType 租户类型
     * @param isRead 是否已读（可选）
     * @return 消息列表
     */
    IPage<AnnouncementMessageVo> queryMessageList(Page<AnnouncementMessageVo> page,
                                                   @Param("tenantId") String tenantId,
                                                   @Param("tenantType") String tenantType,
                                                   @Param("isRead") Integer isRead);



    /**
     * 查询有效的公告列表（未过期、已启用）
     * @param tenantType 租户类型
     * @return 有效公告列表
     */
    List<Announcement> queryValidAnnouncements(@Param("tenantType") String tenantType);

    /**
     * 查询弹窗公告列表
     * @param tenantId 租户ID
     * @return 弹窗公告列表
     */
    List<AnnouncementVo> popWindowSearch(String tenantId);

    /**
     * 批量更新弹窗已读状态
     * @param announcementIds 公告ID列表
     * @param tenantId 租户ID
     */
    void batchPopWindows(List<Long> announcementIds, String tenantId);

    Long countAnnonUnreadMessages(String tenantId);

    Long countWarningSkuUnreadMessages(String tenantId);

    void deleteWithValidByIds(List<Long> ids);
}
