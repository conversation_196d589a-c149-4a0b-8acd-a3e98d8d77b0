package com.zsmall.system.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.AnnouncementTenant;
import com.zsmall.system.entity.domain.vo.announcement.AnnouncementTenantVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公告已读Mapper接口
 *
 * <AUTHOR> Assistant
 * @date 2024-12-27
 */
public interface AnnouncementReadMapper extends BaseMapperPlus<AnnouncementTenant, AnnouncementTenantVo> {

    /**
     * 查询租户的公告已读记录
     * @param announcementId 公告ID
     * @param tenantId 租户ID
     * @return 已读记录
     */
    AnnouncementTenant selectByAnnouncementAndTenant(@Param("announcementId") Long announcementId,
                                                     @Param("tenantId") String tenantId);

    /**
     * 批量插入已读记录
     * @param readRecords 已读记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<AnnouncementTenant> readRecords);

    /**
     * 批量更新已读状态
     * @param announcementIds 公告ID列表
     * @param tenantId 租户ID
     * @param isRead 已读状态
     * @return 影响行数
     */
    int batchUpdateReadStatus(@Param("announcementIds") List<Long> announcementIds,
                              @Param("tenantId") String tenantId,
                              @Param("isRead") Integer isRead);

    /**
     * 批量更新弹窗状态
     * @param announcementIds 公告ID列表
     * @param tenantId 租户ID
     * @param isShowWindow 弹窗状态
     * @return 影响行数
     */
    int batchUpdateShowWindowStatus(@Param("announcementIds") List<Long> announcementIds,
                                    @Param("tenantId") String tenantId,
                                    @Param("isShowWindow") Integer isShowWindow);

    /**
     * 根据公告ID删除已读记录
     * @param announcementId 公告ID
     * @return 影响行数
     */
    int deleteByAnnouncementId(@Param("announcementId") Long announcementId);

    void deleteForTenant(@Param("ids") List<Long> ids, @Param("tenantId") String tenantId);
}
