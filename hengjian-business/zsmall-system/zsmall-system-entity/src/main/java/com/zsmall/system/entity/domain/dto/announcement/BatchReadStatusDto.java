package com.zsmall.system.entity.domain.dto.announcement;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量更新已读状态请求DTO
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
public class BatchReadStatusDto {

    /**
     * 公告ID列表
     */
    @NotEmpty(message = "公告ID列表不能为空")
    private List<Long> announcementIds;

    /**
     * 状态 0/1
     */
    @NotNull(message = "已读状态不能为空")
    private Integer status;

}
