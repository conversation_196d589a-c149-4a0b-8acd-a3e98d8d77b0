package com.zsmall.system.entity.mapper;


import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.BillDetailsRepair;
import com.zsmall.system.entity.domain.vo.billDetailsRepair.BillDetailsRepairVo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账单明细修补表Mapper接口
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
public interface BillDetailsRepairMapper extends BaseMapperPlus<BillDetailsRepair, BillDetailsRepairVo> {

    /**
     * 批量插入账单明细修补数据
     * @param billDetailsRepairList 账单明细修补数据列表
     * @return 插入数量
     */
    int batchInsertBillDetailsRepair(@Param("billDetailsRepairList") List<BillDetailsRepair> billDetailsRepairList);

    /**
     * 拼接sql插入
     * @param sql SQL语句
     */
    @Insert("${sql}")
    void insertBySql(@Param("sql") String sql);

    /**
     * 根据订单号查询修补数据
     * @param orderNos 订单号列表
     * @return 修补数据列表
     */
    List<BillDetailsRepair> selectByOrderNos(@Param("orderNos") List<String> orderNos);

    /**
     * 根据租户ID和时间范围查询修补数据
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 修补数据列表
     */
    List<BillDetailsRepair> selectByTenantIdAndTimeRange(@Param("tenantId") String tenantId,
                                                        @Param("startTime") String startTime,
                                                        @Param("endTime") String endTime);

    /**
     * 查询未推送的修补数据
     * @return 未推送的修补数据列表
     */
    List<BillDetailsRepair> selectUnsentRepairData();

    /**
     * 批量更新推送状态
     * @param ids ID列表
     * @param pushStatus 推送状态（0-未推送，1-已推送）
     * @return 更新数量
     */
    int batchUpdatePushStatus(@Param("ids") List<Long> ids, @Param("pushStatus") Integer pushStatus);

    /**
     * 根据订单号查询最新的修补记录（按创建时间倒序）
     * @param orderNos 订单号列表
     * @return 最新的修补记录列表
     */
    List<BillDetailsRepair> selectLatestByOrderNos(@Param("orderNos") List<String> orderNos);

    /**
     * 根据订单号和租户ID查询未推送的修补记录
     * @param orderNos 订单号列表
     * @param tenantId 租户ID
     * @return 未推送的修补记录列表
     */
    List<BillDetailsRepair> selectUnsentByOrderNos(@Param("orderNos") List<String> orderNos,
                                                   @Param("tenantId") String tenantId);

}
