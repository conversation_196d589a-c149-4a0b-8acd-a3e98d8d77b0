package com.zsmall.system.entity.mapper;


import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.BillDetails;
import com.zsmall.system.entity.domain.OrderItemSystem;
import com.zsmall.system.entity.domain.vo.billDetails.BillDetailsVo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 账单明细-newMapper接口
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
public interface BillDetailsMapper extends BaseMapperPlus<BillDetails, BillDetailsVo> {
    /**
     * 查询当前租户已完成的订单
     * @param startTime
     * @param endTime
     * @param object
     * @return
     */
    List<BillDetails> selectFulfilledBillDetail(@Param("startTime") String startTime,@Param("endTime") String endTime,
                                                @Param("tenantId") Object object,@Param("type") Integer type);

    int batchInsertBillDetails(@Param("billDetails") List<BillDetails> billDetails);

    List<OrderItemSystem> selectOrdersByTenant(@Param("billStartTime") Date billStartTime,
                                               @Param("billEndTime") Date billEndTime,
                                               @Param("tenantId") String tenantId,
                                               @Param("startRow") Integer startRow,
                                               @Param("finalPageSize") Integer finalPageSize,
                                               @Param("billType") Integer billType,
                                               @Param("currencyCode") String currencyCode);

    int selectCountOrdersByTenant(@Param("billStartTime") Date billStartTime,
                                                                        @Param("billEndTime") Date billEndTime,
                                                                        @Param("tenantId") String tenantId,
                                                                        @Param("billType") Integer billType,
                                                                        @Param("currencyCode") String currencyCode);

    /**
     * 拼接sql插入
     * @param sql
     */
    @Insert("${sql}")
    void insertBySql(@Param("sql") String sql);

}
