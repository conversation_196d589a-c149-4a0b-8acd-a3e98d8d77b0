package com.zsmall.system.entity.domain.vo.warningMessage;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.system.entity.domain.WarningMessageTenant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;

/**
 * 告警租户表视图对象 warning_message_tenant
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WarningMessageTenant.class)
public class WarningMessageTenantVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 预警id
     */
    @ExcelProperty(value = "预警ID")
    private Long warningMessageId;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 是否已读0未读 1已读
     */
    @ExcelProperty(value = "是否已读")
    private Integer isRead;

}
