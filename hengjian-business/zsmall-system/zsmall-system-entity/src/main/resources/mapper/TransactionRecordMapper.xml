<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TransactionRecordMapper">

    <select id="getAmountByTransactionType"
            resultType="com.zsmall.system.entity.domain.vo.payment.TransactionOrderRecordVo">
        select sum(transaction_amount) as amount
        from transaction_record
        where transaction_type = #{transactionType}
    </select>

    <select id="sumTransactionAmount" resultType="java.math.BigDecimal">
        select
            sum(transaction_amount) as amount
        from transaction_record
        where
            transaction_type = #{transactionType}
            and transaction_sub_type = #{transactionSubType}
            and transaction_state = 'Success'
        <if test="startDate != null">
            AND transaction_time &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND transaction_time &lt;= #{endDate}
        </if>
    </select>

    <select id="selectBusinessRelations"
            resultType="com.zsmall.system.entity.domain.vo.funds.TransactionBusinessRelationVo">
        select
            'Bill' as business_type, b.bill_no as business_no
        from
            bill b
        where (select 1 from transactions_bill t where t.bill_id = b.id and t.transactions_id = #{id})
        union
        select
            'Order' as business_type, o.order_no as business_no
        from
            orders o
        where exists (select 1 from transactions_orders t where t.order_id = o.id and t.transactions_id = #{id})
        union
        select
            'OrderRefund' as business_type, ore.order_refund_no as business_no
        from
            order_refund ore
        where exists (select 1 from transactions_order_refund t where t.order_refund_id = ore.id and t.transactions_id = #{id})
        ;
    </select>

    <select id="sumTotalAmountBySubType" resultType="java.math.BigDecimal">
        select sum(trr.transaction_amount) from transaction_record trr
        where trr.del_flag=0
        and currency =#{currencyCode}
        and  transaction_state='Success'
        and trr.tenant_id=#{tenantId}
        <if test="transactionSubTypeEnum != null and transactionSubTypeEnum.size() != 0">
            and trr.transaction_sub_type in
            <foreach collection="transactionSubTypeEnum" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        and DATE_FORMAT(create_time, '%Y-%m') = #{currentYearMonth}
    </select>

    <select id="selectWalletBalanceMonthEnd" resultType="java.math.BigDecimal">
        SELECT after_balance
        FROM transaction_record
        WHERE tenant_id = #{tenantId}
          and  del_flag=0
          and currency=#{currencyCode}
          and transaction_state = 'Success'
          AND DATE_FORMAT(transaction_time, '%Y-%m') = #{currentYearMonth}
        ORDER BY id DESC
        LIMIT 1
    </select>

    <select id="selectWalletBalanceMonthStart" resultType="java.math.BigDecimal">
        SELECT before_balance
        FROM transaction_record
        WHERE tenant_id = #{tenantId}
        and del_flag=0
        and currency=#{currencyCode}
        and transaction_state = 'Success'
        AND DATE_FORMAT(transaction_time, '%Y-%m') = #{currentYearMonth}
        ORDER BY id ASC
        LIMIT 1
    </select>


    <select id="selectListNotSysUserCount" resultType="java.lang.Integer">
        SELECT count(tr.id)
        FROM transaction_record tr
        WHERE tr.del_flag=0
        <if test="bo.tenantId != null and bo.tenantId != ''">
            and tr.tenant_id=#{bo.tenantId}
        </if>
        <if test="bo.transactionType != null and bo.transactionType != ''">
            and tr.transaction_type=#{bo.transactionType}
        </if>
        <if test="bo.transactionSubType != null and bo.transactionSubType != ''">
            and tr.transaction_sub_type=#{bo.transactionSubType}
        </if>
        <if test="bo.transactionState != null">
            and tr.transaction_state =#{bo.transactionState}
        </if>
        <if test="bo.createTimeStart != null">
            and tr.create_time &gt;= #{bo.createTimeStart}
        </if>
        <if test="bo.createTimeStart != null">
            and tr.create_time &lt;=  #{bo.createTimeEnd}
        </if>
        <if test="bo.tenantType != null">
            AND EXISTS (SELECT 1
            FROM sys_tenant st
            WHERE st.tenant_id = tr.tenant_id
            AND st.tenant_type = #{bo.tenantType})
        </if>
        <if test="bo.billNo != null">
            AND EXISTS (SELECT 1
            FROM transactions_bill tb
            WHERE tr.id = tb.transactions_id
            AND EXISTS (SELECT 1 FROM bill b WHERE b.id = tb.bill_id AND b.bill_no = #{bo.billNo}))
        </if>
        <if test="bo.orderNo != null">
            AND id IN (SELECT transactions_id FROM transactions_order_refund WHERE order_refund_id IN (SELECT id FROM order_refund WHERE order_no = #{bo.orderNo} AND del_flag = '0') union SELECT transactions_id FROM transactions_orders WHERE order_id IN ( SELECT id FROM orders WHERE order_no = #{bo.orderNo} AND del_flag = '0'))
        </if>
        <if test="bo.orderRefundNo != null and bo.orderRefundNo != ''">
            AND   EXISTS (SELECT 1
            FROM transactions_order_refund tor
            WHERE tr.id = tor.transactions_id
            AND EXISTS (SELECT 1
            FROM order_refund orr
            WHERE orr.id = tor.order_refund_id AND orr.order_refund_no = #{bo.orderRefundNo}))
        </if>
        <if test="bo.currency != null">
            and tr.currency =#{bo.currency}
        </if>
    </select>

    <select id="selectListNotSysUserDel" resultType="com.zsmall.system.entity.domain.TransactionRecord">
        SELECT tr.*
        FROM transaction_record tr
        WHERE tr.del_flag=0
        <if test="bo.tenantId != null and bo.tenantId != ''">
            and tr.tenant_id=#{bo.tenantId}
        </if>
        <if test="bo.transactionType != null and bo.transactionType != ''">
            and tr.transaction_type=#{bo.transactionType}
        </if>
        <if test="bo.transactionSubType != null and bo.transactionSubType != ''">
            and tr.transaction_sub_type=#{bo.transactionSubType}
        </if>
        <if test="bo.transactionNo != null  and bo.transactionNo != ''">
            and tr.transaction_no=#{bo.transactionNo}
        </if>
        <if test="bo.transactionState != null">
            and tr.transaction_state =#{bo.transactionState}
        </if>
        <if test="bo.createTimeStart != null">
            and tr.create_time &gt;= #{bo.createTimeStart}
        </if>
        <if test="bo.createTimeStart != null">
            and tr.create_time &lt;=  #{bo.createTimeEnd}
        </if>
        <if test="bo.tenantType != null">
            AND EXISTS (SELECT 1
            FROM sys_tenant st
            WHERE st.tenant_id = tr.tenant_id
            AND st.tenant_type = #{bo.tenantType})
        </if>
        <if test="bo.billNo != null">
            AND EXISTS (SELECT 1
            FROM transactions_bill tb
            WHERE tr.id = tb.transactions_id
            AND EXISTS (SELECT 1 FROM bill b WHERE b.id = tb.bill_id AND b.bill_no = #{bo.billNo}))
        </if>
        <if test="bo.orderNo != null">
            AND id IN (SELECT transactions_id FROM transactions_order_refund WHERE order_refund_id IN (SELECT id FROM order_refund WHERE order_no = #{bo.orderNo} AND del_flag = '0') union SELECT transactions_id FROM transactions_orders WHERE order_id IN ( SELECT id FROM orders WHERE order_no = #{bo.orderNo} AND del_flag = '0'))
        </if>
        <if test="bo.orderRefundNo != null and bo.orderRefundNo != ''">
            AND   EXISTS (SELECT 1
            FROM transactions_order_refund tor
            WHERE tr.id = tor.transactions_id
            AND EXISTS (SELECT 1
            FROM order_refund orr
            WHERE orr.id = tor.order_refund_id AND orr.order_refund_no = #{bo.orderRefundNo}))
        </if>
        <if test="bo.currency != null">
            and tr.currency =#{bo.currency}
        </if>
        order by tr.id desc
        limit  #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <select id="getOrderList" resultType="com.zsmall.system.entity.domain.vo.funds.TransactionsV2Vo">
        SELECT  t.transactions_id as id,
        o.order_no,
        o.country_code,
        o.tenant_id,
        o.currency,
        o.currency_symbol
        FROM orders o
        inner JOIN transactions_orders t ON t.order_id = o.id
        WHERE t.transactions_id in
        <foreach collection="id" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>


    <select id="getOrderRefoundList" resultType="com.zsmall.system.entity.domain.vo.funds.TransactionsV2Vo">
        SELECT  ore.order_no ,
        t.transactions_id as id,
        ore.currency_code as currency,
        ore.currency_symbol ,
        ore.tenant_id,
        o.country_code
        FROM order_refund ore
                 INNER JOIN transactions_order_refund t ON t.order_refund_id = ore.id
                 inner join orders o on ore.order_id = o.id
            AND t.transactions_id in
        <foreach collection="id" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>

    </select>

    <select id="listByOrderNoList" resultType="com.zsmall.system.entity.domain.TransactionRecord">
        select t1.*
        from transaction_record t1
                 left join transactions_orders t2 on t1.id = t2.transactions_id
                 left join orders t3 on t2.order_id = t3.id and t3.del_flag = 0
        where t1.del_flag = 0
          <if test="null != orderNoList and orderNoList.size()>0">
          and t3.order_no in
          <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
              #{orderNo}
          </foreach>
          </if>
    </select>
    <select id="queryOrdersByOrderNo" resultType="com.zsmall.system.entity.domain.OrdersSystemEntity">
        SELECT o.*,transaction_record.transaction_no as channelOrderNo
        FROM orders o
        inner join order_refund orr on o.id = orr.order_id
        inner JOIN transactions_order_refund ON transactions_order_refund.order_refund_id = orr.id
        inner JOIN transaction_record ON transactions_order_refund.transactions_id = transaction_record.id
        WHERE  transaction_record.transaction_no
        in
        <foreach collection="transactionNo" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
        UNION all
        SELECT o.*,transaction_record.transaction_no as channelOrderNo
        FROM orders o
        JOIN transactions_orders ON transactions_orders.order_id = o.id
        JOIN transaction_record ON transactions_orders.transactions_id = transaction_record.id
        WHERE  transaction_record.transaction_no in
        <foreach collection="transactionNo" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
    </select>
    <sql id="condition_sql">
        <where>
            <!-- 基本字段条件 - 增加直接索引过滤 -->
            <if test="bo.tenantId != null and bo.tenantId != ''">
                AND tr.tenant_id = #{bo.tenantId}
                AND tr.tenant_id = #{bo.tenantId} /* 双写触发索引选择 */
            </if>
            <if test="bo.transactionNo != null and bo.transactionNo != ''">
                AND tr.transaction_no = #{bo.transactionNo}
            </if>
            <if test="bo.transactionType != null and bo.transactionType != ''">
                AND tr.transaction_type = #{bo.transactionType}
            </if>

            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(bo.transactionSubTypes)">
                AND tr.transaction_sub_type IN
                <foreach collection="bo.transactionSubTypes" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="bo.transactionState != null and bo.transactionState != ''">
                AND tr.transaction_state = #{bo.transactionState}
            </if>
            <if test="bo.currency != null and bo.currency != ''">
                AND tr.currency = #{bo.currency}
            </if>

            <!-- 日期范围查询 - 优化为走覆盖索引 -->
            <if test="bo.searchDates != null and bo.searchDates.size() == 2">
                AND tr.create_time BETWEEN
                STR_TO_DATE(CONCAT(#{bo.searchDates[0]}, ' 00:00:00'), '%Y-%m-%d %H:%i:%s') AND
                STR_TO_DATE(CONCAT(#{bo.searchDates[1]}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                AND tr.create_time > '1970-01-01' /* 强制索引选择 */
            </if>

            <!-- 租户类型关联查询 - 优化为JOIN -->
            <if test="bo.tenantType != null and bo.tenantType != ''">
                AND EXISTS (
                SELECT 1 FROM sys_tenant st
                WHERE st.tenant_id = tr.tenant_id
                AND st.tenant_type = #{bo.tenantType}
                LIMIT 1 /* 优化器提示 */
                )
            </if>

            <!-- 账单号关联查询 - 直接JOIN -->
            <if test="bo.billNo != null and bo.billNo != ''">
                AND EXISTS (
                SELECT 1 FROM transactions_bill tb
                JOIN bill b ON tb.bill_id = b.id
                WHERE tb.transactions_id = tr.id
                AND b.bill_no = #{bo.billNo}
                LIMIT 1 /* 优化器提示 */
                )
            </if>

            <!-- ORDER BY前置优化 -->
            <bind name="useIndex" value="true" /> <!-- 强制索引提示 -->

            <!-- 订单号动态逻辑 - 完全重构 -->
            <choose>
                <when test="bo.transactionType == 'Income' and bo.orderNo != null and bo.orderNo != ''">
                    AND tr.id IN (
                    SELECT tor.transactions_id
                    FROM transactions_order_refund tor
                    JOIN order_refund orr ON orr.id = tor.order_refund_id AND orr.del_flag = '0'
                    WHERE orr.order_no = #{bo.orderNo}
                    )
                </when>
                <when test="bo.orderNo != null and bo.orderNo != ''">
                    AND tr.id IN (
                    SELECT t_ids.id FROM (
                    SELECT tor.transactions_id AS id
                    FROM transactions_order_refund tor
                    JOIN order_refund orr ON orr.id = tor.order_refund_id
                    WHERE orr.order_no = #{bo.orderNo} AND orr.del_flag = '0'
                    UNION ALL
                    SELECT to2.transactions_id
                    FROM transactions_orders to2
                    JOIN orders o ON o.id = to2.order_id
                    WHERE o.order_no = #{bo.orderNo} AND o.del_flag = '0'
                    ) AS t_ids
                    )
                </when>
            </choose>

            <!-- 退款单号查询 - 优化为延迟JOIN -->
            <if test="bo.orderRefundNo != null and bo.orderRefundNo != ''">
                AND tr.id IN (
                SELECT tor.transactions_id
                FROM transactions_order_refund tor
                JOIN order_refund orr ON orr.id = tor.order_refund_id
                WHERE orr.order_refund_no = #{bo.orderRefundNo}

                )
            </if>
        </where>
    </sql>
    <select id="queryPageList" resultType="com.zsmall.system.entity.domain.TransactionRecord">
        SELECT tr.*
        FROM transaction_record tr
        <include refid="condition_sql"/>
        ORDER BY tr.id DESC
    </select>


</mapper>
