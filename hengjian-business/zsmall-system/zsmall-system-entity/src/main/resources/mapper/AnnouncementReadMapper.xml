<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.AnnouncementReadMapper">

    <!-- 查询租户的公告已读记录 -->
    <select id="selectByAnnouncementAndTenant" resultType="com.zsmall.system.entity.domain.AnnouncementTenant">
        SELECT
            id,
            announcement_id,
            tenant_id,
            is_read,
            is_show_window,
            del_flag
        FROM announcement_tenant
        WHERE announcement_id = #{announcementId} AND tenant_id = #{tenantId}
        LIMIT 1
    </select>

    <!-- 批量插入已读记录 -->
    <insert id="batchInsert">
        INSERT INTO announcement_tenant (announcement_id, tenant_id, is_read, is_show_window, del_flag)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.announcementId}, #{item.tenantId}, #{item.isRead}, #{item.isShowWindow}, #{item.delFlag})
        </foreach>
    </insert>

    <!-- 批量更新已读状态 -->
    <update id="batchUpdateReadStatus">
        UPDATE announcement_tenant
        SET is_read = #{isRead}
        WHERE tenant_id = #{tenantId}
        AND announcement_id IN
        <foreach collection="announcementIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量更新弹窗状态 -->
    <update id="batchUpdateShowWindowStatus">
        UPDATE announcement_tenant
        SET is_show_window = #{isShowWindow}
        WHERE tenant_id = #{tenantId}
        AND announcement_id IN
        <foreach collection="announcementIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据公告ID删除已读记录 -->
    <delete id="deleteByAnnouncementId">
        DELETE FROM announcement_tenant
        WHERE announcement_id = #{announcementId}
    </delete>

    <update id="deleteForTenant">
        update announcement_tenant set del_flag=1
        where tenant_id = #{tenantId}
        and  announcement_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
