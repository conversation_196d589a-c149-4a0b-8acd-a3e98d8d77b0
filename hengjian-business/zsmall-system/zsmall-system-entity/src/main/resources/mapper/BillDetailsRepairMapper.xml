<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.BillDetailsRepairMapper">

    <insert id="batchInsertBillDetailsRepair">
        INSERT INTO bill_details_repair (
        id, bill_id, bill_no, order_no, channel_order_no, order_extend_id, tenant_id, supper_tenant_id, order_refund_no, order_time, order_status,
        order_payment_methods, product_sku_code, product_quantity, operation_fee, final_delivery_fee,
        product_sku_price, order_total_amount, order_refund_total_amount, create_by, create_time, update_by,
        update_time, del_flag, currency_symbol, currency_code, country_code, supported_logistics, unit_price, is_send_erp, send_erp_time
        ) VALUES
        <foreach collection="billDetailsRepairList" item="item" separator=",">
            (
            #{item.id},
            #{item.billId},
            #{item.billNo},
            #{item.orderNo},
            #{item.channelOrderNo},
            #{item.orderExtendId},
            #{item.tenantId},
            #{item.supperTenantId},
            #{item.orderRefundNo},
            #{item.orderTime},
            #{item.orderStatus},
            #{item.orderPaymentMethods},
            #{item.productSkuCode},
            #{item.productQuantity},
            #{item.operationFee},
            #{item.finalDeliveryFee},
            #{item.productSkuPrice},
            #{item.orderTotalAmount},
            #{item.orderRefundTotalAmount},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.delFlag},
            #{item.currencySymbol},
            #{item.currencyCode},
            #{item.countryCode},
            #{item.supportedLogistics},
            #{item.unitPrice},
            #{item.isSendErp},
            #{item.sendErpTime}
            )
        </foreach>
    </insert>

    <select id="selectByOrderNos" resultType="com.zsmall.system.entity.domain.BillDetailsRepair">
        SELECT * FROM bill_details_repair
        WHERE del_flag = 0
        <if test="orderNos != null and orderNos.size() > 0">
            AND order_no IN
            <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
                #{orderNo}
            </foreach>
        </if>
    </select>

    <select id="selectByTenantIdAndTimeRange" resultType="com.zsmall.system.entity.domain.BillDetailsRepair">
        SELECT * FROM bill_details_repair
        WHERE del_flag = 0
        AND tenant_id = #{tenantId}
        <if test="startTime != null and startTime != ''">
            AND order_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND order_time &lt;= #{endTime}
        </if>
        ORDER BY order_time DESC
    </select>

    <select id="selectUnsentRepairData" resultType="com.zsmall.system.entity.domain.BillDetailsRepair">
        SELECT * FROM bill_details_repair
        WHERE del_flag = 0
        AND (is_send_erp = 0 OR is_send_erp IS NULL)
        ORDER BY tenant_id, create_time DESC
    </select>

    <update id="batchUpdatePushStatus">
        UPDATE bill_details_repair
        SET is_send_erp = #{pushStatus}, send_erp_time = NOW(), update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectLatestByOrderNos" resultType="com.zsmall.system.entity.domain.BillDetailsRepair">
        SELECT * FROM (
            SELECT *, ROW_NUMBER() OVER (PARTITION BY order_no ORDER BY create_time DESC) as rn
            FROM bill_details_repair
            WHERE del_flag = 0
            <if test="orderNos != null and orderNos.size() > 0">
                AND order_no IN
                <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
                    #{orderNo}
                </foreach>
            </if>
        ) t WHERE t.rn = 1
        ORDER BY create_time DESC
    </select>

    <select id="selectUnsentByOrderNos" resultType="com.zsmall.system.entity.domain.BillDetailsRepair">
        SELECT * FROM bill_details_repair
        WHERE del_flag = 0
        AND tenant_id = #{tenantId}
        AND (is_send_erp = 0 OR is_send_erp IS NULL)
        <if test="orderNos != null and orderNos.size() > 0">
            AND order_no IN
            <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
                #{orderNo}
            </foreach>
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
