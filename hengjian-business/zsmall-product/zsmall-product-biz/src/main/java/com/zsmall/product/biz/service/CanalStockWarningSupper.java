package com.zsmall.product.biz.service;

import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;

/**
 * Canal库存预警业务处理器接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface CanalStockWarningSupper {

    /**
     * 处理Canal库存预警消息
     *
     * @param message 消息
     * @param channel 通道
     * @param isJob 是否来自定时任务
     * @throws Exception 处理异常
     */
    void dealCanalStockWarningMessage(Message message, Channel channel, Boolean isJob) throws Exception;

}
