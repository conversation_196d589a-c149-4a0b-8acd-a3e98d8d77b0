package com.zsmall.product.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.bo.warningMessage.WarningMessageBo;
import com.zsmall.product.entity.domain.vo.warningMessage.WarningMessageVo;

import java.util.List;

/**
 * 预警消息Service接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface WarningMessageService {

    /**
     * 查询预警消息
     */
    R<WarningMessageVo> queryById(Long id);

    /**
     * 查询预警消息列表
     */
    R<TableDataInfo<WarningMessageVo>> queryPageList(WarningMessageBo bo, PageQuery pageQuery);

    /**
     * 查询预警消息列表
     */
    R<List<WarningMessageVo>> queryList(WarningMessageBo bo);

    /**
     * 新增预警消息
     */
    R<Void> insertByBo(WarningMessageBo bo);

    /**
     * 修改预警消息
     */
    R<Void> updateByBo(WarningMessageBo bo);

    /**
     * 删除预警消息
     */
    R<Void> deleteWithValidByIds(List<Long> ids);

    /**
     * 获取未读消息数量
     */
    R<Long> getUnreadCount();

    /**
     * 批量标记消息为已读
     */
    R<Void> batchMarkAsRead(List<Long> ids);

    /**
     * 获取当前用户的消息列表
     */
    R<List<WarningMessageVo>> getCurrentUserMessages();

}
