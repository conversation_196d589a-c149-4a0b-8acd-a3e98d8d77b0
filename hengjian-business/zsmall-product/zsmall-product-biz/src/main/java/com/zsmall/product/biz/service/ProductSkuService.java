package com.zsmall.product.biz.service;

import com.zsmall.product.entity.domain.ProductSku;

import java.util.List;

/**
 * 商品SKUService接口
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
public interface ProductSkuService {

    /**
     * 统计SKU库存总数
     * @param productSku
     * @return
     */
    Integer querySkuStockTotal(ProductSku productSku);

    /**
     * 统计SKU库存总数（是否实时检查库存）
     * @param productSku
     * @param check
     * @return
     */
    Integer querySkuStockTotal(ProductSku productSku, boolean check);

    /**
     * 设置SKU库存
     * @param productSku
     */
    void setProductSkuStock(ProductSku productSku);

    void setProductSkuStockNotSync(ProductSku productSku);

    /**
     * 设置SKU库存(批发商品)
     * @param productSku
     */
    void setProductSkuStockWholesale(ProductSku productSku);

    /**
     * 设置SKU库存
     * @param productSkuList
     */
    void setProductSkuStock(List<ProductSku> productSkuList);

    /**
     * 设置SKU库存(批发商品)
     * @param productSkuList
     */
    void setProductSkuStockWholesale(List<ProductSku> productSkuList);

    /**
     * 功能描述：通过 ERP SKU 获取产品 SKU
     *
     * @param erpSku ERP SKU
     * @return {@link ProductSku }
     * <AUTHOR>
     * @date 2024/01/11
     */
    ProductSku getProductSkuByErpSku(String erpSku);

    /**
     * 功能描述：通过卖家 SKU 获取产品 SKU
     *
     * @param sellerSku 卖家 SKU
     * @param tenantId
     * @return {@link ProductSku }
     * <AUTHOR>
     * @date 2024/01/14
     */
    ProductSku getProductSkuBySellerSku(String sellerSku, String tenantId);

    ProductSku getProductSkuByItemNo(String itemNo);
}
