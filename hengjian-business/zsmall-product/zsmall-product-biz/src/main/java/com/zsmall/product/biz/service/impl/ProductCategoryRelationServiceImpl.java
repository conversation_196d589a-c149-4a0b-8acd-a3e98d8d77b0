package com.zsmall.product.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.product.biz.service.IProductCategoryRelationService;
import com.zsmall.product.entity.domain.ProductCategoryRelation;
import com.zsmall.product.entity.domain.bo.category.ProductCategoryRelationBo;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryRelationVo;
import com.zsmall.product.entity.mapper.ProductCategoryRelationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品SPU-商品分类关联Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ProductCategoryRelationServiceImpl implements IProductCategoryRelationService {

    private final ProductCategoryRelationMapper baseMapper;

    /**
     * 查询商品SPU-商品分类关联
     */
    @Override
    public ProductCategoryRelationVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品SPU-商品分类关联列表
     */
    @Override
    public TableDataInfo<ProductCategoryRelationVo> queryPageList(ProductCategoryRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductCategoryRelation> lqw = buildQueryWrapper(bo);
        Page<ProductCategoryRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品SPU-商品分类关联列表
     */
    @Override
    public List<ProductCategoryRelationVo> queryList(ProductCategoryRelationBo bo) {
        LambdaQueryWrapper<ProductCategoryRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductCategoryRelation> buildQueryWrapper(ProductCategoryRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductCategoryRelation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductId() != null, ProductCategoryRelation::getProductId, bo.getProductId());
        lqw.eq(bo.getProductCategoryId() != null, ProductCategoryRelation::getProductCategoryId, bo.getProductCategoryId());
        return lqw;
    }

    /**
     * 新增商品SPU-商品分类关联
     */
    @Override
    public Boolean insertByBo(ProductCategoryRelationBo bo) {
        ProductCategoryRelation add = MapstructUtils.convert(bo, ProductCategoryRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 批量新增商品SPU-商品分类关联
     *
     * @param entityList
     * @return
     */
    @Override
    public Boolean insertBatch(List<ProductCategoryRelation> entityList) {
        log.info("进入【批量新增商品SPU-商品分类关联】 entityList = {}", JSONUtil.toJsonStr(entityList));
        return baseMapper.insertBatch(entityList);
    }

    /**
     * 修改商品SPU-商品分类关联
     */
    @Override
    public Boolean updateByBo(ProductCategoryRelationBo bo) {
        ProductCategoryRelation update = MapstructUtils.convert(bo, ProductCategoryRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductCategoryRelation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品SPU-商品分类关联
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据实体类删除
     *
     * @param entityList
     * @return
     */
    @Override
    public Boolean deleteByEntityList(List<ProductCategoryRelation> entityList) {
        log.info("进入【根据实体类删除】 entityList = {}", JSONUtil.toJsonStr(entityList));
        return baseMapper.deleteBatchIds(entityList) > 0;
    }

    /**
     * 批量保存商品类目关系数据
     * @param productId
     * @param ids
     */
    @Override
    public void batchUpdateProductCategoryRelation(Long productId, List<Long> ids) {

        List<ProductCategoryRelation> list = new ArrayList<>();
        ids.stream().forEach(id ->{
            ProductCategoryRelation relation = new ProductCategoryRelation();
            relation.setProductId(productId);
            relation.setProductCategoryId(id);
            list.add(relation);
        });
        baseMapper.insertOrUpdateBatch(list);
    }

    /**
     * 根据类目ID集合查询是否存在商品绑定关系
     * @param categoryIds
     * @return
     */
    @Override
    public Boolean existRelationByCategoryIds(List<Long> categoryIds) {
        LambdaQueryWrapper<ProductCategoryRelation> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductCategoryRelation::getProductCategoryId, categoryIds);
        return CollUtil.isEmpty(baseMapper.selectVoList(lqw)) ? false : true;
    }

    @Override
    @InMethodLog("根据类目ID集合查询所有关联的商品编号")
    public List<String> existProductCodeRelationByCategoryIds(List<Long> categoryIds) {
        return baseMapper.existProductCodeRelationByCategoryIds(categoryIds);
    }

    /**
     * 根据商品主键查询分类关系集合
     *
     * @param productId
     * @return
     */
    @Override
    public List<ProductCategoryRelation> queryByProductId(Long productId) {
        log.info("进入【根据商品主键查询关联的分类主键集合】 productId = {}", productId);
        LambdaQueryWrapper<ProductCategoryRelation> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductCategoryRelation::getProductId, productId);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据商品主键删除
     *
     * @param productId
     */
    @Override
    public Boolean deleteByProductId(Long productId) {
        LambdaQueryWrapper<ProductCategoryRelation> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductCategoryRelation::getProductId, productId);
        return baseMapper.delete(lqw) > 0;
    }

    @Override
    public void cleanAndCopySourceTenantId(Map<Long, Long> productIdMapping) {
        // productIdMapping 取出所有的key 放入list
        ArrayList<ProductCategoryRelation> targetProductCategoryRelations = new ArrayList<>();
        List<Long> keyList = new ArrayList<>(productIdMapping.keySet());
        LambdaQueryWrapper<ProductCategoryRelation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductCategoryRelation::getProductId,keyList);
        lambdaQueryWrapper.eq(ProductCategoryRelation::getDelFlag,0);
        List<ProductCategoryRelation> categoryRelations = TenantHelper.ignore(() -> baseMapper.selectList(lambdaQueryWrapper));
        // categoryRelations根据productIdMapping 将集合内符合productId =key的值替换成productIdMapping的value
        for (ProductCategoryRelation relation : categoryRelations) {
            ProductCategoryRelation targetProductCategoryRelation = new ProductCategoryRelation();

            BeanUtil.copyProperties(relation,targetProductCategoryRelation);
            Long oldId = relation.getProductId();
            if (productIdMapping.containsKey(oldId)) {
                targetProductCategoryRelation.setProductId(productIdMapping.get(oldId));
                targetProductCategoryRelation.setId(null);
            }else {
                throw new RuntimeException("商品数据迁移异常");
            }
            targetProductCategoryRelations.add(targetProductCategoryRelation);
        }
        insertBatch(targetProductCategoryRelations);
    }

}
