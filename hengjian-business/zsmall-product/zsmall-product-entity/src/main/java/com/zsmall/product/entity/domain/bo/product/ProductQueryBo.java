package com.zsmall.product.entity.domain.bo.product;

import lombok.Data;

import java.util.List;

/**
 * 商品SPU业务对象 product
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
public class ProductQueryBo {

    /**
     * 查询类型：ProductName-商品名，Sku-最小库存单位，ItemNo-SKU唯一编号, ProductCodes-商品编码
     */
    private String queryType;

    /**
     * 查询关键字
     */
    private String queryValue;

    /**
     * 上架状态：All-全部，OnShelf-上架，OffShelf-下架
     */
    private String shelfState;

    /**
     * 商品类型
     */
    private String productType;

    /**
     * 审计状态
     */
    private String auditStatus;

    /**
     * SKU的上架状态 OnShelf-上架；OffShelf-下架
     */
    private String skuShelfState;
    /**
     * 查询关键字集合
     */
    private List<String> queryValues;

    /**
     * 站点
     */
    private Long siteId;
    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 锁货异常码
     */
    private Integer lockExceptionCode;

    /**
     * 商品SKU编号集合,用于库存异常查询
     */
    List<String> productSkuCodeList;
}
