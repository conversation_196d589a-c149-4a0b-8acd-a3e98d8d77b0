package com.zsmall.product.entity.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.product.entity.iservice.IProductImportRecordService;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.mapper.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.hengjian.common.core.constant.AbstractCodeTypeBase;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.service.AbstractCodeGenerator;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 仓库系统编号生成器实现类
 *
 * <AUTHOR>
 * @date 2023/5/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProductCodeGenerator extends AbstractCodeGenerator {

     private final IProductService iProductService;
     private final ProductSkuMapper productSkuMapper;
     private final ProductSkuStockMapper productSkuStockMapper;
     private final ProductSkuPriceRuleMapper productSkuPriceRuleMapper;
     private final IProductImportRecordService iProductImportRecordService;
     private final ProductQuestionMapper productQuestionMapper;
     private final ProductQuestionAnswerMapper productQuestionAnswerMapper;


    /**
     * 编号生成器
     *
     * @param type 主类型
     * @return
     */
    @Override
    public String codeGenerate(AbstractCodeTypeBase type) throws RStatusCodeException {
        log.info("编号生成器 type = {}, subType = {}", type);
        var code = "";
        var value = type.getValue();
        var repeat = true;
        while (repeat) {
            if (BusinessCodeEnum.ProductCode.equals(type)) {
                code = value + RandomUtil.randomNumbers(6);
                repeat = iProductService.existProductCode(code);
            } else if (BusinessCodeEnum.ProductSkuCode.equals(type)) {
//                code = value + RandomUtil.randomNumbers(6); 重复概率高
                code = value + RandomUtil.randomNumbers(6);
                repeat = productSkuMapper.existProductSkuCode(code);
            } else if (BusinessCodeEnum.StockCode.equals(type)) {
                code = value + RandomUtil.randomNumbers(10);
                repeat = productSkuStockMapper.existStockCode(code);
            } else if (BusinessCodeEnum.CalculationRuleCode.equals(type)) {
                code = value + RandomUtil.randomNumbers(10);
                repeat = productSkuPriceRuleMapper.existRuleCode(code);
            } else if (BusinessCodeEnum.ProductImportRecordNo.equals(type)) {
                code = value + RandomUtil.randomNumbers(10);
                repeat = iProductImportRecordService.existImportRecordNo(code);
            } else if (BusinessCodeEnum.ProductQuestion.equals(type)) {
                code = value + RandomUtil.randomStringUpper(9);
                repeat = productQuestionMapper.existQuestionCode(code);
            } else if (BusinessCodeEnum.ProductQuestionAnswer.equals(type)) {
                code = value + RandomUtil.randomStringUpper(8);
                repeat = productQuestionAnswerMapper.existAnswerCode(code);
            }
        }

        if (StrUtil.isNotBlank(code)) {
            return code;
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.BUSINESS_CODE_GENERATE_ERROR);
        }
    }

    /**
     * 编号生成器
     *
     * @param type    主类型
     * @param subType 子类型
     * @return
     */
    @Override
    public String codeGenerate(AbstractCodeTypeBase type, String subType) throws RStatusCodeException {
        return null;
    }

    public String codeGenerateAndVerify(AbstractCodeTypeBase type, Set<String> generatedCodes) {
        int maxAttempts = 100; // 设置最大尝试次数，防止无限循环
        int attempts = 0;

        log.info("编号生成器 type = {}, subType = {}", type);
        var code = "";
        var value = type.getValue();
        var repeat = true;
        while (repeat) {
            if (BusinessCodeEnum.ProductCode.equals(type)) {
                code = value + RandomUtil.randomNumbers(6);
                repeat = iProductService.existProductCode(code);
            } else if (BusinessCodeEnum.ProductSkuCode.equals(type)) {
//                code = value + RandomUtil.randomNumbers(6); 重复概率高
                do {
                    // 生成随机码
                    code = value + RandomUtil.randomNumbers(6);
                    attempts++;

                    // 如果尝试次数过多，可以考虑扩展随机码位数或使用其他策略
                    if (attempts > maxAttempts) {
                        // 扩展随机码位数以避免冲突
                        code = value + RandomUtil.randomNumbers(8);
                    }
                } while (generatedCodes.contains(code));

                repeat = productSkuMapper.existProductSkuCode(code);
                generatedCodes.add(code);
            } else if (BusinessCodeEnum.StockCode.equals(type)) {
                code = value + RandomUtil.randomNumbers(10);
                repeat = productSkuStockMapper.existStockCode(code);
            } else if (BusinessCodeEnum.CalculationRuleCode.equals(type)) {
                code = value + RandomUtil.randomNumbers(10);
                repeat = productSkuPriceRuleMapper.existRuleCode(code);
            } else if (BusinessCodeEnum.ProductImportRecordNo.equals(type)) {
                code = value + RandomUtil.randomNumbers(10);
                repeat = iProductImportRecordService.existImportRecordNo(code);
            } else if (BusinessCodeEnum.ProductQuestion.equals(type)) {
                code = value + RandomUtil.randomStringUpper(9);
                repeat = productQuestionMapper.existQuestionCode(code);
            } else if (BusinessCodeEnum.ProductQuestionAnswer.equals(type)) {
                code = value + RandomUtil.randomStringUpper(8);
                repeat = productQuestionAnswerMapper.existAnswerCode(code);
            }
        }

        if (StrUtil.isNotBlank(code)) {

            return code;
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.BUSINESS_CODE_GENERATE_ERROR);
        }
    }
}
