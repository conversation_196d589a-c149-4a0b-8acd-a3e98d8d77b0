package com.zsmall.warehouse.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.warehouse.entity.domain.WarehouseAdminInfo;
import com.zsmall.warehouse.entity.mapper.WarehouseAdminInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年7月10日  17:38
 * @description: 管理远仓库业务处理
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IWarehouseAdminService extends ServiceImpl<WarehouseAdminInfoMapper, WarehouseAdminInfo> {

    private final WarehouseAdminInfoMapper baseMapper;

    /**
     * 获取全部的管理员仓库信息
     *
     * @return
     */
    public List<WarehouseAdminInfo> listWarehouseAdminInfo(){
        LambdaQueryWrapper<WarehouseAdminInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(WarehouseAdminInfo::getDelFlag, "0");
        lqw.eq(WarehouseAdminInfo::getWarehouseState, 1);
        lqw.orderByDesc(WarehouseAdminInfo::getId);
        return TenantHelper.ignore(() ->baseMapper.selectList(lqw));
    }

    public void cleanAndCopySourceTenantId(String sourceTenantId, String targetTenantId,
                                           HashMap<String, String> warehouseSysCodeMap,
                                           HashMap<String, List<WarehouseAdminInfo>> sourceWarehouseAdminMap) {
        IWarehouseAdminService proxy = (IWarehouseAdminService) AopContext.currentProxy();

        List<WarehouseAdminInfo> lists = new ArrayList<>();
        LambdaQueryWrapper<WarehouseAdminInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WarehouseAdminInfo::getTenantId,sourceTenantId);
        List<WarehouseAdminInfo> warehouseAdminInfos = TenantHelper.ignore(() -> baseMapper.selectList(queryWrapper));
        for (WarehouseAdminInfo warehouseAdminInfo : warehouseAdminInfos) {
            String warehouseSystemCode = warehouseAdminInfo.getWarehouseSystemCode();
            WarehouseAdminInfo targetWarehouseAdminInfo = new WarehouseAdminInfo();
            if(CollUtil.isEmpty(warehouseSysCodeMap)||(CollUtil.isNotEmpty(warehouseSysCodeMap)&&!warehouseSysCodeMap.containsKey(warehouseSystemCode))){
                continue;
            }
            String targetWarehouseSystemCode = warehouseSysCodeMap.get(warehouseSystemCode);
            BeanUtil.copyProperties(warehouseAdminInfo,targetWarehouseAdminInfo);
            targetWarehouseAdminInfo.setId(null);
            targetWarehouseAdminInfo.setTenantId(targetTenantId);
            targetWarehouseAdminInfo.setWarehouseSystemCode(targetWarehouseSystemCode);
            targetWarehouseAdminInfo.setMigrationId(warehouseAdminInfo.getId());
            lists.add(targetWarehouseAdminInfo);
        }
        proxy.saveBatch(lists);
        sourceWarehouseAdminMap.put("sourceWarehouseAdmin",warehouseAdminInfos);
        sourceWarehouseAdminMap.put("targetWarehouseAdmin",warehouseAdminInfos);
    }
}
