package com.zsmall.warehouse.entity.iservice;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.warehouse.entity.domain.WarehouseAdminDeliveryCountry;
import com.zsmall.warehouse.entity.domain.WarehouseAdminInfo;
import com.zsmall.warehouse.entity.mapper.WarehouseAdminDeliveryCountryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 仓库地址支持配送国家Service接口
 *
 * <AUTHOR> Li
 * @date 2024-12-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IWarehouseAdminDeliveryCountryService extends ServiceImpl<WarehouseAdminDeliveryCountryMapper, WarehouseAdminDeliveryCountry> {

    public void cleanAndCopySourceTenantId(HashMap<String, List<WarehouseAdminInfo>> sourceWarehouseAdminMap) {
        IWarehouseAdminDeliveryCountryService proxy = (IWarehouseAdminDeliveryCountryService) AopContext.currentProxy();
        List<WarehouseAdminInfo> sourceWarehouseAdmin = sourceWarehouseAdminMap.get("sourceWarehouseAdmin");
        List<WarehouseAdminInfo> targetWarehouseAdmin = sourceWarehouseAdminMap.get("targetWarehouseAdmin");
        if(CollUtil.isEmpty(sourceWarehouseAdmin)||CollUtil.isEmpty(targetWarehouseAdmin)){

        }else {
            Map<Long, Long> map = targetWarehouseAdmin.stream()
                                                      .collect(Collectors.toMap(WarehouseAdminInfo::getMigrationId, WarehouseAdminInfo::getId));
            List<Long> warehouseIds = sourceWarehouseAdmin.stream().map(WarehouseAdminInfo::getId).collect(Collectors.toList());
            LambdaQueryWrapper<WarehouseAdminDeliveryCountry> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(WarehouseAdminDeliveryCountry::getWarehouseAdminInfoId,warehouseIds);
            List<WarehouseAdminDeliveryCountry> countries = new ArrayList<>();
            List<WarehouseAdminDeliveryCountry> countryList = TenantHelper.ignore(() -> baseMapper.selectList(queryWrapper));
            //warehouseIds
            for (WarehouseAdminDeliveryCountry warehouseAdminDeliveryCountry : countryList) {
                WarehouseAdminDeliveryCountry targetDeliveryCountry = new WarehouseAdminDeliveryCountry();
                Long sourceId = warehouseAdminDeliveryCountry.getWarehouseAdminInfoId();
                WarehouseAdminDeliveryCountry targetWarehouseAdminDelivery = new WarehouseAdminDeliveryCountry();

                Long targetId = map.get(sourceId);
                BeanUtil.copyProperties(warehouseAdminDeliveryCountry,targetDeliveryCountry);
                targetWarehouseAdminDelivery.setId(null);
                targetWarehouseAdminDelivery.setWarehouseAdminInfoId(targetId);

                countries.add(targetWarehouseAdminDelivery);
            }
            proxy.saveBatch(countries);
        }

    }
}
