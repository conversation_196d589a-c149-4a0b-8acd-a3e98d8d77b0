package com.zsmall.warehouse.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.warehouse.entity.domain.WarehouseAddress;
import com.zsmall.warehouse.entity.domain.bo.warehouse.WarehouseAddressBo;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseAddressVo;
import com.zsmall.warehouse.entity.mapper.WarehouseAddressMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 仓库地址信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-24
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IWarehouseAddressService extends ServiceImpl<WarehouseAddressMapper, WarehouseAddress> {

    public WarehouseAddressVo queryByWarehouseId(Long warehouseId) {
        log.info("根据仓库主表主键查询仓库地址信息 warehouseId = {}");
        LambdaQueryWrapper<WarehouseAddress> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(WarehouseAddress::getWarehouseId, warehouseId);
        return baseMapper.selectVoOne(lambdaQueryWrapper);
    }

    /**
     * 新增仓库地址信息
     */
    public Boolean insertByBo(WarehouseAddressBo bo) {
        WarehouseAddress add = MapstructUtils.convert(bo, WarehouseAddress.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改仓库地址信息
     */
    public Boolean updateByBo(WarehouseAddressBo bo) {
        WarehouseAddress update = MapstructUtils.convert(bo, WarehouseAddress.class);
        return baseMapper.updateById(update) > 0;
    }

    @InMethodLog("根据商品SKU查询所属的仓库地址")
    public List<WarehouseAddress> queryByProductSkuCode(String productSkuCode) {
        return baseMapper.queryByProductSkuCode(productSkuCode);
    }

    @InMethodLog("根据商品SKU映射渠道查询所属的仓库地址")
    public IPage<WarehouseAddress> queryByProductMappingChannel(Page<WarehouseAddress> page, Long channelId, String queryValue) {
        return baseMapper.queryByProductMappingChannel(page, channelId, queryValue);
    }

}
