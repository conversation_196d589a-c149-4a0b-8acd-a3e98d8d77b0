package com.zsmall.activity.controller.controller;

import java.util.List;

import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.activity.entity.domain.bo.storageFee.StorageFeeItemBo;
import com.zsmall.activity.entity.domain.vo.storageFee.StorageFeeItemVo;
import com.zsmall.activity.entity.iservice.IStorageFeeItemService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.web.core.BaseController;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.log.enums.BusinessType;

/**
 * 仓储费子
 *
 * <AUTHOR> Li
 * @date 2025-07-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/storageFeeItem")
public class StorageFeeItemController extends BaseController {

    private final IStorageFeeItemService storageFeeItemService;

    /**
     * 查询仓储费子列表
     */
    @GetMapping("/list")
    public TableDataInfo<StorageFeeItemVo> list(StorageFeeItemBo bo, PageQuery pageQuery) {
        return storageFeeItemService.queryPageList(bo, pageQuery);
    }


}
