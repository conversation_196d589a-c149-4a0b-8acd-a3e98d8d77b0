package com.zsmall.activity.biz.factory.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.activity.biz.factory.ProductActivityFactory;
import com.zsmall.activity.biz.factory.ProductActivityFactoryService;
import com.zsmall.activity.biz.support.ProductActivitySupport;
import com.zsmall.activity.entity.domain.ProductActivityItem;
import com.zsmall.activity.entity.domain.bo.productActivity.ActivityUpdateBo;
import com.zsmall.activity.entity.domain.bo.productActivity.ProductActivityBaseBo;
import com.zsmall.activity.entity.domain.bo.productActivity.ProductActivityQueryBo;
import com.zsmall.activity.entity.domain.vo.productActivity.ProductActivityDetailBaseVo;
import com.zsmall.activity.entity.domain.vo.productActivity.ProductActivityDetailDVo;
import com.zsmall.activity.entity.domain.vo.productActivity.ProductActivityDraftVo;
import com.zsmall.activity.entity.domain.vo.productActivity.ProductActivityItemQueryVo;
import com.zsmall.activity.entity.iservice.IProductActivityItemService;
import com.zsmall.activity.entity.iservice.IProductActivityReviewRecordService;
import com.zsmall.activity.entity.iservice.IProductActivityService;
import com.zsmall.activity.entity.iservice.IProductActivityStockLockItemService;
import com.zsmall.common.enums.productActivity.ProductActivityItemStateEnum;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商品活动工厂-分销商相关实现
 *
 * <AUTHOR>
 * @date 2023/7/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistributorImpl implements ProductActivityFactoryService {

    private final IProductSkuService iProductSkuService;
    private final IProductActivityService iProductActivityService;
    private final IProductActivityItemService iProductActivityItemService;
    private final IProductActivityStockLockItemService iProductActivityStockLockItemService;
    private final IProductActivityReviewRecordService iProductActivityReviewRecordService;
    private final IProductSkuStockService iProductSkuStockService;

    private final ProductActivitySupport productActivitySupport;

    @Override
    public void afterPropertiesSet() throws Exception {
        ProductActivityFactory.register(TenantType.Distributor, this);
    }

    /**
     * 分页查询商品活动列表
     */
    @Override
    public TableDataInfo getProductActivityPage(ProductActivityQueryBo bo, PageQuery pageQuery) {
        String activityState = bo.getActivityState();
        if (ProductActivityItemStateEnum.InProgress.name().equals(activityState)) {
            bo.setOrderBy("ORDER BY -pasli.expiry_date_time DESC, pai.create_time ASC");
        } else {
            bo.setOrderBy("ORDER BY pai.create_time DESC");
        }
        IPage<ProductActivityItemQueryVo> page = iProductActivityItemService.queryPage(bo, pageQuery.build());
        return TableDataInfo.build(page);
    }

    /**
     * 创建活动
     *
     * @param bo
     */
    @Override
    public R<Void> createActivity(ProductActivityBaseBo bo) {
        throw new RStatusCodeException(ZSMallStatusCodeEnum.NO_HANDLE_PERMISSION);
    }

    /**
     * 查询商品活动草稿
     *
     * @param activityID
     */
    @Override
    public R<ProductActivityDraftVo> getActivityDraft(String activityID) {
        throw new RStatusCodeException(ZSMallStatusCodeEnum.NO_HANDLE_PERMISSION);
    }

    /**
     * 查询商品活动详情
     *
     * @param activityID
     */
    @Override
    public R<ProductActivityDetailBaseVo> getActivityDetail(String activityID) {
        //活动信息
        ProductActivityDetailDVo vo = iProductActivityItemService.queryDetailVo(activityID);
        if (vo == null) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST);
        }
        return R.ok(vo);
    }

    /**
     * 更新活动状态
     *
     * @param bo
     */
    @Override
    public R<Void> updateState(ActivityUpdateBo bo) {
        String activityID = bo.getActivityID();
        String activityState = bo.getActivityState();
        // 获取活动信息
        ProductActivityItem productActivityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().activityCode(activityID).build());
        if (productActivityItem == null) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST);
        }

        ProductActivityTypeEnum activityType = productActivityItem.getActivityType();
        ProductActivityItemStateEnum activityStateEnum = ProductActivityItemStateEnum.valueOf(activityState);
        if (!ProductActivityItemStateEnum.Canceled.equals(activityStateEnum) || ProductActivityTypeEnum.Buyout.equals(activityType)) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_CANNOT_UPDATE_STATUS);
        }
        productActivitySupport.cancelActivityItem(productActivityItem, activityStateEnum);
        return R.ok();
    }
}
