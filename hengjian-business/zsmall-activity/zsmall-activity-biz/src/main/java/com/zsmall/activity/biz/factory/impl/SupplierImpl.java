package com.zsmall.activity.biz.factory.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.activity.biz.factory.ProductActivityFactory;
import com.zsmall.activity.biz.factory.ProductActivityFactoryService;
import com.zsmall.activity.biz.support.ProductActivitySupport;
import com.zsmall.activity.entity.domain.ProductActivity;
import com.zsmall.activity.entity.domain.ProductActivityReviewRecord;
import com.zsmall.activity.entity.domain.bo.productActivity.ActivityStockBo;
import com.zsmall.activity.entity.domain.bo.productActivity.ActivityUpdateBo;
import com.zsmall.activity.entity.domain.bo.productActivity.ProductActivityBaseBo;
import com.zsmall.activity.entity.domain.bo.productActivity.ProductActivityQueryBo;
import com.zsmall.activity.entity.domain.vo.productActivity.*;
import com.zsmall.activity.entity.iservice.IProductActivityItemService;
import com.zsmall.activity.entity.iservice.IProductActivityReviewRecordService;
import com.zsmall.activity.entity.iservice.IProductActivityService;
import com.zsmall.common.enums.productActivity.ProductActivityReviewState;
import com.zsmall.common.enums.productActivity.ProductActivityStateEnum;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAndStockVo;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品活动工厂-供货商相关实现
 *
 * <AUTHOR>
 * @date 2023/7/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierImpl implements ProductActivityFactoryService {

    private final IProductSkuService iProductSkuService;
    private final IProductActivityService iProductActivityService;
    private final IProductActivityItemService iProductActivityItemService;
    private final IProductActivityReviewRecordService iProductActivityReviewRecordService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IWarehouseService iWarehouseService;

    private final ProductActivitySupport productActivitySupport;

    @Override
    public void afterPropertiesSet() throws Exception {
        ProductActivityFactory.register(TenantType.Supplier, this);
    }

    /**
     * 分页查询商品活动列表
     */
    @Override
    public TableDataInfo getProductActivityPage(ProductActivityQueryBo bo, PageQuery pageQuery) {
        IPage<ProductActivityQueryVo> page = iProductActivityService.queryPage(bo, pageQuery.build());
        return TableDataInfo.build(page);
    }

    /**
     * 创建活动
     *
     * @param bo
     */
    @Override
    public R<Void> createActivity(ProductActivityBaseBo bo) {
        String activityType = bo.getActivityType();
        Integer activityTime = bo.getActivityTime();
        String activityState = bo.getActivityState();
        String productSkuCode = bo.getProductSkuCode();
        Integer quantityMinimum = bo.getQuantityMinimum();
        List<ActivityStockBo> stockList = bo.getStockList();

        //状态只能是草稿或者待审核
        ProductActivityStateEnum activityStateEnum = ProductActivityStateEnum.valueOf(activityState);
        if (!ProductActivityStateEnum.Draft.equals(activityStateEnum) && !ProductActivityStateEnum.UnderReview.equals(activityStateEnum)) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_STATUS_NOT_DRAFT_UNDERREVIEW_ERROR);
        }

        // 锁货专属的限制
        if (ProductActivityTypeEnum.StockLock.name().equals(activityType) && (activityTime == null || activityTime < 30)) {
            // 期限不能低于30天
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_TIME_LESS_THEN_30_ERROR);
        }

        //查询所有仓库库存是否足够
        Integer quantityTotal = 0;
        for (ActivityStockBo activityStockBo : stockList) {
            String warehouseSystemCode = activityStockBo.getWarehouseSystemCode();
            Integer stockQuantity = activityStockBo.getQuantity();
            ProductSkuStock productSkuStock = iProductSkuStockService.queryByProductSkuCode(productSkuCode, warehouseSystemCode);
            Integer stockAvailable = productSkuStock.getStockAvailable();
            // 库存不足
            if (stockQuantity > stockAvailable) {
                Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCode(warehouseSystemCode);
                return R.fail(ZSMallStatusCodeEnum.STOCK_LOCK_QUANTITY_NOT_ENOUGH.args(warehouse != null ? warehouse.getWarehouseName() : warehouseSystemCode));
            }
            // 统计可锁货总数
            quantityTotal += stockQuantity;
        }
        bo.setQuantityTotal(quantityTotal);

        // 活动商品总数量不能小于最小起订量，报错提示
        if (quantityTotal < quantityMinimum) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_QUANTITY_LESS_THEN_MINIMUM);
        }

        //保存为草稿或者直接提交审核
        if (ProductActivityStateEnum.Draft.equals(activityStateEnum)) {
            try {
                productActivitySupport.createActivityDraft(bo);
            } catch (DataIntegrityViolationException e) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.TOTAL_AMOUNT_IS_TOO_LARGE);
            }
        } else {
            productActivitySupport.lockCreateActivity(bo);
        }
        return R.ok();
    }

    /**
     * 查询商品活动草稿
     *
     * @param activityID
     */
    @Override
    public R<ProductActivityDraftVo> getActivityDraft(String activityID) {
        //活动信息
        ProductActivityDraftVo productActivityDraftVo = iProductActivityService.queryDraftVo(activityID);
        if (productActivityDraftVo == null) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST);
        }

        String productSkuCode = productActivityDraftVo.getProductSkuCode();
        ProductSkuAndStockVo productSkuAndStockVo = iProductSkuService.queryProductSkuAndStockByCode(productSkuCode);
        ActivityProductInfoVo activityProductInfoVo = BeanUtil.toBean(productSkuAndStockVo, ActivityProductInfoVo.class);
        productActivityDraftVo.setProduct(activityProductInfoVo);

        return R.ok(productActivityDraftVo);
    }

    /**
     * 查询商品活动详情
     *
     * @param activityID
     */
    @Override
    public R<ProductActivityDetailBaseVo> getActivityDetail(String activityID) {
        ProductActivityDetailVo productActivityDetailVo = iProductActivityService.queryDetailVoSupplier(activityID);
        return R.ok(productActivityDetailVo);
    }

    /**
     * 更新活动状态
     *
     * @param bo
     */
    @Override
    public R<Void> updateState(ActivityUpdateBo bo) {
        LoginHelper.getLoginUser(TenantType.Supplier);
        String activityID = bo.getActivityID();
        String activityState = bo.getActivityState();
        // 获取活动信息
        ProductActivity productActivity = iProductActivityService.queryOneByEntity(ProductActivity.builder().activityCode(activityID).build());
        if (productActivity == null) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST);
        }

        Long productActivityId = productActivity.getId();
        ProductActivityStateEnum activityStateEnum = ProductActivityStateEnum.valueOf(activityState);

        if (!ProductActivityStateEnum.Draft.equals(activityStateEnum) && !ProductActivityStateEnum.UnderReview.equals(activityStateEnum) && !ProductActivityStateEnum.Canceling.equals(activityStateEnum)) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_CANNOT_UPDATE_STATUS);
        }

        ProductActivityStateEnum originState = productActivity.getActivityState();

        ZSMallStatusCodeEnum activityCancelSuccess = ZSMallStatusCodeEnum.ACTIVITY_CANCEL_REQUEST_SUBMITTED;
        // 需要提交给员工审核
        if (ProductActivityStateEnum.UnderReview.equals(activityStateEnum) || ProductActivityStateEnum.Canceling.equals(activityStateEnum)) {
            ProductActivityReviewRecord reviewRecordEntity = new ProductActivityReviewRecord();
            reviewRecordEntity.setProductActivityId(productActivityId);
            reviewRecordEntity.setActivityOriginState(originState);
            reviewRecordEntity.setReviewState(ProductActivityReviewState.Pending);
            iProductActivityReviewRecordService.save(reviewRecordEntity);
        }

        // 如果状态是Published、UnderReview、NotApproved直接取消，不需要审核
        if ((ProductActivityStateEnum.Published.equals(originState) || ProductActivityStateEnum.UnderReview.equals(originState) ||
            ProductActivityStateEnum.NotApproved.equals(originState)) && ProductActivityStateEnum.Canceling.equals(activityStateEnum)) {
            activityStateEnum = ProductActivityStateEnum.Canceled;
            activityCancelSuccess = ZSMallStatusCodeEnum.ACTIVITY_CANCEL_SUCCESS;
        }
        productActivity.setActivityState(activityStateEnum);
        iProductActivityService.updateById(productActivity);
        return R.ok(activityCancelSuccess);
    }


}
