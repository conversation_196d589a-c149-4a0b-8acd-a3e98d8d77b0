package com.zsmall.activity.biz.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.activity.biz.support.StorageFeeSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025年8月29日  16:38
 * @description: 仓储费处理任务类
 */
@Slf4j
@Service
public class StorageFeeHandlerJob {

    @Resource
    StorageFeeSupport storageFeeSupport;

    /**
     * 每日仓储费明细数据生成任务
     */
    @XxlJob("storageFeeItemGenerate")
    public void orderLogisticsAttachmentExceptionHandler() {
        storageFeeSupport.storageFee();
    }

    /**
     * 主仓储费生成任务
     */
    @XxlJob("storageFeeMainGenerate")
    public void storageFeeMainGenerate() {
        storageFeeSupport.storageFeeMain(null);
    }
}
