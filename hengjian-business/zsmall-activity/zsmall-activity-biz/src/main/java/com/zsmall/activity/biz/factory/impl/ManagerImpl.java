package com.zsmall.activity.biz.factory.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.biz.factory.ProductActivityFactory;
import com.zsmall.activity.biz.factory.ProductActivityFactoryService;
import com.zsmall.activity.biz.support.ProductActivitySupport;
import com.zsmall.activity.entity.domain.ProductActivity;
import com.zsmall.activity.entity.domain.ProductActivityReviewRecord;
import com.zsmall.activity.entity.domain.bo.productActivity.ActivityUpdateBo;
import com.zsmall.activity.entity.domain.bo.productActivity.ProductActivityBaseBo;
import com.zsmall.activity.entity.domain.bo.productActivity.ProductActivityQueryBo;
import com.zsmall.activity.entity.domain.vo.productActivity.ProductActivityDetailBaseVo;
import com.zsmall.activity.entity.domain.vo.productActivity.ProductActivityDetailVo;
import com.zsmall.activity.entity.domain.vo.productActivity.ProductActivityDraftVo;
import com.zsmall.activity.entity.domain.vo.productActivity.ProductActivityQueryVo;
import com.zsmall.activity.entity.iservice.IProductActivityItemService;
import com.zsmall.activity.entity.iservice.IProductActivityReviewRecordService;
import com.zsmall.activity.entity.iservice.IProductActivityService;
import com.zsmall.common.enums.productActivity.ProductActivityReviewState;
import com.zsmall.common.enums.productActivity.ProductActivityStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.extend.utils.ZSMallProductEventUtils;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 商品活动工厂-管理员相关实现
 *
 * <AUTHOR>
 * @date 2023/7/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ManagerImpl implements ProductActivityFactoryService {

    private final IProductSkuService iProductSkuService;
    private final IProductActivityService iProductActivityService;
    private final IProductActivityItemService iProductActivityItemService;
    private final IProductActivityReviewRecordService iProductActivityReviewRecordService;
    private final IProductSkuStockService iProductSkuStockService;

    private final ProductActivitySupport productActivitySupport;

    @Override
    public void afterPropertiesSet() throws Exception {
        ProductActivityFactory.register(TenantType.Manager, this);
    }

    /**
     * 分页查询商品活动列表
     */
    @Override
    public TableDataInfo getProductActivityPage(ProductActivityQueryBo bo, PageQuery pageQuery) {
        bo.setExcludeActivityState(ProductActivityStateEnum.Draft.getValue());
        IPage<ProductActivityQueryVo> page = iProductActivityService.queryPage(bo, pageQuery.build());
        return TableDataInfo.build(page);
    }

    /**
     * 创建活动
     *
     * @param bo
     */
    @Override
    public R<Void> createActivity(ProductActivityBaseBo bo) {
        throw new RStatusCodeException(ZSMallStatusCodeEnum.NO_HANDLE_PERMISSION);
    }

    /**
     * 查询商品活动草稿
     *
     * @param activityID
     */
    @Override
    public R<ProductActivityDraftVo> getActivityDraft(String activityID) {
        throw new RStatusCodeException(ZSMallStatusCodeEnum.NO_HANDLE_PERMISSION);
    }

    /**
     * 查询商品活动详情
     *
     * @param activityID
     */
    @Override
    public R<ProductActivityDetailBaseVo> getActivityDetail(String activityID) {
        ProductActivityDetailVo productActivityDetailVo = iProductActivityService.queryDetailVoManager(activityID);

        // String productSkuCode = productActivityDetailVo.getProductSkuCode();
        // ProductSkuAndStockVo productSkuAndStockVo = iProductSkuService.queryProductSkuAndStockByCode(productSkuCode);
        // ActivitySimpleProductInfoVo activitySimpleProductInfoVo = BeanUtil.toBean(productSkuAndStockVo, ActivitySimpleProductInfoVo.class);
        // productActivityDetailVo.setProduct(CollUtil.newArrayList(activitySimpleProductInfoVo));

        return R.ok(productActivityDetailVo);
    }

    /**
     * 更新活动状态
     */
    @Override
    public R<Void> updateState(ActivityUpdateBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Manager);

        String activityID = bo.getActivityID();
        String activityState = bo.getActivityState();
        Boolean approved = bo.getApproved();
        if (approved == null) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        //状态校验
        ProductActivityStateEnum activityStateEnum = ProductActivityStateEnum.valueOf(activityState);

        //获取活动信息
        ProductActivity productActivity = iProductActivityService.queryOneByEntity(ProductActivity.builder().activityCode(activityID).activityState(activityStateEnum).build());
        if (productActivity == null) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST);
        }

        if (!ProductActivityStateEnum.UnderReview.equals(activityStateEnum) && !ProductActivityStateEnum.Canceling.equals(activityStateEnum)) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_CANNOT_UPDATE_STATUS);
        }

        ProductActivityReviewRecord productActivityReviewRecord = iProductActivityReviewRecordService.queryByProductActivityIdAndReviewState(productActivity.getId(), ProductActivityReviewState.Pending);
        if (productActivityReviewRecord == null) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST_OR_CANCELLED);
        }
        String productSkuCode = productActivity.getProductSkuCode();

        //更新活动状态
        if (ProductActivityStateEnum.UnderReview.equals(activityStateEnum)) {
            if (approved) {
                productActivity.setActivityState(ProductActivityStateEnum.Published);
                ZSMallProductEventUtils.esProductSkuUpload(productSkuCode);
            } else {
                productActivity.setActivityState(ProductActivityStateEnum.NotApproved);
            }
        }

        if (ProductActivityStateEnum.Canceling.equals(activityStateEnum)) {
            if (approved) {
                productActivity.setActivityState(ProductActivityStateEnum.Canceled);
            } else {
                productActivity.setActivityState(ProductActivityStateEnum.InProgress);
            }
        }
        TenantHelper.ignore(() -> iProductActivityService.updateById(productActivity));

        // 取消成功 or 未通过审核 返还库存
        if (ProductActivityStateEnum.NotApproved.equals(productActivity.getActivityState()) || ProductActivityStateEnum.Canceled.equals(productActivity.getActivityState())) {
            productActivitySupport.cancelActivity(productActivity);
        }

        // 更新审核记录
        productActivityReviewRecord.setReviewManager(loginUser.getTenantId());
        productActivityReviewRecord.setReviewTime(new Date());
        if (approved) {
            productActivityReviewRecord.setReviewState(ProductActivityReviewState.Accepted);
        } else {
            productActivityReviewRecord.setReviewState(ProductActivityReviewState.Rejected);
        }
        TenantHelper.ignore(() -> iProductActivityReviewRecordService.updateById(productActivityReviewRecord), TenantType.Manager);
        return R.ok();
    }
}
