package com.zsmall.activity.test;

import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.productActivity.ProductActiveImportErrorEnum;

/**
 * 测试国际化消息是否能正确拼接中文参数
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
public class LocaleMessageTest {

    public static void main(String[] args) {
        System.out.println("=== 测试国际化消息拼接（统一【】格式 + 换行）===");

        // 测试带行号的错误消息格式（统一【】格式）
        LocaleMessage message1 = new LocaleMessage();
        message1.append(ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY.args("【第2行】数据异常：仓库编号\n"));
        System.out.println("FIELD_CANNOT_EMPTY 测试（统一格式）:");
        System.out.println("中文: " + message1.getZh_CN().toString());
        System.out.println("英文: " + message1.getEn_US().toString());
        System.out.println("JSON: " + message1.toJSONStr());
        System.out.println();

        // 测试 OBJECT_NOT_EXIST
        LocaleMessage message2 = new LocaleMessage();
        message2.append(ProductActiveImportErrorEnum.OBJECT_NOT_EXIST.args("【第3行】数据异常：商品不存在\n"));
        System.out.println("OBJECT_NOT_EXIST 测试（统一格式）:");
        System.out.println("中文: " + message2.getZh_CN().toString());
        System.out.println("英文: " + message2.getEn_US().toString());
        System.out.println("JSON: " + message2.toJSONStr());
        System.out.println();

        // 测试 FIELD_FORMAT_ERROR
        LocaleMessage message3 = new LocaleMessage();
        message3.append(ProductActiveImportErrorEnum.FIELD_FORMAT_ERROR.args("【第4行】数据异常：活动期限最小为30天\n"));
        System.out.println("FIELD_FORMAT_ERROR 测试（统一格式）:");
        System.out.println("中文: " + message3.getZh_CN().toString());
        System.out.println("英文: " + message3.getEn_US().toString());
        System.out.println("JSON: " + message3.toJSONStr());
        System.out.println();

        // 测试数据一致性错误
        LocaleMessage message4 = new LocaleMessage();
        message4.append(ProductActiveImportErrorEnum.FIELD_VALUE_INCONSISTENT.args("【第5行】【活动单价】存在不同的值\n"));
        System.out.println("FIELD_VALUE_INCONSISTENT 测试（统一格式）:");
        System.out.println("中文: " + message4.getZh_CN().toString());
        System.out.println("英文: " + message4.getEn_US().toString());
        System.out.println("JSON: " + message4.toJSONStr());
        System.out.println();

        // 测试多个错误消息拼接（验证换行效果）
        LocaleMessage combinedMessage = new LocaleMessage();
        combinedMessage.append(ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY.args("【第2行】数据异常：活动名称\n"));
        combinedMessage.append(ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY.args("【第2行】数据异常：SKU ID\n"));
        combinedMessage.append(ProductActiveImportErrorEnum.OBJECT_NOT_EXIST.args("【第3行】数据异常：仓库不存在\n"));
        combinedMessage.append(ProductActiveImportErrorEnum.FIELD_VALUE_INCONSISTENT.args("【第4行】【活动单价】存在不同的值\n"));
        combinedMessage.append(ProductActiveImportErrorEnum.FIELD_VALUE_INCONSISTENT.args("【第5行】【尾程】存在不同的值\n"));
        System.out.println("组合消息测试（统一格式 + 换行）:");
        System.out.println("中文: " + combinedMessage.getZh_CN().toString());
        System.out.println("英文: " + combinedMessage.getEn_US().toString());
        System.out.println("JSON: " + combinedMessage.toJSONStr());
    }
}
