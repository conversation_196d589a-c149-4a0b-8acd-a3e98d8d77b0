<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zsmall-activity</artifactId>
        <groupId>com.zsmall</groupId>
        <version>${zsmall.version}</version>
    </parent>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.26</version>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-system-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-product-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall.extend.es</groupId>
            <artifactId>zsmall-extend-es</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dromara.easy-es</groupId>
            <artifactId>easy-es-core</artifactId>
            <version>3.0.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zsmall-activity-entity</artifactId>
    <groupId>com.zsmall</groupId>
    <name>ZS-Mall活动实体模块</name>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

</project>
