<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.activity.entity.mapper.SupplierProductActivityStockMapper">


    <select id="getBySupplierActivityCode"
            resultType="com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityStock">
        select *
        from supplier_product_activity_stock spas
        where del_flag=0
            and spas.supplier_activity_code = #{supplierProductActivityCode}
        <if test="supportedLogistics != null and supportedLogistics != ''">
            and spas.supported_logistics = #{supportedLogistics}
        </if>
    </select>

    <update id="updateQuantitySold">
        update supplier_product_activity_stock
        set quantity_sold = quantity_sold + #{quantityTotal},
            quantity_surplus = quantity_surplus - #{quantityTotal}
        where id = #{supplierActivityStockId}
    </update>

    <update id="updateQuantitySoldWithOptimisticLock">
        update supplier_product_activity_stock
        set quantity_sold = quantity_sold + #{quantityTotal},
            quantity_surplus = quantity_surplus - #{quantityTotal}
        where id = #{supplierActivityStockId}
          and quantity_surplus >= #{quantityTotal}
          and del_flag = 0
    </update>
</mapper>
