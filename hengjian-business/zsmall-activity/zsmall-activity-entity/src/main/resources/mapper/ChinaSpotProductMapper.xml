<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.activity.entity.mapper.ChinaSpotProductMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProduct">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="belongCategoryId" column="belong_category_id" jdbcType="BIGINT"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
            <result property="allSpec" column="all_spec" jdbcType="VARCHAR"/>
            <result property="possibleSpec" column="possible_spec" jdbcType="VARCHAR"/>
            <result property="selectableQuantity" column="selectable_quantity" jdbcType="INTEGER"/>
            <result property="forbiddenChannel" column="forbidden_channel" jdbcType="OTHER"/>
            <result property="skuQuantity" column="sku_quantity" jdbcType="INTEGER"/>
            <result property="minimumQuantity" column="minimum_quantity" jdbcType="INTEGER"/>
            <result property="reviewState" column="review_state" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,belong_category_id,
        product_name,product_code,all_spec,
        possible_spec,selectable_quantity,forbidden_channel,
        sku_quantity,minimum_quantity,review_state,
        delete_tenant_id,delete_time,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>

    <select id="countByProductCode" resultType="java.lang.Long">
        SELECT IFNULL(COUNT(csp.id), 0)
        FROM china_spot_product csp
        WHERE csp.product_code = #{productCode}
    </select>

    <select id="queryPage" resultMap="BaseResultMap">
        SELECT csp.*
        FROM china_spot_product csp
        WHERE csp.del_flag = '0'
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductCode')">
            AND csp.product_code LIKE #{queryValue}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'Sku')">
            AND EXISTS (SELECT 1 FROM china_spot_product_sku csps WHERE csps.china_spot_product_id = csp.id AND csps.del_flag = '0' AND csps.sku LIKE #{queryValue})
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductName')">
            AND csp.product_name LIKE #{queryValue}
        </if>
        ORDER BY csp.create_time DESC, csp.id DESC
    </select>
</mapper>
