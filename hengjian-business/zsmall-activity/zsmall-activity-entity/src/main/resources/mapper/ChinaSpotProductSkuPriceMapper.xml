<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.activity.entity.mapper.ChinaSpotProductSkuPriceMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductSkuPrice">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="chinaSpotProductId" column="china_spot_product_id" jdbcType="BIGINT"/>
            <result property="chinaSpotProductSkuId" column="china_spot_product_sku_id" jdbcType="BIGINT"/>
            <result property="price" column="price" jdbcType="DECIMAL"/>
            <result property="minimumQuantity" column="minimum_quantity" jdbcType="INTEGER"/>
            <result property="maximumQuantity" column="maximum_quantity" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
</mapper>
