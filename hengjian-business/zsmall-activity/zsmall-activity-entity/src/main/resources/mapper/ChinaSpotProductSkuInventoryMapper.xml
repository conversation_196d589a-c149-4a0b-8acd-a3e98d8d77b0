<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.activity.entity.mapper.ChinaSpotProductSkuInventoryMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductSkuStock">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="warehouseId" column="warehouse_id" jdbcType="BIGINT"/>
            <result property="warehouseSystemCode" column="warehouse_system_code" jdbcType="VARCHAR"/>
            <result property="warehouseBelongCity" column="warehouse_belong_city" jdbcType="VARCHAR"/>
            <result property="chinaSpotProductId" column="china_spot_product_id" jdbcType="BIGINT"/>
            <result property="chinaSpotProductSkuId" column="china_spot_product_sku_id" jdbcType="BIGINT"/>
            <result property="quantity" column="quantity" jdbcType="INTEGER"/>
            <result property="quantityTotal" column="quantity_total" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,warehouse_id,warehouse_system_code,
        warehouse_belong_city,china_spot_product_id,china_spot_product_sku_id,
        quantity,quantity_total,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>


    <select id="countQuantityTotalByProduct" resultType="java.lang.Long">
        SELECT IFNULL(SUM(cspsi.quantity_total), 0)
        FROM china_spot_product_sku_stock cspsi
        WHERE cspsi.china_spot_product_id = #{chinaSpotProductId}
          AND cspsi.del_flag = '0'
    </select>


</mapper>
