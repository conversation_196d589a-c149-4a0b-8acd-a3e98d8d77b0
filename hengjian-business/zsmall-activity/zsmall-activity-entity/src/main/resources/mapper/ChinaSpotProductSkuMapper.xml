<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.activity.entity.mapper.ChinaSpotProductSkuMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductSku">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="chinaSpotProductId" column="china_spot_product_id" jdbcType="BIGINT"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="skuSpec" column="sku_spec" jdbcType="VARCHAR"/>
            <result property="fixedSpecValue" column="fixed_spec_value" jdbcType="VARCHAR"/>
            <result property="referencePrice" column="reference_price" jdbcType="DECIMAL"/>
            <result property="productSkuName" column="product_sku_name" jdbcType="VARCHAR"/>
            <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="length" column="length" jdbcType="DECIMAL"/>
            <result property="width" column="width" jdbcType="DECIMAL"/>
            <result property="height" column="height" jdbcType="DECIMAL"/>
            <result property="lengthUnit" column="length_unit" jdbcType="VARCHAR"/>
            <result property="weight" column="weight" jdbcType="DECIMAL"/>
            <result property="weightUnit" column="weight_unit" jdbcType="VARCHAR"/>
            <result property="packLength" column="pack_length" jdbcType="DECIMAL"/>
            <result property="packWidth" column="pack_width" jdbcType="DECIMAL"/>
            <result property="packHeight" column="pack_height" jdbcType="DECIMAL"/>
            <result property="packLengthUnit" column="pack_length_unit" jdbcType="VARCHAR"/>
            <result property="packWeight" column="pack_weight" jdbcType="DECIMAL"/>
            <result property="packWeightUnit" column="pack_weight_unit" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,china_spot_product_id,sku,
        sku_spec,fixed_spec_value,reference_price,
        product_sku_name,product_sku_code,description,
        length,width,height,
        length_unit,weight,weight_unit,
        pack_length,pack_width,pack_height,
        pack_length_unit,pack_weight,pack_weight_unit,
        del_flag,create_by,create_time,
        update_by,update_time
    </sql>

    <select id="countBySku" resultType="java.lang.Long">
        SELECT IFNULL(COUNT(csps.id), 0)
        FROM china_spot_product_sku csps
                 JOIN china_spot_product csp on csps.china_spot_product_id = csp.id
        WHERE csps.sku = #{sku}
          AND csps.del_flag = '0'
          AND csp.del_flag = '0'
    </select>

    <select id="countByProductSkuCode" resultType="java.lang.Long">
        SELECT IFNULL(COUNT(csps.id), 0)
        FROM china_spot_product_sku csps
                 JOIN china_spot_product csp on csps.china_spot_product_id = csp.id
        WHERE csps.product_sku_code = #{productSkuCode}
    </select>
</mapper>
