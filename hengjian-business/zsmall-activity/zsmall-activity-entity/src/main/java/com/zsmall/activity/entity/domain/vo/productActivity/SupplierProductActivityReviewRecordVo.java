package com.zsmall.activity.entity.domain.vo.productActivity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityReviewRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * 供应商锁货活动审核记录视图对象 supplier_product_activity_review_record
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SupplierProductActivityReviewRecord.class)
public class SupplierProductActivityReviewRecordVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商品活动表（供货商）主键
     */
    @ExcelProperty(value = "商品活动表", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "供=货商")
    private Long supplierProductActivityId;

    /**
     * 活动初始状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）
     */
    @ExcelProperty(value = "活动初始状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "D=raft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消")
    private String activityOriginState;

    /**
     * 审核租户编号（管理员）
     */
    @ExcelProperty(value = "审核租户编号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "管=理员")
    private String reviewManager;

    /**
     * 审核时间
     */
    @ExcelProperty(value = "审核时间")
    private Date reviewTime;

    /**
     * 审核意见
     */
    @ExcelProperty(value = "审核意见")
    private String reviewOpinion;

    /**
     * 审核状态（1-审核中，2-通过，3-驳回）
     */
    @ExcelProperty(value = "审核状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-审核中，2-通过，3-驳回")
    private Long reviewState;


}
