package com.zsmall.activity.entity.domain.dto.productActivity.erp;

import lombok.Data;

/**
 * Len
 * ERP解锁库存响应体
 * <AUTHOR>
 */
@Data
public class ErpProductLockInventoryReleaseResponse {

    /**
     * 状态码
     */
    private Integer statusCode;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     * 库存预留实体ID
     */
    private Long data;
}
