package com.zsmall.activity.entity.domain.dto.productActivity.erp;

import lombok.Data;
import java.util.List;

/**
 * ERP锁库存请求体
 * <AUTHOR>
 */
@Data
public class ErpProductLockInventoryReservedRequest {

    /**
     * 仓库编码
     */
    private String orgWarehouseCode;

    /**
     * 库存类型
     * 0:正常库存; 1:不可售库存
     */
    private Integer inventoryType;

    /**
     * 来源类型
     * 分销商城: 90
     */
    private Integer srcLabel;

    /**
     * 来源单号(固定传值活动供应商租户id)
     */
    private String sourceNo;

    /**
     * 渠道名称
     */
    private String sourceChannel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作时间
     */
    private String operateAt;

    /**
     * 产品项目列表
     */
    private List<ProductItemDto> productItemList;

    @Data
    public static class ProductItemDto {

        /**
         * 数量
         */
        private Integer quantity;

        /**
         * 预留追踪号
         */
        private String trackCode;

        /**
         * 产品编码
         */
        private String sku;
    }
}
