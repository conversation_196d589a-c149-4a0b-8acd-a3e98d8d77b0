package com.zsmall.activity.entity.domain.dto.productActivity;

import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * 活动列表查询统一DTO
 */
@Data
public class ProductActivitySearchDTO {
    /**
     * 活动名称（分货商）
     */
    private String activityName;
    /**
     * 商品名称
     */
    private String productName;

    /**
     * 租户编号（供货商）
     */
    private String supplierTenantId;

    /**
     * 活动编号（供货商）
     */
    private String supplierActivityCode;
    /**
     * 租户编号（分货商）
     */
    private String distributorTenantId;

    /**
     * 活动编号（分销商）
     */
    private String distributorActivityCode;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 活动状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）
     */
    private String activityState;

    /**
     * 商品Sku唯一编码
     */
    private String productSkuCode;
    /**
     * 商品Sku
     */
    private String productSku;
    /**
     * 站点
     */
    private String site;

    /**
     * 币种
     */
    private String currencySymbol;

    /**
     * 商品唯一编码
     */
    private String productCode;
    /**
     * 供应商活动编号集合(用于导出勾选)
     */
    private Set<String> supplierActivityCodes;
    /**
     * 分销商供应商活动编号集合(用于导出勾选)
     */
    private Set<String> distributorActivityCodes;

    private String tenantId;
    private String tenantType;

}
