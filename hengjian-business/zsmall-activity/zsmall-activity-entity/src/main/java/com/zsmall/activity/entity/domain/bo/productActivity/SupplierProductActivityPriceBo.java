package com.zsmall.activity.entity.domain.bo.productActivity;


import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityPrice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 供应商商品活动定价业务对象 supplier_product_activity_price
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SupplierProductActivityPrice.class, reverseConvertGenerate = false)
public class SupplierProductActivityPriceBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 供货商活动编号
     */
    @NotBlank(message = "供货商活动编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supplierActivityCode;

    /**
     * 供应商租户id
     */
    @NotBlank(message = "供应商租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supplierTenantId;

    /**
     * 商品活动表（供货商）主键
     */
    @NotNull(message = "商品活动表（供货商）主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long supplierProductActivityId;

    /**
     * 供货商活动单价（供货商设置）
     */
    @NotNull(message = "供货商活动单价（供货商设置）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal supplierActivityUnitPrice;

    /**
     * 供货商活动操作费（供货商设置）
     */
    @NotNull(message = "供货商活动操作费（供货商设置）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal supplierActivityOperationFee;

    /**
     * 供货商活动尾程派送费（供货商设置）
     */
    @NotNull(message = "供货商活动尾程派送费（供货商设置）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal supplierActivityFinalDeliveryFee;



    /**
     * 供货商活动自提价（活动单价+活动操作费）
     */
    @NotNull(message = "供货商活动自提价（活动单价+活动操作费）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal supplierActivityPickUpPrice;

    /**
     * 供货商活动代发价（活动自提价+活动尾程派送费）
     */
    @NotNull(message = "供货商活动代发价（活动自提价+活动尾程派送费）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal supplierActivityDropShippingPrice;


}
