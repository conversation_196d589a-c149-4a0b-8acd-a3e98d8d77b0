package com.zsmall.activity.entity.domain.dto.productActivity.export;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 分销商活动详情导出
 * <AUTHOR>
 */
@Data
public class DistributorProductActivityDetailsExportDTO {
    @ExcelProperty("活动ID")
    private String distributorActivityCode;
    /**
     * 活动类型
     */
    @ExcelProperty("活动类型")
    private String activityType;
    /**
     * 活动名称
     */
//    @ExcelProperty("活动名称")
//    private String activityName;

    @ExcelProperty("活动SKU ID")
    private String productSkuCode;

    @ExcelProperty("活动发货方式")
    private String supportedLogistics;
    /**
     * 已支付总押金
     */
    @ExcelProperty("已支付总押金")
    private BigDecimal depositPaidTotal;

    /**
     * 已支付总仓储费
     */
    @ExcelProperty("已支付总仓储费")
    private BigDecimal storageFeePaidTotal;
    /**
     * 仓库编码
     */
    @ExcelProperty("锁货仓库")
    private String warehouseCode;


    @ExcelProperty("仓库锁货总数量")
    private Long quantityTotal;

    /**
     * 锁货剩余数量
     */
    @ExcelProperty("仓库锁货剩余数量")
    private Long quantitySurplus;



}
