package com.zsmall.activity.entity.domain;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 交易记录仓储费关联对象 transaction_storage_fee
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@TableName("transaction_storage_fee")
@Accessors(chain = true)
public class TransactionStorageFee implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 交易记录ID
     */
    private Long transactionsId;

    /**
     * 仓储费ID
     */
    private Long storageFeeId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
