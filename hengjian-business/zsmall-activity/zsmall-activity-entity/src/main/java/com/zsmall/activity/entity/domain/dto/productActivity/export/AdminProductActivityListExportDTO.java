package com.zsmall.activity.entity.domain.dto.productActivity.export;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AdminProductActivityListExportDTO {
    @ExcelProperty("活动ID")
    private String supplierActivityCode;
    /**
     * 租户编号（供货商）
     */
    @ExcelProperty("供应商ID")
    private String supplierTenantId;
    /**
     * 活动类型
     */
    @ExcelProperty("活动类型")
    private String activityType;
    /**
     * 活动名称
     */
    @ExcelProperty("活动名称")
    private String activityName;

    /**
     * 商品名称
     */
    @ExcelProperty("商品名称")
    private String productName;


    @ExcelProperty("SKU ID")
    private String productSkuCode;

    /**
     * 站点
     */
    @ExcelProperty("活动/币种")
    private String site;
    /**
     * 活动锁货库存剩余数量
     */
    @ExcelProperty("剩余可用库存")
    private Long quantityLockedRemaining;

    /**
     * 供货商活动单价（供货商设置）
     */
    @ExcelProperty("活动单价")
    private BigDecimal supplierActivityUnitPrice;

    /**
     * 供货商活动操作费（供货商设置）
     */
    @ExcelProperty("活动操作费")
    private BigDecimal supplierActivityOperationFee;

    /**
     * 供货商活动尾程派送费（供货商设置）
     */
    @ExcelProperty("活动尾程派送费")
    private BigDecimal supplierActivityFinalDeliveryFee;

    /**
     * 活动最小起订量
     */
    @ExcelProperty("最小起订量(件)")
    private Long quantityMinimum;

    @ExcelProperty("仓储费(每天/每件)")
    private BigDecimal supplierActivityStorageFee;
    /**
     * 免仓期
     */
    @ExcelProperty("免仓期(天)")
    private Long freeStoragePeriod;

    /**
     * 活动天数
     */
    @ExcelProperty("活动期限(天)")
    private Long activityDay;

    @ExcelProperty("状态")
    private String activityState;

    @ExcelProperty("创建时间")
    private Date  createTime;

    /**
     * 活动自提锁货库存总数
     */
    @ExcelIgnore
    private Long pickupQuantityLocked;

    /**
     * 活动代发锁货库存总数
     */
    @ExcelIgnore
    private Long dropShippingQuantityLocked;
    /**
     * 活动自提锁货库存已使用数量
     */
    @ExcelIgnore
    private Long pickupLockedUsed;
    /**
     * 活动代发锁货库存已使用数量
     */
    @ExcelIgnore
    private Long dropShippingLockedUsed;
    /**
     * 币种
     */
    @ExcelIgnore
    private String currencySymbol;
}
