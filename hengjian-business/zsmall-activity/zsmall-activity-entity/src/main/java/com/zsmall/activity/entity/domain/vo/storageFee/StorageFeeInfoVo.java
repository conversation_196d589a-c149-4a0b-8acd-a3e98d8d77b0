package com.zsmall.activity.entity.domain.vo.storageFee;

import java.math.BigDecimal;
import java.util.Date;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.activity.entity.domain.StorageFeeInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;


import java.io.Serializable;



/**
 * 仓储费主视图对象 storage_fee_info
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StorageFeeInfo.class)
public class StorageFeeInfoVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "分销商ID")
    private String tenantId;

    /**
     * 仓储费ID
     */
    @ExcelProperty(value = "仓储费ID")
    private String storageFeeId;

    /**
     * 费用状态
     */
    @ExcelIgnore
    private Integer feeState;

    @ExcelProperty(value = "费用状态")
    private String feeStateString;

    /**
     * 币种code
     */
    @ExcelProperty(value = "币种")
    private String currencyCode;

    /**
     * 币种符号
     */
    @ExcelIgnore
    private String currencySymbol;

    /**
     * 总仓储费
     */
    @ExcelIgnore
    private BigDecimal totalStorageFee;

    @ExcelProperty(value = "总仓储费")
    private String totalStorageFeeString;

    /**
     * 仓储费结算时间
     */
    @ExcelProperty(value = "仓储费结算时间")
    private String storageFeeSettlementDate;

    /**
     * 活动ID
     */
    @ExcelProperty(value = "关联活动ID")
    private String activityId;

    /**
     * 活动类型
     */
    @ExcelProperty(value = "关联活动类型")
    private String activityType;

    /**
     * 发货方式
     */
    @ExcelProperty(value = "关联发货方式")
    private String logisticsType;

    /**
     * 支付状态
     */
    @ExcelIgnore
    private Integer payState;

    @ExcelProperty(value = "支付状态")
    private String payStateString;

    /**
     * 支付时间
     */
    @ExcelProperty(value = "支付时间")
    private String payTime;

    /**
     * 发送时间
     */
    @ExcelProperty(value = "发送时间")
    private String sendTime;

    private JSONObject errorMessage;
}
