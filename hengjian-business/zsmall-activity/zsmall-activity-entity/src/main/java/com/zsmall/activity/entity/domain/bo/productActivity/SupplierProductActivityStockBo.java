package com.zsmall.activity.entity.domain.bo.productActivity;


import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityStock;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 供应商商品活动库存业务对象 supplier_product_activity_stock
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SupplierProductActivityStock.class, reverseConvertGenerate = false)
public class SupplierProductActivityStockBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 供应商活动编码
     */
    @NotBlank(message = "供应商活动编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supplierActivityCode;

    /**
     * 供应商活动表id
     */
    @NotNull(message = "供应商活动表id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long supplierProductActivityId;

    /**
     * 仓库系统编号
     */
    @NotBlank(message = "仓库系统编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseSystemCode;

    /**
     * 自提/代发
     */
    @NotBlank(message = "自提/代发不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supportedLogistics;

    /**
     * 锁货数量
     */
    @NotNull(message = "锁货数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long quantityTotal;

    /**
     * 锁货已售数量
     */
    @NotNull(message = "锁货已售数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long quantitySold;

    /**
     * 锁货剩余数量
     */
    @NotNull(message = "锁货剩余数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long quantitySurplus;


}
