package com.zsmall.activity.entity.domain.bo.storageFee;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.activity.entity.domain.StorageFeeItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 仓储费子业务对象 storage_fee_item
 *
 * <AUTHOR> Li
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StorageFeeItem.class, reverseConvertGenerate = false)
public class StorageFeeItemBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 仓储费ID
     */
    @NotBlank(message = "仓储费ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String storageFeeId;

    /**
     * 明细ID
     */
    @NotBlank(message = "明细ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String detailId;

    /**
     * 仓库code
     */
    @NotBlank(message = "仓库code不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseCode;

    /**
     * 仓库系统code唯一
     */
    @NotBlank(message = "仓库系统code唯一不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseSystemCode;

    /**
     * SKU仓库数量
     */
    @NotNull(message = "SKU仓库数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long skuNum;

    /**
     * SKU仓库剩余数量
     */
    @NotNull(message = "SKU仓库剩余数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long skuRemainingNum;

    /**
     * 仓储费
     */
    @NotNull(message = "仓储费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal storageFee;

    /**
     * 每天的仓储费总和
     */
    private BigDecimal storageFeeDay;

    /**
     * 分销系统唯一sku标识
     */
    @NotBlank(message = "分销系统唯一sku标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private String skuId;

    /**
     * erpSku
     */
    @NotBlank(message = "erpSku不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sku;

    /**
     * 币种code
     */
    @NotBlank(message = "币种code不能为空", groups = { AddGroup.class, EditGroup.class })
    private String currencyCode;

    /**
     * 币种符号
     */
    @NotBlank(message = "币种符号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String currencySymbol;

    /**
     * 站点
     */
    @NotBlank(message = "站点不能为空", groups = { AddGroup.class, EditGroup.class })
    private String site;

    /**
     * 活动ID
     */
    @NotBlank(message = "活动ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String activityId;

    /**
     * 明细仓储费结算时间
     */
    @NotNull(message = "明细仓储费结算时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String feeSettlementDate;

    /**
     * 明细仓储费结算时间开始
     */
    private String feeSettlementDateStart;

    /**
     *  明细仓储费结算时间结束
     */
    private String feeSettlementDateEnd;

}
