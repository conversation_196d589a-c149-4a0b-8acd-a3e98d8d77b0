package com.zsmall.activity.entity.domain.dto.productActivity;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 分销商活动添加
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
public class DistributorProductActivityAddDTO {

    /**
     * 供应商活动编码
     */
    @NotNull(message = "供应商活动编码不能为空")
    private String supplierActivityCode;

    /**
     * 自提/代发
     */
    @NotNull(message = "发货方式不能为空")
    private String supportedLogistics;

    /**
     * 活动仓库库存
     */
    @NotEmpty(message = "活动仓库库存不能为空")
    @Valid
    private List<DistributorProductActivityStockAddDTO> stockList;
    @Data
    public static  class DistributorProductActivityStockAddDTO {
        /**
         * 供应商活动库存id
         */
        @NotNull(message = "供应商活动库存id不能为空")
        private Long supplierActivityStockId;
        /**
         * 仓库系统编号
         */
        private String warehouseSystemCode;
        /**
         * 仓库编码
         */
        @NotBlank(message = "仓库编码不能为空")
        private String warehouseCode;

        /**
         * 锁货数量
         */
        @NotNull(message = "锁货数量不能为空")
        @Min(value = 1, message = "锁货数量最小为1")
        private int quantityTotal;
    }


}
