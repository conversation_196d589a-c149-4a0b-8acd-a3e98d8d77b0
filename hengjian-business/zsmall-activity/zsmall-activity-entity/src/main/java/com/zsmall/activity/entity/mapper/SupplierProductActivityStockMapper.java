package com.zsmall.activity.entity.mapper;


import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityStock;
import com.zsmall.activity.entity.domain.vo.productActivity.SupplierProductActivityStockVo;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 供应商商品活动库存Mapper接口
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
public interface SupplierProductActivityStockMapper extends BaseMapperPlus<SupplierProductActivityStock, SupplierProductActivityStockVo> {

    List<SupplierProductActivityStock> getBySupplierActivityCode(@NotNull(message = "供应商活动编码不能为空") String supplierProductActivityCode, String supportedLogistics);

    void updateQuantitySold(@NotNull(message = "锁货数量不能为空") @Min(value = 1, message = "锁货数量最小为1") int quantityTotal, @NotNull(message = "供应商活动库存id不能为空") Long supplierActivityStockId);

    /**
     * 使用乐观锁更新已分配数量，防止并发超卖
     * @param quantityTotal 要分配的数量
     * @param supplierActivityStockId 供应商活动库存ID
     * @return 更新影响的行数，0表示更新失败（库存不足或并发冲突）
     */
    int updateQuantitySoldWithOptimisticLock(@NotNull(message = "锁货数量不能为空") @Min(value = 1, message = "锁货数量最小为1") int quantityTotal, @NotNull(message = "供应商活动库存id不能为空") Long supplierActivityStockId);

}
