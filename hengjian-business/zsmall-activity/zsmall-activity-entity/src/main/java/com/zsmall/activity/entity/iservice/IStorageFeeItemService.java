package com.zsmall.activity.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.entity.domain.StorageFeeItem;
import com.zsmall.activity.entity.domain.bo.storageFee.StorageFeeItemBo;
import com.zsmall.activity.entity.domain.vo.storageFee.StorageFeeItemVo;
import com.zsmall.activity.entity.mapper.StorageFeeItemMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 仓储费子Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-03
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class IStorageFeeItemService extends ServiceImpl<StorageFeeItemMapper, StorageFeeItem> {

    private final StorageFeeItemMapper baseMapper;

    /**
     * 查询仓储费子列表
     */
    @InMethodLog("仓储费详情分页列表")
    public TableDataInfo<StorageFeeItemVo> queryPageList(StorageFeeItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StorageFeeItem> lqw = buildQueryWrapper(bo);
        Page<StorageFeeItemVo> result = TenantHelper.ignore(() -> baseMapper.selectVoPage(pageQuery.build(), lqw));
        return TableDataInfo.build(result);
    }

    @InMethodLog("查询仓储费明细数据根据仓储费ID集合")
    public List<StorageFeeItemVo> queryExportList(List<String> storageFeeIdList) {
        LambdaQueryWrapper<StorageFeeItem> lqw = Wrappers.lambdaQuery();
        lqw.in(StorageFeeItem::getStorageFeeId, storageFeeIdList);
        return TenantHelper.ignore(() ->baseMapper.selectVoList(lqw));
    }

    private LambdaQueryWrapper<StorageFeeItem> buildQueryWrapper(StorageFeeItemBo bo) {
        LambdaQueryWrapper<StorageFeeItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getStorageFeeId()), StorageFeeItem::getStorageFeeId, bo.getStorageFeeId());
        lqw.eq(StringUtils.isNotBlank(bo.getDetailId()), StorageFeeItem::getDetailId, bo.getDetailId());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseCode()), StorageFeeItem::getWarehouseCode, bo.getWarehouseCode());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseSystemCode()), StorageFeeItem::getWarehouseSystemCode, bo.getWarehouseSystemCode());
        lqw.eq(StringUtils.isNotBlank(bo.getSkuId()), StorageFeeItem::getSkuId, bo.getSkuId());
        lqw.eq(StringUtils.isNotBlank(bo.getSku()), StorageFeeItem::getSku, bo.getSku());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrencyCode()), StorageFeeItem::getCurrencyCode, bo.getCurrencyCode());
        lqw.eq(StringUtils.isNotBlank(bo.getSite()), StorageFeeItem::getSite, bo.getSite());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityId()), StorageFeeItem::getActivityId, bo.getActivityId());
        if(StringUtils.isNotBlank(bo.getFeeSettlementDateStart()) && StringUtils.isNotBlank(bo.getFeeSettlementDateEnd())){
            lqw.between(StorageFeeItem::getFeeSettlementDate, bo.getFeeSettlementDateStart(), bo.getFeeSettlementDateEnd());
        }
        return lqw;
    }

    @InMethodLog("查询仓库列表")
    public List<String> listWarehouseForSelect(String storageFeeId) {
        LambdaQueryWrapper<StorageFeeItem> lqw = Wrappers.lambdaQuery();
        lqw.select(StorageFeeItem::getWarehouseCode);
        lqw.eq(StorageFeeItem::getStorageFeeId, storageFeeId);
        List<StorageFeeItem> list = TenantHelper.ignore(() -> baseMapper.selectList(lqw));
        return list.stream().map(StorageFeeItem::getWarehouseCode).distinct().collect(Collectors.toList());
    }

}
