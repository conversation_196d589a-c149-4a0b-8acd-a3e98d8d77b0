package com.zsmall.activity.entity.domain.dto.productActivity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 商品活动erp库存锁定/释放日志对象 product_activity_stock_lock_release_log
 *
 * <AUTHOR> Li
 * @date 2025-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_activity_stock_lock_release_log")
public class ProductActivityStockLockReleaseLog extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;


    /**
     * 活动编码
     */
    private String activeCode;

    /**
     * 仓库编码
     */
    private String warehouseName;

    /**
     * 请求类型（[ERP库存锁定] 或 [ERP库存释放]）
     */
    private String requestType;

    /**
     * 请求成功/失败
     */
    private Boolean isSuccess;

    /**
     * 请求体
     */
    private String request;

    /**
     * 响应体
     */
    private String response;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;


}
