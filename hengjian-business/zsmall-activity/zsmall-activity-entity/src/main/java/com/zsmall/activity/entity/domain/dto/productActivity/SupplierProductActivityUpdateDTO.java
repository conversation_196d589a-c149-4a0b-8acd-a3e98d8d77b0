package com.zsmall.activity.entity.domain.dto.productActivity;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 供应商活动新增DTO
 * <AUTHOR>
 */
@Data
public class SupplierProductActivityUpdateDTO {

    /**
     * 活动编号（供货商）
     */
    @NotBlank(message = "活动编号不能为空")
    private String supplierActivityCode;

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空")
    @Size(max = 20, message = "活动名称长度不能超过20个字符")
    private String activityName;

    /**
     * 活动最小起订量
     */
    @NotNull(message = "活动最小起订量不能为空")
    @Min(value = 1, message = "最小起订量必须大于0")
    private Integer quantityMinimum;


    /**
     * 供货商活动仓储费（一件/天，供货商设置）
     */
    @NotNull(message = "供货商活动仓储费不能为空")
    private BigDecimal supplierActivityStorageFee;

    /**
     * 供货商活动单价（供货商设置）
     */
    @NotNull(message = "供货商活动单价不能为空")
    private BigDecimal supplierActivityUnitPrice;

    /**
     * 供货商活动操作费（供货商设置）
     */
    @NotNull(message = "供货商活动操作费不能为空")
    private BigDecimal supplierActivityOperationFee;

    /**
     * 供货商活动尾程派送费（供货商设置）
     */
    @NotNull(message = "供货商活动尾程派送费不能为空")
    private BigDecimal supplierActivityFinalDeliveryFee;

    @NotNull(message = "业务类型不能为空,1:保存 2:保存并提交审核")
    private Integer businessType;
}

