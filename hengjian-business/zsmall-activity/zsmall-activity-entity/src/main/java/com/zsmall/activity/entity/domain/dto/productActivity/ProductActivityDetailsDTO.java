package com.zsmall.activity.entity.domain.dto.productActivity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 活动详情统一DTO
 * <AUTHOR>
 */
@Data
public class ProductActivityDetailsDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户编号（供货商）
     */
    private String supplierTenantId;
    /**
     * 租户编号（分货商）
     */
    private String distributorTenantId;

    /**
     * 供应商活动id
     */
    private Long supplierActivityId;

    /**
     * 供应商活动编码
     */
    private String supplierActivityCode;
    /**
     * 分销商活动id
     */
    private Long distributorActivityId;
    /**
     * 活动编号（分销商）
     */
    private String distributorActivityCode;
    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 活动状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）
     */
    private String activityState;

    /**
     * 审核状态
     */
    private String reviewState;

    /**
     * 审核意见
     */
    private String reviewOpinion;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品图片
     */
    private String productImg;

    /**
     * 商品唯一编码
     */
    private String productCode;

    /**
     * 商品Sku唯一编码
     */
    private String productSkuCode;

    /**
     * 商品Sku(ERP SKU)
     */
    private String productSku;

    /**
     * 站点
     */
    private String site;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 活动天数
     */
    private Integer activityDay;

    /**
     * 免仓期
     */
    private Integer freeStoragePeriod;

    /**
     * 活动自提锁货库存总数(锁货)
     */
    private Integer pickupQuantityLocked;

    /**
     * 活动代发锁货库存总数(锁货)
     */
    private Integer dropShippingQuantityLocked;

    /**
     * 活动自提锁货库存已使用数量(锁货)
     */
    private Integer pickupLockedUsed;

    /**
     * 活动代发锁货库存已使用数量(锁货)
     */
    private Integer dropShippingLockedUsed;

    /**
     * 活动最小起订量
     */
    private Integer quantityMinimum;

    /**
     * 活动sku出单总数(订单)
     */
    private Integer orderedTotal;

    /**
     * 已支付总押金
     */
    private BigDecimal depositPaidTotal;

    /**
     * 已支付总仓储费
     */
    private BigDecimal storageFeePaidTotal;

    /**
     * 供货商活动仓储费（一件/天，供货商设置
     */
    private BigDecimal supplierActivityStorageFee;

    /**
     * 活动异常 0无异常
     */
    private Integer exceptionCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private Integer delFlag;

    /**
     * 供货商活动单价（供货商设置）
     */
    private BigDecimal activityUnitPrice;

    /**
     * 供货商活动操作费（供货商设置）
     */
    private BigDecimal activityOperationFee;

    /**
     * 供货商活动尾程派送费（供货商设置）
     */
    private BigDecimal activityFinalDeliveryFee;

    /**
     * 供货商活动自提价（活动单价活动操作费）
     */
    private BigDecimal activityPickUpPrice;

    /**
     * 供货商活动代发价（活动自提价活动尾程派送费）
     */
    private BigDecimal activityDropShippingPrice;
    /**
     * 活动开始时间
     */
    private Date activeStartTime;

    /**
     * 活动结束时间
     */
    private Date activeEndTime;
    /**
     * 发货方式
     */
    private String supportedLogistics;

    /**
     * 商品自提库存
     */
    private Integer productSkuPickUpStock;
    /**
     * 商品代发库存
     */
    private Integer productSkuDropShippingStock;

    /**
     * 商品自提价格
     */
    private BigDecimal productSkuPickUpPrice;
    /**
     * 商品代发价格
     */
    private BigDecimal productSkuDropShippingPrice;

    /**
     * 是否存在进行中的分销商活动(用于前端供应商活动取消按钮展示)
     */
    private Boolean hasInProgressDisActivity;

    /**
     * 活动库存
     */
    private List<ProductActivityStockDTO> productActivityStocks;

    /**
     * 分销商活动详情
     */
    private List<DistributorProductActivityDetails> distributorProductActivityDetails;

}
