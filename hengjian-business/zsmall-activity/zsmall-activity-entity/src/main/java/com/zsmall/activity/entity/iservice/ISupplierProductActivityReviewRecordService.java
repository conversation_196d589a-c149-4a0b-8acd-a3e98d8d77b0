package com.zsmall.activity.entity.iservice;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityReviewRecord;
import com.zsmall.activity.entity.mapper.SupplierProductActivityReviewRecordMapper;
import org.springframework.stereotype.Service;

/**
 * 供应商锁货活动审核记录Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Service
public class ISupplierProductActivityReviewRecordService extends ServiceImpl<SupplierProductActivityReviewRecordMapper, SupplierProductActivityReviewRecord> {

}
