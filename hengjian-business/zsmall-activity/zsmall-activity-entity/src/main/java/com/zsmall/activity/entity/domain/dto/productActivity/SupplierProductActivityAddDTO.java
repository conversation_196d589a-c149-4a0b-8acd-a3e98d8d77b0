package com.zsmall.activity.entity.domain.dto.productActivity;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 供应商活动新增DTO
 * <AUTHOR>
 */
@Data
public class SupplierProductActivityAddDTO {

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空")
    @Size(max = 20, message = "活动名称长度不能超过20个字符")
    private String activityName;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String productName;

    /**
     * 商品图片
     */
    @NotBlank(message = "商品图片不能为空")
    private String productImg;

    /**
     * 商品唯一编码
     */
    @NotBlank(message = "商品唯一编码不能为空")
    private String productCode;

    /**
     * 商品Sku唯一编码
     */
    @NotBlank(message = "商品Sku唯一编码不能为空")
    private String productSkuCode;

    /**
     * 商品Sku
     */
    @NotBlank(message = "商品Sku不能为空")
    private String productSku;


    /**
     * 站点
     */
    @NotBlank(message = "站点不能为空")
    private String site;

    /**
     * 币种
     */
    @NotBlank(message = "币种不能为空")
    private String currencySymbol;

    /**
     * 活动天数
     */
    @NotNull(message = "活动天数不能为空")
    @Min(value = 30, message = "活动期限必须大于等于30天")
    private Integer activityDay;

    /**
     * 免仓期
     */
    @NotNull(message = "免仓期不能为空")
    @Min(value = 0, message = "免仓期必须大于等于0")
    private Integer freeStoragePeriod;


    /**
     * 活动最小起订量
     */
    @NotNull(message = "活动最小起订量不能为空")
    @Min(value = 1, message = "最小起订量必须大于0")
    private Integer quantityMinimum;


    /**
     * 供货商活动仓储费（一件/天，供货商设置）
     */
    @NotNull(message = "供货商活动仓储费不能为空")
    private BigDecimal supplierActivityStorageFee;

    /**
     * 供货商活动单价（供货商设置）
     */
    @NotNull(message = "供货商活动单价不能为空")
    private BigDecimal supplierActivityUnitPrice;

    /**
     * 供货商活动操作费（供货商设置）
     */
    @NotNull(message = "供货商活动操作费不能为空")
    private BigDecimal supplierActivityOperationFee;

    /**
     * 供货商活动尾程派送费（供货商设置）
     */
    @NotNull(message = "供货商活动尾程派送费不能为空")
    private BigDecimal supplierActivityFinalDeliveryFee;

    @NotNull(message = "业务类型不能为空,1:保存 2:保存并提交审核")
    private Integer businessType;

    /**
     * 活动仓库库存
     */
    @NotEmpty(message = "活动仓库库存不能为空")
    @Valid
    private List<SupplierProductActivityStockAddDTO> stockList;
    @Data
    public static  class SupplierProductActivityStockAddDTO {

        @NotNull(message = "商品库存id不能为空")
        private Long productSkuStockId;
        /**
         * 仓库系统编号
         */
        private String warehouseSystemCode;
        /**
         * 仓库编码
         */
        @NotBlank(message = "仓库编码不能为空")
        private String warehouseCode;

        /**
         * 自提/代发
         */
        @NotBlank(message = "自提/代发不能为空")
        private String supportedLogistics;

        /**
         * 锁货数量
         */
        @NotNull(message = "锁货数量不能为空")
        @Min(value = 1, message = "锁货数量最小为1")
        private Integer quantityTotal;
    }
}

