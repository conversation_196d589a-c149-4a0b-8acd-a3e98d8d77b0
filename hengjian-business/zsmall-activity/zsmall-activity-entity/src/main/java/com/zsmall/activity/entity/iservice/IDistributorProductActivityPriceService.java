package com.zsmall.activity.entity.iservice;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityPrice;
import com.zsmall.activity.entity.mapper.DistributorProductActivityPriceMapper;
import org.springframework.stereotype.Service;

/**
 * 分销商商品活动定价Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Service
public class IDistributorProductActivityPriceService  extends ServiceImpl<DistributorProductActivityPriceMapper, DistributorProductActivityPrice> {


}
