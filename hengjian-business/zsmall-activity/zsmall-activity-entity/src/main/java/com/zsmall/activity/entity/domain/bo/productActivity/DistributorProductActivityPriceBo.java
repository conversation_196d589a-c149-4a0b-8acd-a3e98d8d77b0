package com.zsmall.activity.entity.domain.bo.productActivity;


import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityPrice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 分销商商品活动定价业务对象 distributor_product_activity_price
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DistributorProductActivityPrice.class, reverseConvertGenerate = false)
public class DistributorProductActivityPriceBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 分销商活动编号
     */
    @NotBlank(message = "分销商活动编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String distributorActivityCode;

    /**
     * 分销商商品活动表主键
     */
    @NotNull(message = "分销商商品活动表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long distributorProductActivityId;

    /**
     * 分销商租户id
     */
    @NotBlank(message = "分销商租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String distributorTenantId;

    /**
     * 平台单价（平台设置）
     */
    @NotNull(message = "平台单价（平台设置）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal distributorActivityUnitPrice;

    /**
     * 平台操作费（平台设置）
     */
    @NotNull(message = "平台操作费（平台设置）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal distributorActivityOperationFee;

    /**
     * 平台尾程派送费（平台设置）
     */
    @NotNull(message = "平台尾程派送费（平台设置）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal distributorActivityFinalDeliveryFee;

    /**
     * 平台自提价（平台单价+平台操作费）
     */
    @NotNull(message = "平台自提价（平台单价+平台操作费）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal distributorActivityPickUpPrice;

    /**
     * 平台代发价（平台自提价+平台尾程派送费）
     */
    @NotNull(message = "平台代发价（平台自提价+平台尾程派送费）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal distributorActivityDropShippingPrice;


}
