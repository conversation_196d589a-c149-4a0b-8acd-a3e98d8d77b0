package com.zsmall.activity.entity.domain.bo.productActivity;


import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 供货商商品活动业务对象 supplier_product_activity
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SupplierProductActivity.class, reverseConvertGenerate = false)
public class SupplierProductActivityBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 活动编号（供货商）
     */
    @NotBlank(message = "活动编号（供货商）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supplierActivityCode;

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String activityName;

    /**
     * 活动类型
     */
    @NotBlank(message = "活动类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String activityType;

    /**
     * 活动状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）
     */
    @NotBlank(message = "活动状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String activityState;

    /**
     * 活动审批状态
     */
    private String reviewState;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productName;

    /**
     * 商品图片
     */
    @NotBlank(message = "商品图片不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productImg;

    /**
     * 商品唯一编码
     */
    @NotBlank(message = "商品唯一编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productCode;

    /**
     * 商品Sku唯一编码
     */
    @NotBlank(message = "商品Sku唯一编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productSkuCode;

    /**
     * 商品Sku
     */
    @NotBlank(message = "商品Sku不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productSku;

//    /**
//     * 活动开始时间
//     */
//    @NotNull(message = "活动开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
//    private Date activeStartTime;
//
//    /**
//     * 活动结束时间
//     */
//    @NotNull(message = "活动结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
//    private Date activeEndTime;

    /**
     * 站点
     */
    @NotBlank(message = "站点不能为空", groups = { AddGroup.class, EditGroup.class })
    private String site;

    /**
     * 币种
     */
    @NotBlank(message = "币种不能为空", groups = { AddGroup.class, EditGroup.class })
    private String currency;

    /**
     * 活动天数
     */
    @NotNull(message = "活动天数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long activityDay;

    /**
     * 免仓期
     */
    @NotNull(message = "免仓期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long freeStoragePeriod;

    /**
     * 发货方式
     */
    @NotBlank(message = "发货方式不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supportedLogistics;

    /**
     * 活动锁货库存总数
     */
    @NotNull(message = "活动锁货库存总数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long quantityLocked;

    /**
     * 活动锁货库存已使用数量
     */
    @NotNull(message = "活动锁货库存已使用数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long quantityLockedUsed;

    /**
     * 活动最小起订量
     */
    @NotNull(message = "活动最小起订量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long quantityMinimum;

    /**
     * 活动sku出单总数(查询详情的时候通过es查询)
     */
    @NotNull(message = "活动sku出单总数(查询详情的时候通过es查询)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderedTotal;

    /**
     * 已支付总押金
     */
    @NotNull(message = "已支付总押金不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal depositPaidTotal;

    /**
     * 已支付总仓储费
     */
    @NotNull(message = "已支付总仓储费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal storageFeePaidTotal;

    /**
     * 供货商活动仓储费（一件/天，供货商设置）
     */
    @NotNull(message = "供货商活动仓储费（一件/天，供货商设置）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal supplierActivityStorageFee;
    /**
     * 活动审批意见
     */
    private String reviewOpinion;
    /**
     * 活动锁货库存剩余数量
     */
    private Long quantityLockedRemaining;
}
