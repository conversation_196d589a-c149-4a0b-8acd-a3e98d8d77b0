package com.zsmall.activity.entity.domain.dto.productActivity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;



/**
 * 供应商商品活动定价对象 supplier_product_activity_price
 *
 * <AUTHOR> Li
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("supplier_product_activity_price")
public class SupplierProductActivityPrice extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 供货商活动编号
     */
    private String supplierActivityCode;

    /**
     * 供应商租户id
     */
    private String supplierTenantId;

    /**
     * 商品活动表（供货商）主键
     */
    private Long supplierActivityId;

    /**
     * 供货商活动单价（供货商设置）
     */
    private BigDecimal supplierActivityUnitPrice;

    /**
     * 供货商活动操作费（供货商设置）
     */
    private BigDecimal supplierActivityOperationFee;

    /**
     * 供货商活动尾程派送费（供货商设置）
     */
    private BigDecimal supplierActivityFinalDeliveryFee;

    /**
     * 供货商活动自提价（活动单价活动操作费）
     */
    private BigDecimal supplierActivityPickUpPrice;

    /**
     * 供货商活动代发价（活动自提价活动尾程派送费）
     */
    private BigDecimal supplierActivityDropShippingPrice;

    /**
     * 删除标记
     */
    @TableLogic
    private Integer delFlag;


}
