package com.zsmall.activity.entity.domain.bo.productActivity;


import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 分销商商品活动库存业务对象 distributor_product_activity_stock
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DistributorProductActivityStock.class, reverseConvertGenerate = false)
public class DistributorProductActivityStockBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 分销商活动编码
     */
    @NotBlank(message = "分销商活动编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String distributorActivityCode;

    /**
     * 分销商活动表id
     */
    @NotNull(message = "分销商活动表id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long distributorProductActivityId;

    /**
     * 仓库系统编号
     */
    @NotBlank(message = "仓库系统编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseSystemCode;

    /**
     * 自提/代发
     */
    @NotBlank(message = "自提/代发不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supportedLogistics;

    /**
     * 锁货数量
     */
    @NotNull(message = "锁货数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long quantityTotal;

    /**
     * 锁货已售数量
     */
    @NotNull(message = "锁货已售数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long quantitySold;

    /**
     * 锁货剩余数量
     */
    @NotNull(message = "锁货剩余数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long quantitySurplus;


}
