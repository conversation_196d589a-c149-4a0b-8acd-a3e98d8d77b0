package com.zsmall.activity.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductSkuAttachment;
import com.zsmall.activity.entity.mapper.ChinaSpotProductSkuAttachmentMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【china_spot_product_sku_attachment(国内现货商品SKU附件表)】的数据库操作Service实现
* @createDate 2023-08-15 16:40:48
*/
@Service
public class IChinaSpotProductSkuAttachmentService extends ServiceImpl<ChinaSpotProductSkuAttachmentMapper, ChinaSpotProductSkuAttachment> {


    @InMethodLog(value = "根据国内现货商品id查询SKU附件")
    public List<ChinaSpotProductSkuAttachment> queryListByChinaSpotProductId(Long chinaSpotProductId) {
        return lambdaQuery().eq(ChinaSpotProductSkuAttachment::getChinaSpotProductId, chinaSpotProductId)
            .orderByAsc(ChinaSpotProductSkuAttachment::getId)
            .orderByAsc(ChinaSpotProductSkuAttachment::getAttachmentSort)
            .list();
    }


    @InMethodLog(value = "根据国内现货商品SkuId查询所有图片")
    public List<ChinaSpotProductSkuAttachment> queryBySpotProductSkuId(Long chinaSpotProductSkuId) {
        return lambdaQuery().eq(ChinaSpotProductSkuAttachment::getChinaSpotProductSkuId, chinaSpotProductSkuId)
            .orderByAsc(ChinaSpotProductSkuAttachment::getId)
            .orderByAsc(ChinaSpotProductSkuAttachment::getAttachmentSort)
            .list();
    }

}




