package com.zsmall.activity.entity.domain.dto.productActivity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;



/**
 * 供应商锁货活动审核记录对象 supplier_product_activity_review_record
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("supplier_product_activity_review_record")
public class SupplierProductActivityReviewRecord extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品活动表（供货商）主键
     */
    private Long supplierActivityId;

    /**
     * 活动初始状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）
     */
    private String activityOriginState;

    /**
     * 审核租户编号（管理员）
     */
    private String reviewManager;

    /**
     * 审核时间
     */
    private Date reviewTime;

    /**
     * 审核意见
     */
    private String reviewOpinion;

    /**
     * 审核状态
     */
    private String reviewState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;


}
