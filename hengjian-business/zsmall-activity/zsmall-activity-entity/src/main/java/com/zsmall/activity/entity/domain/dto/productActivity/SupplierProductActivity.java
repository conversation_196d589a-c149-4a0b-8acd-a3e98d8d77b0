package com.zsmall.activity.entity.domain.dto.productActivity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;



/**
 * 供货商商品活动对象 supplier_product_activity
 *
 * <AUTHOR> Li
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("supplier_product_activity")
public class SupplierProductActivity extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 租户编号（供货商）
     */
    private String supplierTenantId;

    /**
     * 活动编号（供货商）
     */
    private String supplierActivityCode;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 活动状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）
     */
    private String activityState;

    /**
     * 审核状态
     */
    private String reviewState;

    /**
     * 审核意见
     */
    private String reviewOpinion;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品图片
     */
    private String productImg;

    /**
     * 商品唯一编码
     */
    private String productCode;

    /**
     * 商品Sku唯一编码
     */
    private String productSkuCode;

    /**
     * 商品Sku(ERP SKU)
     */
    private String productSku;

    /**
     * 站点
     */
    private String site;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 活动天数
     */
    private Integer activityDay;

    /**
     * 免仓期
     */
    private Integer freeStoragePeriod;

    /**
     * 活动自提锁货库存总数(锁货)
     */
    private Integer pickupQuantityLocked;

    /**
     * 活动代发锁货库存总数(锁货)
     */
    private Integer dropShippingQuantityLocked;

    /**
     * 活动自提锁货库存已使用数量(锁货)
     */
    private Integer pickupLockedUsed;

    /**
     * 活动代发锁货库存已使用数量(锁货)
     */
    private Integer dropShippingLockedUsed;

    /**
     * 活动最小起订量
     */
    private Integer quantityMinimum;

    /**
     * 活动sku出单总数(订单)
     */
    private Integer orderedTotal;

    /**
     * 已支付总押金
     */
    private BigDecimal depositPaidTotal;

    /**
     * 已支付总仓储费
     */
    private BigDecimal storageFeePaidTotal;

    /**
     * 供货商活动仓储费（一件/天，供货商设置
     */
    private BigDecimal supplierActivityStorageFee;

    /**
     * 活动异常 0无异常
     */
    private Integer exceptionCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;


}
