package com.zsmall.activity.entity.domain.dto.productActivity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 活动库存统一DTO
 * 用于统一处理供应商活动库存和分销商活动库存
 * <AUTHOR>
 */
@Data
public class ProductActivityStockDTO {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品sku库存id（供应商活动专用）
     */
    private Long productSkuStockId;

    /**
     * 供应商活动编码（供应商活动专用）
     */
    private String supplierActivityCode;

    /**
     * 供应商活动表id（供应商活动专用）
     */
    private Long supplierActivityId;

    /**
     * 分销商活动编码（分销商活动专用）
     */
    private String distributorActivityCode;

    /**
     * 分销商活动表id（分销商活动专用）
     */
    private Long distributorActivityId;

    /**
     * 供应商仓库ID（分销商活动专用）
     */
    private Long supplierActivityStockId;

    /**
     * 仓库系统编号
     */
    private String warehouseSystemCode;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 自提/代发
     */
    private String supportedLogistics;

    /**
     * 锁货总数量(锁货)
     */
    private Integer quantityTotal;

    /**
     * 锁货已售数量(订单)
     */
    private Integer quantitySold;

    /**
     * 锁货剩余可用数量(订单)
     */
    private Integer quantitySurplus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private Integer delFlag;

    /**
     * 库存异常 0无异常
     */
    private Integer exceptionCode;
}
