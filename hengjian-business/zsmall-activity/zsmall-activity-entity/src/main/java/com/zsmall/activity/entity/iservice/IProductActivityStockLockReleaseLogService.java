package com.zsmall.activity.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.activity.entity.domain.dto.productActivity.ProductActivityStockLockReleaseLog;
import com.zsmall.activity.entity.mapper.ProductActivityStockLockReleaseLogMapper;
import org.springframework.stereotype.Service;

/**
 * 商品活动erp库存锁定/释放日志Service接口
 *
 * <AUTHOR> Li
 * @date 2025-08-12
 */
@Service
public class IProductActivityStockLockReleaseLogService extends ServiceImpl<ProductActivityStockLockReleaseLogMapper, ProductActivityStockLockReleaseLog> {

}
