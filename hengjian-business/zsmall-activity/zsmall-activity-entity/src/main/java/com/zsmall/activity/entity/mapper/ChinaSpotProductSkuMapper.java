package com.zsmall.activity.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductSku;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【china_spot_product_sku(国内现货商品SKU表)】的数据库操作Mapper
* @createDate 2023-08-15 16:40:48
* @Entity com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductSku
*/
public interface ChinaSpotProductSkuMapper extends BaseMapper<ChinaSpotProductSku> {

    Long countBySku(@Param("sku") String sku);

    Long countByProductSkuCode(@Param("productSkuCode") String productSkuCode);

}




