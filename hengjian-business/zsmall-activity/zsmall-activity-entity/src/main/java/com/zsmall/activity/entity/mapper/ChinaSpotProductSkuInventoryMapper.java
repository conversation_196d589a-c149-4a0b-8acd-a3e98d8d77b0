package com.zsmall.activity.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductSkuStock;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【china_spot_product_sku_stock(国内现货SKU库存表)】的数据库操作Mapper
* @createDate 2023-08-15 16:40:48
* @Entity com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductSkuStock
*/
public interface ChinaSpotProductSkuInventoryMapper extends BaseMapper<ChinaSpotProductSkuStock> {


    Long countQuantityTotalByProduct(@Param("chinaSpotProductId") Long chinaSpotProductId);


}




