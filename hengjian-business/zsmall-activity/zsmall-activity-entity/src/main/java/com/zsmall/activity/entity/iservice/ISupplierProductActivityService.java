package com.zsmall.activity.entity.iservice;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivity;
import com.zsmall.activity.entity.mapper.SupplierProductActivityMapper;
import org.springframework.stereotype.Service;

/**
 * 供货商商品活动Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Service
public class ISupplierProductActivityService extends ServiceImpl<SupplierProductActivityMapper, SupplierProductActivity> {


   public SupplierProductActivity getByActivityCode(String activityCode) {
        return lambdaQuery().eq(SupplierProductActivity::getSupplierActivityCode, activityCode).one();
    }


}
