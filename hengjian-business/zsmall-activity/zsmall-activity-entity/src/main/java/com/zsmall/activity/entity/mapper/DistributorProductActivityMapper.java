package com.zsmall.activity.entity.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityDetails;
import com.zsmall.activity.entity.domain.dto.productActivity.ProductActivitySearchDTO;
import com.zsmall.activity.entity.domain.dto.productActivity.export.DistributorProductActivityDetailsExportDTO;
import com.zsmall.activity.entity.domain.dto.productActivity.export.DistributorProductActivityListExportDTO;
import com.zsmall.activity.entity.domain.vo.productActivity.DistributorProductActivityVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分销商商品活动Mapper接口
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
public interface DistributorProductActivityMapper extends BaseMapperPlus<DistributorProductActivity, DistributorProductActivityVo> {

    IPage<DistributorProductActivity> selectListResponseDTO(@Param("page") Page page, ProductActivitySearchDTO wa);

    List<DistributorProductActivityDetailsExportDTO> selectDistributorProductActivityDetailsExport(@Param("wa") ProductActivitySearchDTO bo);

    List<DistributorProductActivityListExportDTO> selectDistributorProductActivityListExport(@Param("wa") ProductActivitySearchDTO bo);

    void updateDistributorProductActivityException(@Param("i") int i,@Param("productSkuCode") String productSkuCode,@Param("warehouseSystemCode") String warehouseSystemCode);

    List<DistributorProductActivityDetails> getDistributorAvailableActivesBySku(@Param("productSkuCode")String productSkuCode,@Param("site") String site);

    List<DistributorProductActivityDetails> getDistributorAvailableActivesBySpu(@Param("productCode")String productCode,@Param("site") String site);
    List<DistributorProductActivityDetails> getDistributorAvailableActivesBySupplierActiveCode(@Param("supplierActiveCode")String supplierActiveCode);
}
