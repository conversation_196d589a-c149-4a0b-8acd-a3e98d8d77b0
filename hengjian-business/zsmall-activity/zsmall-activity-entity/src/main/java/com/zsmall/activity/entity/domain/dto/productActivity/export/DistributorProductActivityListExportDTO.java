package com.zsmall.activity.entity.domain.dto.productActivity.export;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 分销商活动列表导出
 * <AUTHOR>
 */
@Data
public class DistributorProductActivityListExportDTO {
    /**
     * 活动编号（分销商）
     */
    @ExcelProperty("活动ID")
    private String distributorActivityCode;

    /**
     * 活动类型
     */
    @ExcelProperty("活动类型")
    private String activityType;

    /**
     * 活动名称
     */
//    @ExcelProperty("活动名称")
//    private String activityName;

    @ExcelProperty("商品名称")
    private String productName;

    @ExcelProperty("SKU ID")
    private String productSkuCode;

    /**
     * 站点
     */
    @ExcelProperty("站点/币种")
    private String site;
    @ExcelProperty("锁货总件数")
    private Long quantityLocked;
    @ExcelProperty("剩余件数")
    private Long quantityRemaining;

    /**
     * 活动自提锁货库存总数
     */
    @ExcelIgnore
    private Long pickupQuantityLocked;

    /**
     * 活动代发锁货库存总数
     */
    @ExcelIgnore
    private Long dropShippingQuantityLocked;
    /**
     * 活动自提锁货库存已使用数量
     */
    @ExcelIgnore
    private Long pickupLockedUsed;
    /**
     * 活动代发锁货库存已使用数量
     */
    @ExcelIgnore
    private Long dropShippingLockedUsed;

    /**
     * 平台单价（平台设置）
     */
    @ExcelProperty("活动单价")
    private BigDecimal distributorActivityUnitPrice;

    /**
     * 平台操作费（平台设置）
     */
    @ExcelProperty("活动操作费")
    private BigDecimal distributorActivityOperationFee;

    /**
     * 平台尾程派送费（平台设置）
     */
    @ExcelProperty("活动尾程费用")
    private BigDecimal distributorActivityFinalDeliveryFee;


    /**
     * 活动最小起订量
     */
    @ExcelProperty("最小起订量(件)")
    private Long quantityMinimum;


    @ExcelProperty("仓储费(每天/每件)")
    private BigDecimal distributorActivityStorageFee;

    @ExcelProperty("免仓期(天)")
    private Long freeStoragePeriod;
    /**
     * 活动天数
     */
    @ExcelProperty("活动期限(天)")
    private Long activityDay;

    /**
     * 活动开始时间(分销商)
     */
    @ExcelProperty("活动开始时间")
    private Date activeStartTime;

    /**
     * 活动结束时间(分销商)
     */
    @ExcelProperty("活动结束时间")
    private Date activeEndTime;

    @ExcelProperty("状态")
    private String activityState;
    @ExcelProperty("剩余时间")
    private Long remainingDay;
    /**
     * 币种
     */
    @ExcelIgnore
    private String currencySymbol;

}
