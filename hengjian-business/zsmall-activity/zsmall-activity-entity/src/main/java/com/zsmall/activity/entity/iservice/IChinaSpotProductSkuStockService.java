package com.zsmall.activity.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductSkuStock;
import com.zsmall.activity.entity.mapper.ChinaSpotProductSkuInventoryMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【china_spot_product_sku_stock(国内现货SKU库存表)】的数据库操作Service实现
* @createDate 2023-08-15 16:40:48
*/
@Service
public class IChinaSpotProductSkuStockService extends ServiceImpl<ChinaSpotProductSkuInventoryMapper, ChinaSpotProductSkuStock> {


    @InMethodLog(value = "根据国内现货商品id统计所有库存")
    public Long countQuantityTotalByProduct(Long chinaSpotProductId) {
        return baseMapper.countQuantityTotalByProduct(chinaSpotProductId);
    }

    @InMethodLog(value = "根据国内现货商品SkuId查询所有库存")
    public List<ChinaSpotProductSkuStock> queryBySpotProductSkuId(Long chinaSpotProductSkuId) {
        return lambdaQuery().eq(ChinaSpotProductSkuStock::getChinaSpotProductSkuId, chinaSpotProductSkuId).list();
    }


}




