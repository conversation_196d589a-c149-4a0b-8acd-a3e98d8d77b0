package com.zsmall.activity.entity.domain.dto.productActivity.export;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AdminProductActivityWarehouseExportDTO {
    @ExcelProperty("活动ID")
    private String supplierActivityCode;

    @ExcelProperty("供应商ID")
    private String supplierTenantId;
    @ExcelProperty("活动类型")
    private String  activityType;
    /**
     * 活动名称
     */
    @ExcelProperty("活动名称")
    private String activityName;
    /**
     * 发货方式
     */
    @ExcelProperty("发货方式")
    private String supportedLogistics;

    @ExcelProperty(value = "锁货仓库")
    private String warehouseCode;
    /**
     * 锁货剩余数量
     */
    @ExcelProperty(value = "可锁货库存")
    private Long quantityLockedRemaining;

}
