package com.zsmall.activity.entity.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.activity.entity.domain.StorageFeeInfo;
import com.zsmall.activity.entity.domain.bo.storageFee.StorageFeeInfoBo;
import com.zsmall.activity.entity.domain.vo.storageFee.StorageFeeInfoVo;
import org.springframework.data.repository.query.Param;

/**
 * 仓储费主Mapper接口
 *
 * <AUTHOR> Li
 * @date 2025-07-03
 */
public interface StorageFeeInfoMapper extends BaseMapperPlus<StorageFeeInfo, StorageFeeInfoVo> {

    /**
     * 分页查询
     */
    Page<StorageFeeInfoVo> queryPageListBy<PERSON>son(@Param("bo") StorageFeeInfoBo bo,  Page<StorageFeeInfoVo> page);

}
