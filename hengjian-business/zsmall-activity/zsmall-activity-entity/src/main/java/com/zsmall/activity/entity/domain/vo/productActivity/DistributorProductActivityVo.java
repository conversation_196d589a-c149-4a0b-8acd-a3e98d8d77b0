package com.zsmall.activity.entity.domain.vo.productActivity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 分销商商品活动视图对象 distributor_product_activity
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DistributorProductActivity.class)
public class DistributorProductActivityVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 供应商活动id
     */
    @ExcelProperty(value = "供应商活动id")
    private Long supplierProductActivityId;

    /**
     * 活动编号（分销商）
     */
    @ExcelProperty(value = "活动编号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "分=销商")
    private String activityCode;

    /**
     * 活动名称（分货商）
     */
    @ExcelProperty(value = "活动名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "分=货商")
    private String activityName;

    /**
     * 活动类型
     */
    @ExcelProperty(value = "活动类型")
    private String activityType;

    /**
     * 活动状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）
     */
    @ExcelProperty(value = "活动状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "D=raft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消")
    private String activityState;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String productName;

    /**
     * 商品图片
     */
    @ExcelProperty(value = "商品图片")
    private String productImg;

    /**
     * 商品唯一编码
     */
    @ExcelProperty(value = "商品唯一编码")
    private String productCode;

    /**
     * 商品Sku唯一编码
     */
    @ExcelProperty(value = "商品Sku唯一编码")
    private String productSkuCode;

    /**
     * 商品Sku
     */
    @ExcelProperty(value = "商品Sku")
    private String productSku;

    /**
     * 活动开始时间
     */
    @ExcelProperty(value = "活动开始时间")
    private Date activeStartTime;

    /**
     * 活动结束时间
     */
    @ExcelProperty(value = "活动结束时间")
    private Date activeEndTime;

    /**
     * 站点
     */
    @ExcelProperty(value = "站点")
    private String site;

    /**
     * 币种
     */
    @ExcelProperty(value = "币种")
    private String currency;

    /**
     * 活动天数
     */
    @ExcelProperty(value = "活动天数")
    private Long activityDay;

    /**
     * 免仓期
     */
    @ExcelProperty(value = "免仓期")
    private Long freeStoragePeriod;

    /**
     * 发货方式
     */
    @ExcelProperty(value = "发货方式")
    private String supportedLogistics;

    /**
     * 活动锁货库存总数
     */
    @ExcelProperty(value = "活动锁货库存总数")
    private Long quantityLocked;

    /**
     * 活动锁货库存已使用数量
     */
    @ExcelProperty(value = "活动锁货库存已使用数量")
    private Long quantityLockedUsed;

    /**
     * 活动最小起订量
     */
    @ExcelProperty(value = "活动最小起订量")
    private Long quantityMinimum;

    /**
     * 活动sku出单总数(查询详情的时候通过es查询)
     */
    @ExcelProperty(value = "活动sku出单总数(查询详情的时候通过es查询)")
    private Long orderedTotal;

    /**
     * 已支付总押金
     */
    @ExcelProperty(value = "已支付总押金")
    private BigDecimal depositPaidTotal;

    /**
     * 已支付总仓储费
     */
    @ExcelProperty(value = "已支付总仓储费")
    private BigDecimal storageFeePaidTotal;

    /**
     * 活动锁货库存剩余数量
     */
    private Long quantityLockedRemaining;
}
