package com.zsmall.activity.entity.domain.bo.productActivity;


import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityReviewRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 供应商锁货活动审核记录业务对象 supplier_product_activity_review_record
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SupplierProductActivityReviewRecord.class, reverseConvertGenerate = false)
public class SupplierProductActivityReviewRecordBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商品活动表（供货商）主键
     */
    @NotNull(message = "商品活动表（供货商）主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long supplierProductActivityId;

    /**
     * 活动初始状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）
     */
    @NotBlank(message = "活动初始状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String activityOriginState;

    /**
     * 审核租户编号（管理员）
     */
    @NotBlank(message = "审核租户编号（管理员）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reviewManager;

    /**
     * 审核时间
     */
    @NotNull(message = "审核时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date reviewTime;

    /**
     * 审核意见
     */
    @NotBlank(message = "审核意见不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reviewOpinion;

    /**
     * 审核状态（1-审核中，2-通过，3-驳回）
     */
    @NotNull(message = "审核状态（1-审核中，2-通过，3-驳回）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long reviewState;


}
