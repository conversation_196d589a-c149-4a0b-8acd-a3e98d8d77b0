package com.zsmall.activity.entity.domain.vo.chinaSpotProduct;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 国内现货SKU库存表
 * @TableName china_spot_product_sku_stock
 */
@TableName(value ="china_spot_product_sku_stock")
@Data
@EqualsAndHashCode(callSuper=false)
public class ChinaSpotProductSkuStock extends NoDeptBaseEntity {
    /**
     *
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库系统编号
     */
    private String warehouseSystemCode;

    /**
     * 仓库所属城市
     */
    private String warehouseBelongCity;

    /**
     * 国内现货商品id
     */
    private Long chinaSpotProductId;

    /**
     * 国内现货商品skuId
     */
    private Long chinaSpotProductSkuId;

    /**
     * 库存（实时变动）
     */
    private Integer quantity;

    /**
     * 总库存（创建时就固定）
     */
    private Integer quantityTotal;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
