package com.zsmall.activity.entity.domain;

import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.hengjian.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;



/**
 * 仓储费子对象 storage_fee_item
 *
 * <AUTHOR> Li
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("storage_fee_item")
@Accessors(chain = true)
public class StorageFeeItem extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    private String tenantId;

    /**
     * 仓储费ID
     */
    private String storageFeeId;

    /**
     * 明细ID
     */
    private String detailId;

    /**
     * 仓库code
     */
    private String warehouseCode;

    /**
     * 仓库系统code唯一
     */
    private String warehouseSystemCode;

    /**
     * SKU仓库数量
     */
    private Long skuNum;

    /**
     * SKU仓库剩余数量
     */
    private Long skuRemainingNum;

    /**
     * 仓储费
     */
    private BigDecimal storageFee;

    /**
     * 每天的仓储费总和
     */
    private BigDecimal storageFeeDay;

    /**
     * 分销系统唯一sku标识
     */
    private String skuId;

    /**
     * erpSku
     */
    private String sku;

    /**
     * 币种code
     */
    private String currencyCode;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 站点
     */
    private String site;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 明细仓储费结算时间
     */
    private String feeSettlementDate;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
