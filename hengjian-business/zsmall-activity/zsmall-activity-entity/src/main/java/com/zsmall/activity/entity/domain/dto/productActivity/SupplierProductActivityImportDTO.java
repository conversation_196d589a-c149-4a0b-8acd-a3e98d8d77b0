package com.zsmall.activity.entity.domain.dto.productActivity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 供应商活动导入
 * <AUTHOR>
 */
@Data
public class SupplierProductActivityImportDTO {

    /**
     * 活动名称
     */
    @ExcelProperty(index = 0)
    private String activityName;
    /**
     * 商品Sku唯一编码
     */
    @ExcelProperty(index = 1)
    private String productSkuCode;
    /**
     * 站点
     */
    @ExcelProperty(index = 2)
    private String site;
    /**
     * 供货商活动单价（供货商设置）
     */
    @ExcelProperty(index = 3)
    private BigDecimal supplierActivityUnitPrice;

    /**
     * 供货商活动操作费（供货商设置）
     */
    @ExcelProperty(index = 4)
    private BigDecimal supplierActivityOperationFee;

    /**
     * 供货商活动尾程派送费（供货商设置）
     */
    @ExcelProperty(index = 5)
    private BigDecimal supplierActivityFinalDeliveryFee;
    /**
     * 活动最小起订量
     */
    @ExcelProperty(index = 6)
    private Integer quantityMinimum;
    /**
     * 供货商活动仓储费（一件/天，供货商设置）
     */
    @ExcelProperty(index = 7)
    private BigDecimal supplierActivityStorageFee;
    /**
     * 免仓期
     */
    @ExcelProperty(index = 8)
    private Integer freeStoragePeriod;
    /**
     * 活动天数
     */
    @ExcelProperty(index = 9)
    private Integer activityDay;

    /**
     * 发货方式
     */
    @ExcelProperty(index = 10)
    private String supportedLogistics;
    /**
     * 仓库编码
     */
    @ExcelProperty(index = 11)
    private String warehouseCode;
    /**
     * 锁货数量
     */
    @ExcelProperty(index = 12)
    private Integer quantityTotal;

    @ExcelIgnore
    private String warehouseSystemCode;
    @ExcelIgnore
    private Long productSkuStockId;
}
