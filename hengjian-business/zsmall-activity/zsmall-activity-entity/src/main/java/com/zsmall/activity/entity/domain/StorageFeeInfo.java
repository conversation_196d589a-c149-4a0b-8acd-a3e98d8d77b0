package com.zsmall.activity.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.hengjian.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;



/**
 * 仓储费主对象 storage_fee_info
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("storage_fee_info")
@Accessors(chain = true)
public class StorageFeeInfo extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    private String tenantId;

    /**
     * 仓储费ID
     */
    private String storageFeeId;

    /**
     * 费用状态  0: 待确认  1：确认中 2：已确认
     */
    private Integer feeState;

    /**
     * 币种code
     */
    private String currencyCode;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 总仓储费
     */
    private BigDecimal totalStorageFee;

    /**
     * 仓储费结算时间
     */
    private String storageFeeSettlementDate;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 发货方式
     */
    private String logisticsType;

    /**
     * 支付状态  0：未支付 1：已支付
     */
    private Integer payState;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 发送时间
     */
    private String sendTime;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject errorMessage;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
