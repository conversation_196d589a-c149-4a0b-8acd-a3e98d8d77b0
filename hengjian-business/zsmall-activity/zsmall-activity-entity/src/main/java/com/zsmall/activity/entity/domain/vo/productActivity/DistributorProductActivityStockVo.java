package com.zsmall.activity.entity.domain.vo.productActivity;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 分销商商品活动库存视图对象 distributor_product_activity_stock
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DistributorProductActivityStock.class)
public class DistributorProductActivityStockVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 分销商活动编码
     */
    @ExcelProperty(value = "分销商活动编码")
    private String distributorActivityCode;

    /**
     * 分销商活动表id
     */
    @ExcelProperty(value = "分销商活动表id")
    private Long distributorProductActivityId;

    /**
     * 仓库系统编号
     */
    @ExcelProperty(value = "仓库系统编号")
    private String warehouseSystemCode;
    /**
     * 仓库编码
     */
    @ExcelProperty(value = "仓库编号")
    private String warehouseCode;

    /**
     * 自提/代发
     */
    @ExcelProperty(value = "自提/代发")
    private String supportedLogistics;

    /**
     * 锁货数量
     */
    @ExcelProperty(value = "锁货数量")
    private Long quantityTotal;

    /**
     * 锁货已售数量
     */
    @ExcelProperty(value = "锁货已售数量")
    private Long quantitySold;

    /**
     * 锁货剩余数量
     */
    @ExcelProperty(value = "锁货剩余数量")
    private Long quantitySurplus;


}
