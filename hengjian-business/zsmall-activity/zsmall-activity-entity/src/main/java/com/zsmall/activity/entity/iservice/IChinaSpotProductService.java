package com.zsmall.activity.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProduct;
import com.zsmall.activity.entity.mapper.ChinaSpotProductMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【china_spot_product(国内现货商品表)】的数据库操作Service实现
* @createDate 2023-08-15 16:40:48
*/
@Service
public class IChinaSpotProductService extends ServiceImpl<ChinaSpotProductMapper, ChinaSpotProduct> {


    @InMethodLog(value = "分页查询国内现货商品")
    public Page<ChinaSpotProduct> queryPage(String queryType, String queryValue,
                                              Page<ChinaSpotProduct> page) {
        return TenantHelper.ignore(() -> baseMapper.queryPage(queryType, queryValue, page), TenantType.Manager);
    }

    @InMethodLog(value = "根据商品编号查询国内现货商品")
    public ChinaSpotProduct queryByProductCode(String productCode) {
        return TenantHelper.ignore(() -> lambdaQuery().eq(ChinaSpotProduct::getProductCode, productCode).one(), TenantType.Manager);
    }

    @InMethodLog(value = "Count相同ProductCode的SPU数量")
    public Long countByProductCode(String productCode) {
        return TenantHelper.ignore(() -> baseMapper.countByProductCode(productCode));
    }

    @InMethodLog("国内现货商品编号是否存在")
    public Boolean existProductCode(String productCode) {
        LambdaQueryWrapper<ChinaSpotProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(ChinaSpotProduct::getProductCode, productCode);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }
}




