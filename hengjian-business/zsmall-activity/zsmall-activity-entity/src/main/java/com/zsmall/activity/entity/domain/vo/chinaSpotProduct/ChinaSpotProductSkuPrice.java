package com.zsmall.activity.entity.domain.vo.chinaSpotProduct;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 国内现货SKU价格表
 * @TableName china_spot_product_sku_price
 */
@TableName(value ="china_spot_product_sku_price")
@Data
@EqualsAndHashCode(callSuper=false)
public class ChinaSpotProductSkuPrice extends NoDeptBaseEntity {
    /**
     *
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 清货商品id
     */
    private Long chinaSpotProductId;

    /**
     * 清货商品skuId
     */
    private Long chinaSpotProductSkuId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 适用价格的最小数量（包含）
     */
    private Integer minimumQuantity;

    /**
     * 适用价格的最大数量（不包含）
     */
    private Integer maximumQuantity;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
