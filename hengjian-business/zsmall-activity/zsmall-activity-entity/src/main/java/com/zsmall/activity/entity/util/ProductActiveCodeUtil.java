package com.zsmall.activity.entity.util;

import org.springframework.stereotype.Component;
import java.util.Random;
import java.util.HashSet;
import java.util.Set;

/**
 * Simple 6-digit unique ID generator for low concurrency scenarios
 * Format: timestamp(2) + random(4)
 * <AUTHOR>
 */
@Component
public class ProductActiveCodeUtil {

    private final Random random = new Random();

    public ProductActiveCodeUtil() {
        System.out.println("SimpleUniqueIdGenerator initialized");
    }

    /**
     * Generate 6-digit unique ID
     * Format: timestamp(2) + random(4)
     * Higher randomness, lower time dependency
     */
    public String generateUniqueId() {
        long timestamp = System.currentTimeMillis();
        int timePart = (int)(timestamp % 100);
        int randomPart = random.nextInt(10000);

        return String.format("%02d%04d", timePart, randomPart);
    }

    /**
     * Static method for direct calling
     * Usage: SimpleUniqueIdGenerator.generateUniqueId()
     */
    public static String generate() {
        long timestamp = System.currentTimeMillis();
        int timePart = (int)(timestamp % 100);
        int randomPart = new Random().nextInt(10000);

        return String.format("%02d%04d", timePart, randomPart);
    }



    /**
     * Test duplicate rate
     */
    public void testDuplicateRate(int testCount) {
        Set<String> idSet = new HashSet<>();
        int duplicates = 0;

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < testCount; i++) {
            String id = generateUniqueId();
            if (!idSet.add(id)) {
                duplicates++;
            }
        }

        long endTime = System.currentTimeMillis();

        System.out.println("=== Duplicate Rate Test Results ===");
        System.out.println("Test Count: " + testCount);
        System.out.println("Unique IDs: " + idSet.size());
        System.out.println("Duplicates: " + duplicates);
        System.out.println("Duplicate Rate: " + String.format("%.4f%%", (double) duplicates / testCount * 100));
        System.out.println("Time Cost: " + (endTime - startTime) + "ms");
    }

    public static void main(String[] args) {
        ProductActiveCodeUtil generator = new ProductActiveCodeUtil();

        // Basic test
        System.out.println("=== Basic ID Generation Test ===");
        for (int i = 0; i < 10; i++) {
            System.out.println("ID: " + generator.generateUniqueId());
        }

        // Duplicate rate test
        generator.testDuplicateRate(10000);
    }
}
