package com.zsmall.activity.entity.domain.dto.productActivity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ActivityProductSkuDTO {
    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品图片
     */
    private String productSkuImg;

    /**
     * productSkuId
     */
    private Long productSkuId;

    /**
     * 商品唯一编码
     */
    private String productCode;

    /**
     * 商品Sku唯一编码
     */
    private String productSkuCode;

    /**
     * 商品Sku(ERP SKU)
     */
    private String productSku;

    /**
     * 站点
     */
    private String site;

    /**
     * 币种符号
     */
    private String currencySymbol;
    /**
     * 自提价
     */
    private BigDecimal pickUpPrice;
    /**
     * 代发价
     */
    private BigDecimal dropShippingPrice;

    /**
     * 自提库存
     */
    private Integer pickUpStock;
    /**
     * 代发库存
     */
    private Integer dropShippingStock;
    /**
     * 支持的物流方式
     */
    private String supportedLogistics;



}
