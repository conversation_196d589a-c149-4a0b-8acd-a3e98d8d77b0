package com.zsmall.activity.entity.domain.dto.productActivity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 分销商商品活动库存对象 distributor_product_activity_stock
 *
 * <AUTHOR> Li
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("distributor_product_activity_stock")
public class DistributorProductActivityStock extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 分销商活动编码
     */
    private String distributorActivityCode;

    /**
     * 分销商活动表id
     */
    private Long distributorActivityId;

    /**
     * 供应商仓库ID
     */
    private Long supplierActivityStockId;

    /**
     * 仓库系统编号
     */
    private String warehouseSystemCode;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 自提/代发
     */
    private String supportedLogistics;

    /**
     * 锁货总数量(锁货)
     */
    private Integer quantityTotal;

    /**
     * 锁货已售数量(订单)
     */
    private Integer quantitySold;

    /**
     * 锁货剩余可用数量(订单)
     */
    private Integer quantitySurplus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;
    /**
     * 库存异常 0无异常
     */
    private Integer exceptionCode;

}
