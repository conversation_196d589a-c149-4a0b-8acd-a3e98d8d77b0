package com.zsmall.activity.entity.domain.bo.storageFee;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.activity.entity.domain.StorageFeeInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 仓储费主业务对象 storage_fee_info
 *
 * <AUTHOR> Li
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StorageFeeInfo.class, reverseConvertGenerate = false)
public class StorageFeeInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 仓储费ID集合
     */
    private List<Long> ids;

    private String tenantId;

    /**
     * 仓储费ID
     */
    @NotBlank(message = "仓储费ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String storageFeeId;

    /**
     * 费用状态
     */
    @NotNull(message = "费用状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer feeState;

    private List<Integer> feeStateList;

    /**
     * 币种code
     */
    @NotBlank(message = "币种code不能为空", groups = { AddGroup.class, EditGroup.class })
    private String currencyCode;

    /**
     * 币种符号
     */
    @NotBlank(message = "币种符号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String currencySymbol;

    /**
     * 总仓储费
     */
    @NotNull(message = "总仓储费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal totalStorageFee;

    /**
     * 仓储费结算时间
     */
    @NotNull(message = "仓储费结算时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String storageFeeSettlementDate;

    /**
     * 仓储费结算时间开始
     */
    private String storageFeeSettlementDateStart;

    /**
     * 仓储费结算时间结束
     */
    private String storageFeeSettlementDateEnd;

    /**
     * 活动ID
     */
    @NotBlank(message = "活动ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String activityId;

    /**
     * 活动类型
     */
    @NotBlank(message = "活动类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String activityType;

    /**
     * 发货方式
     */
    @NotBlank(message = "发货方式不能为空", groups = { AddGroup.class, EditGroup.class })
    private String logisticsType;

    /**
     * 支付状态
     */
    @NotBlank(message = "支付状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer payState;

    /**
     * 支付时间
     */
    @NotNull(message = "支付时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String payTime;

    /**
     * 支付时间开始
     */
    private String payTimeStart;
    /**
     * 支付时间结束
     */
    private String payTimeEnd;

    /**
     * 发送时间
     */
    @NotNull(message = "发送时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sendTime;

    /**
     * 发送时间开始
     */
    private String sendTimeStart;
    /**
     * 发送时间结束
     */
    private String sendTimeEnd;


}
