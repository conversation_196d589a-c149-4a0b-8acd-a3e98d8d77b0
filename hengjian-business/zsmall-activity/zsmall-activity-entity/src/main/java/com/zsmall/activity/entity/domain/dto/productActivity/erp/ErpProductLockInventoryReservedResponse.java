package com.zsmall.activity.entity.domain.dto.productActivity.erp;

import lombok.Data;

/**
 * ERP锁库存响应体
 * <AUTHOR>
 */
@Data
public class ErpProductLockInventoryReservedResponse {

    /**
     * 状态码
     */
    private Integer statusCode;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private InventoryReserveData data;

    @Data
    public static class InventoryReserveData {

        /**
         * ID
         */
        private Long id;

        /**
         * 创建人名称
         */
        private String createdName;

        /**
         * 创建人ID
         */
        private Integer createdBy;

        /**
         * 更新人名称
         */
        private String updatedName;

        /**
         * 更新时间
         */
        private String updatedAt;

        /**
         * 组织ID
         */
        private Integer organizationId;

        /**
         * 仓库类型
         */
        private Integer warehouseType;

        /**
         * 库存类型
         */
        private Integer inventoryType;

        /**
         * 组织仓库ID
         */
        private Integer orgWarehouseId;

        /**
         * 仓库ID
         */
        private Integer warehouseId;

        /**
         * 组织仓库编码
         */
        private String orgWarehouseCode;

        /**
         * 来源渠道
         */
        private String sourceChannel;

        /**
         * 产品ID
         */
        private Long productId;

        /**
         * SKU
         */
        private String sku;

        /**
         * 仓库SKU
         */
        private String depotSku;

        /**
         * 来源标签
         */
        private Integer srcLabel;

        /**
         * 来源编号
         */
        private String sourceNo;

        /**
         * 仓库SKU别名
         */
        private String depotSkualias;

        /**
         * 操作类型
         */
        private String opType;

        /**
         * 数量标签
         */
        private String quantityLabel;

        /**
         * 操作标签
         */
        private String opLabel;

        /**
         * 数量
         */
        private Integer quantity;

        /**
         * 数量请求
         */
        private Integer qtyRequest;

        /**
         * 货币
         */
        private String currency;

        /**
         * 追踪编码
         */
        private String trackCode;

        /**
         * 销售SKU
         */
        private String sellerSku;

        /**
         * 订单号
         */
        private String orderNo;

        /**
         * 备注
         */
        private String note;

        /**
         * 元数据
         */
        private String meta;

        /**
         * 物流信息
         */
        private String shippingInfo;

        /**
         * 操作ID
         */
        private String opId;

        /**
         * 配对状态
         */
        private String pairStatus;

        /**
         * 发生时间
         */
        private String occursAt;

        /**
         * 执行时间
         */
        private String execAt;

        /**
         * 操作预留时间
         */
        private String operateAt;

        /**
         * 配对更新时间
         */
        private String pairUpdated;

        /**
         * 删除时间
         */
        private String deletedAt;

        /**
         * 组编码
         */
        private String groupCode;

        /**
         * 来源ID
         */
        private String sourceId;

        /**
         * 操作名称
         */
        private String opName;

        /**
         * 库存预留键
         */
        private String inventoryReservedKey;

        /**
         * 显示信息
         */
        private String display;
    }
}
