package com.zsmall.activity.entity.domain.vo.productActivity;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.activity.entity.domain.dto.productActivity.ProductActivityStockLockReleaseLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 商品活动erp库存锁定/释放日志视图对象 product_activity_stock_lock_release_log
 *
 * <AUTHOR> Li
 * @date 2025-08-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductActivityStockLockReleaseLog.class)
public class ProductActivityStockLockReleaseLogVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 请求类型（[ERP库存锁定] 或 [ERP库存释放]）
     */
    private String requestType;

    /**
     * 活动编码
     */
    @ExcelProperty(value = "活动编码")
    private String activeCode;

    /**
     * 仓库编码
     */
    @ExcelProperty(value = "仓库编码")
    private String warehouseName;



    /**
     * 请求成功/失败
     */
    @ExcelProperty(value = "请求成功/失败")
    private Boolean isSuccess;
    /**
     *
     */
    @ExcelProperty(value = "")
    private String request;

    /**
     * 响应
     */
    @ExcelProperty(value = "响应")
    private String response;


}
