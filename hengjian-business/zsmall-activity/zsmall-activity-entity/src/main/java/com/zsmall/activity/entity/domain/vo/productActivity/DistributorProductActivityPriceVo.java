package com.zsmall.activity.entity.domain.vo.productActivity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityPrice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;



/**
 * 分销商商品活动定价视图对象 distributor_product_activity_price
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DistributorProductActivityPrice.class)
public class DistributorProductActivityPriceVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 分销商活动编号
     */
    @ExcelProperty(value = "分销商活动编号")
    private String distributorActivityCode;

    /**
     * 分销商商品活动表主键
     */
    @ExcelProperty(value = "分销商商品活动表主键")
    private Long distributorProductActivityId;

    /**
     * 分销商租户id
     */
    @ExcelProperty(value = "分销商租户id")
    private String distributorTenantId;

    /**
     * 平台单价（平台设置）
     */
    @ExcelProperty(value = "平台单价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "平=台设置")
    private BigDecimal distributorActivityUnitPrice;

    /**
     * 平台操作费（平台设置）
     */
    @ExcelProperty(value = "平台操作费", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "平=台设置")
    private BigDecimal distributorActivityOperationFee;

    /**
     * 平台尾程派送费（平台设置）
     */
    @ExcelProperty(value = "平台尾程派送费", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "平=台设置")
    private BigDecimal distributorActivityFinalDeliveryFee;

    /**
     * 平台自提价（平台单价+平台操作费）
     */
    @ExcelProperty(value = "平台自提价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "平=台单价+平台操作费")
    private BigDecimal distributorActivityPickUpPrice;

    /**
     * 平台代发价（平台自提价+平台尾程派送费）
     */
    @ExcelProperty(value = "平台代发价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "平=台自提价+平台尾程派送费")
    private BigDecimal distributorActivityDropShippingPrice;


}
