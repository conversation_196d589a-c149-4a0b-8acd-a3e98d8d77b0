package com.zsmall.activity.entity.mapper;


import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.activity.entity.domain.dto.productActivity.ProductActivityStockLockReleaseLog;
import com.zsmall.activity.entity.domain.vo.productActivity.ProductActivityStockLockReleaseLogVo;

/**
 * 商品活动erp库存锁定/释放日志Mapper接口
 *
 * <AUTHOR> Li
 * @date 2025-08-12
 */
public interface ProductActivityStockLockReleaseLogMapper extends BaseMapperPlus<ProductActivityStockLockReleaseLog, ProductActivityStockLockReleaseLogVo> {

}

