package com.zsmall.activity.entity.iservice;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityPrice;
import com.zsmall.activity.entity.mapper.SupplierProductActivityPriceMapper;
import org.springframework.stereotype.Service;

/**
 * 供应商商品活动定价Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Service
public class ISupplierProductActivityPriceService extends ServiceImpl<SupplierProductActivityPriceMapper, SupplierProductActivityPrice> {

}
