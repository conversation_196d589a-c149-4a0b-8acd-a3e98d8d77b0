package com.zsmall.activity.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.activity.entity.mapper.TransactionStorageFeeMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;



/**
 * 交易记录仓储费关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@RequiredArgsConstructor
@Service
public class ITransactionStorageFeeService extends ServiceImpl<TransactionStorageFeeMapper, com.zsmall.activity.entity.domain.TransactionStorageFee> {

    private final TransactionStorageFeeMapper baseMapper;

}
