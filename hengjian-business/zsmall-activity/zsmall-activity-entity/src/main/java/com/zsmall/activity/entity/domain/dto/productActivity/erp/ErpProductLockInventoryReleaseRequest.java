package com.zsmall.activity.entity.domain.dto.productActivity.erp;

import lombok.Data;

/**
 * 释放预留库存请求体
 * <AUTHOR>
 */
@Data
public class ErpProductLockInventoryReleaseRequest {

    /**
     * 分销商活动库存主键
     */
    private Long distributorActivityStockId;
    /**
     * 供应商活动库存主键
     */
    private Long supplierActivityStockId;

    /**
     * 仓库编码
     */
    private String orgWarehouseCode;

    /**
     * 库存类型
     * 0:正常库存; 1:不可售库存
     */
    private Integer inventoryType;

    /**
     * 来源类型
     * 分销商城: 90
     */
    private Integer srcLabel;

    /**
     * 来源单号
     */
    private String sourceNo;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 预留追踪号
     */
    private String trackCode;

    /**
     * 产品编码(ERP SKU)
     */
    private String sku;
}
