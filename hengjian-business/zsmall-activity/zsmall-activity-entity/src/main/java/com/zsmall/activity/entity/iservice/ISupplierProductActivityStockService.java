package com.zsmall.activity.entity.iservice;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityStock;
import com.zsmall.activity.entity.mapper.SupplierProductActivityStockMapper;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 供应商商品活动库存Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Service
public class ISupplierProductActivityStockService extends ServiceImpl<SupplierProductActivityStockMapper, SupplierProductActivityStock> {

    public List<SupplierProductActivityStock> getBySupplierActivityCode(
        @NotNull(message = "供应商活动编码不能为空") String supplierProductActivityCode, String supportedLogistics) {
        return baseMapper.getBySupplierActivityCode(supplierProductActivityCode, supportedLogistics);
    }
}
