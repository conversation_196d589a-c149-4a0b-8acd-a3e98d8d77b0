package com.zsmall.activity.entity.iservice;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import com.zsmall.activity.entity.mapper.DistributorProductActivityStockMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分销商商品活动库存Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Service
public class IDistributorProductActivityStockService extends ServiceImpl<DistributorProductActivityStockMapper, DistributorProductActivityStock> {

    public List<DistributorProductActivityStock> queryListByDistributorProductActivityIds(List<Long> distributorProductActivityIds){
        if(CollUtil.isNotEmpty(distributorProductActivityIds)){
            LambdaQueryWrapper<DistributorProductActivityStock> lqw = Wrappers.lambdaQuery();
            lqw.in(DistributorProductActivityStock::getDistributorActivityId,distributorProductActivityIds);
            return TenantHelper.ignore(() ->baseMapper.selectList(lqw));
        }else {
            return null;
        }
    }
}
