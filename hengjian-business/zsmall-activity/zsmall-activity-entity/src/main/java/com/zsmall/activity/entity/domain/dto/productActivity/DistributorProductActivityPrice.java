package com.zsmall.activity.entity.domain.dto.productActivity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;



/**
 * 分销商商品活动定价对象 distributor_product_activity_price
 *
 * <AUTHOR> Li
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("distributor_product_activity_price")
public class DistributorProductActivityPrice extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 分销商活动编号
     */
    private String distributorActivityCode;

    /**
     * 分销商商品活动表主键
     */
    private Long distributorActivityId;

    /**
     * 分销商租户id
     */
    private String distributorTenantId;

    /**
     * 平台单价（平台设置）
     */
    private BigDecimal distributorActivityUnitPrice;

    /**
     * 平台操作费（平台设置）
     */
    private BigDecimal distributorActivityOperationFee;

    /**
     * 平台尾程派送费（平台设置）
     */
    private BigDecimal distributorActivityFinalDeliveryFee;

    /**
     * 平台自提价（平台单价平台操作费）
     */
    private BigDecimal distributorActivityPickUpPrice;

    /**
     * 平台代发价（平台自提价平台尾程派送费）
     */
    private BigDecimal distributorActivityDropShippingPrice;

    /**
     *
     */
    @TableLogic
    private Integer delFlag;


}
