package com.zsmall.activity.entity.domain.dto.productActivity.export;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SupplierProductActivityDetailsExportDTO {
    @ExcelProperty("活动ID")
    private String supplierActivityCode;

    /**
     * 活动名称
     */
    @ExcelProperty("活动名称")
    private String activityName;

    @ExcelProperty("活动SKU ID")
    private String productSkuCode;

    /**
     * 站点
     */
    @ExcelProperty("活动站点")
    private String site;
    @ExcelIgnore
    private String currency;

    @ExcelProperty("锁货总数量")
    private Long quantityLocked;

    @ExcelProperty("活动出单SKU总数量")
    private Long activityOrderNum;
    /**
     * 已支付总押金
     */
    @ExcelProperty("已支付总押金")
    private BigDecimal depositPaidTotal;

    /**
     * 已支付总仓储费
     */
    @ExcelProperty("已支付总仓储费")
    private BigDecimal storageFeePaidTotal;

    /**
     * 活动编号（分销商）
     */
    @ExcelProperty("分销商活动ID")
    private String distributorActivityCode;
    /**
     * 租户编号（分货商）
     */
    @ExcelProperty("分销商ID")
    private String distributorTenantId;

    @ExcelProperty("分销商发货方式")
    private String distributorSupportedLogistics;
    /**
     * 活动锁货库存总数
     */
    @ExcelProperty("分销商锁货总数量")
    private Long distributorQuantityLocked;

    @ExcelProperty("分销商活动出单SKU总数量")
    private Long distributorActivityOrderNum;

    /**
     * 已支付总押金
     */
    @ExcelProperty("分销商已支付总押金")
    private BigDecimal distributorDepositPaidTotal;

    /**
     * 已支付总仓储费
     */
    @ExcelProperty("分销商已支付总仓储费")
    private BigDecimal distributorStorageFeePaidTotal;

    @ExcelProperty("分销商活动创建时间")
    private Date  distributorCreateTime;
    @ExcelIgnore
    private String supplierTenantId;
    @ExcelIgnore
    private String currencySymbol;


    /**
     * 活动自提锁货库存总数
     */
    @ExcelIgnore
    private Long distributorPickupQuantityLocked;

    /**
     * 活动代发锁货库存总数
     */
    @ExcelIgnore
    private Long distributorDropShippingQuantityLocked;
    /**
     * 活动自提锁货库存已使用数量
     */
    @ExcelIgnore
    private Long distributorPickupLockedUsed;
    /**
     * 活动代发锁货库存已使用数量
     */
    @ExcelIgnore
    private Long distributorDropShippingLockedUsed;


    /**
     * 活动自提锁货库存总数
     */
    @ExcelIgnore
    private Long supplierPickupQuantityLocked;

    /**
     * 活动代发锁货库存总数
     */
    @ExcelIgnore
    private Long supplierDropShippingQuantityLocked;
    /**
     * 活动自提锁货库存已使用数量
     */
    @ExcelIgnore
    private Long supplierPickupLockedUsed;
    /**
     * 活动代发锁货库存已使用数量
     */
    @ExcelIgnore
    private Long supplierDropShippingLockedUsed;
}
