package com.zsmall.activity.entity.iservice;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.mapper.DistributorProductActivityMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分销商商品活动Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Service
public class IDistributorProductActivityService extends ServiceImpl<DistributorProductActivityMapper, DistributorProductActivity>{
    public DistributorProductActivity getByActivityCode(String activityCode) {
        return lambdaQuery().eq(DistributorProductActivity::getDistributorActivityCode, activityCode).one();
    }
    public List<DistributorProductActivity> getBySupplierActivityCode(String supplierActivityCode) {
        return lambdaQuery().eq(DistributorProductActivity::getSupplierActivityCode, supplierActivityCode).list();
    }
    public void updateDistributorProductActivityState(String supplierActivityCode,String activityState) {
        lambdaUpdate().eq(DistributorProductActivity::getSupplierActivityCode, supplierActivityCode)
            .set(DistributorProductActivity::getActivityState, activityState)
            .update();
    }

    /**
     * 查询分销商商品活动列表
     * @param activityState
     * @return
     */
    public List<DistributorProductActivity> queryListByActivityState(List<String> activityState){
        if(CollUtil.isNotEmpty(activityState)){
            LambdaQueryWrapper<DistributorProductActivity> lqw = Wrappers.lambdaQuery();
            lqw.in(DistributorProductActivity::getActivityState,activityState);
            return TenantHelper.ignore(() ->baseMapper.selectList(lqw));
        }else {
            return null;
        }

    }

}
