package com.zsmall.activity.entity.domain.dto.productActivity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 供应商商品活动库存对象 supplier_product_activity_stock
 *
 * <AUTHOR> Li
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("supplier_product_activity_stock")
public class SupplierProductActivityStock extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 商品sku库存id
     */
    private Long productSkuStockId;
    /**
     * 供应商活动编码
     */
    private String supplierActivityCode;

    /**
     * 供应商活动表id
     */
    private Long supplierActivityId;

    /**
     * 仓库系统编号
     */
    private String warehouseSystemCode;

    /**
     * 仓库编号
     */
    private String warehouseCode;

    /**
     * 自提/代发
     */
    private String supportedLogistics;

    /**
     * 锁货数量(锁货)
     */
    private Integer quantityTotal;

    /**
     * 锁货已使用数量(锁货)
     */
    private Integer quantitySold;

    /**
     * 锁货剩余数量(锁货)
     */
    private Integer quantitySurplus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 库存异常 0无异常
     */
    private Integer exceptionCode;


}
