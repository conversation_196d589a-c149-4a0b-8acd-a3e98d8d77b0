package com.zsmall.activity.entity.domain.vo.storageFee;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.activity.entity.domain.StorageFeeItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;


import java.io.Serializable;



/**
 * 仓储费子视图对象 storage_fee_item
 *
 * <AUTHOR> Li
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StorageFeeItem.class)
public class StorageFeeItemVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;

    /**
     * 仓储费ID
     */
    @ExcelIgnore
    private String storageFeeId;

    /**
     * 明细ID
     */
    @ExcelProperty(value = "明细ID")
    private String detailId;

    /**
     * 仓库code
     */
    @ExcelProperty(value = "仓库")
    private String warehouseCode;

    /**
     * 仓库系统code唯一
     */
    @ExcelIgnore
    private String warehouseSystemCode;

    /**
     * SKU仓库数量
     */
    @ExcelProperty(value = "SKU仓库数量")
    private Long skuNum;

    /**
     * SKU仓库剩余数量
     */
    @ExcelProperty(value = "SKU仓库剩余数量")
    private Long skuRemainingNum;

    /**
     * 仓储费
     */
    @ExcelIgnore
    private BigDecimal storageFee;

    @ExcelProperty(value = "仓储费（每天/每件）")
    private String storageFeeString;
    /**
     * 每天的仓储费总和
     */
    @ExcelIgnore
    private BigDecimal storageFeeDay;

    @ExcelProperty(value = "仓储费")
    private String storageFeeDayString;

    /**
     * 分销系统唯一sku标识
     */
    @ExcelProperty(value = "SKU ID")
    private String skuId;

    /**
     * erpSku
     */
    @ExcelProperty(value = "SKU")
    private String sku;

    /**
     * 币种code
     */
    @ExcelIgnore
    private String currencyCode;

    /**
     * 币种符号
     */
    @ExcelIgnore
    private String currencySymbol;

    /**
     * 站点
     */
    @ExcelIgnore
    private String site;

    @ExcelProperty(value = "站点")
    private String siteString;

    /**
     * 活动ID
     */
    @ExcelProperty(value = "活动ID")
    private String activityId;

    /**
     * 明细仓储费结算时间
     */
    @ExcelProperty(value = "明细结算时间")
    private String feeSettlementDate;


}
