package com.zsmall.activity.entity.domain.vo.productActivity;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityStock;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 供应商商品活动库存视图对象 supplier_product_activity_stock
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SupplierProductActivityStock.class)
public class SupplierProductActivityStockVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 供应商活动编码
     */
    @ExcelProperty(value = "供应商活动编码")
    private String supplierActivityCode;

    /**
     * 供应商活动表id
     */
    @ExcelProperty(value = "供应商活动表id")
    private Long supplierProductActivityId;

    /**
     * 仓库系统编号
     */
    @ExcelProperty(value = "仓库系统编号")
    private String warehouseSystemCode;

    @ExcelProperty(value = "仓库编号")
    private String warehouseCode;
    /**
     * 自提/代发
     */
    @ExcelProperty(value = "自提/代发")
    private String supportedLogistics;

    /**
     * 锁货数量
     */
    @ExcelProperty(value = "锁货数量")
    private Long quantityTotal;

    /**
     * 锁货已售数量
     */
    @ExcelProperty(value = "锁货已售数量")
    private Long quantitySold;

    /**
     * 锁货剩余数量
     */
    @ExcelProperty(value = "锁货剩余数量")
    private Long quantitySurplus;


}
