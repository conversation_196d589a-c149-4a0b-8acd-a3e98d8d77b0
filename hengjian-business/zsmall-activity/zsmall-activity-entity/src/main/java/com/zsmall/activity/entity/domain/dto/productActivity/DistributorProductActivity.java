package com.zsmall.activity.entity.domain.dto.productActivity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;



/**
 * 分销商商品活动对象 distributor_product_activity
 *
 * <AUTHOR> Li
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("distributor_product_activity")
public class DistributorProductActivity extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 供应商活动id
     */
    private Long supplierActivityId;

    /**
     * 供应商活动编码
     */
    private String supplierActivityCode;

    /**
     * 租户编号（分货商）
     */
    private String distributorTenantId;

    /**
     * 活动编号（分销商）
     */
    private String distributorActivityCode;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 活动状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）
     */
    private String activityState;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品图片
     */
    private String productImg;

    /**
     * 商品唯一编码
     */
    private String productCode;

    /**
     * 商品Sku唯一编码
     */
    private String productSkuCode;

    /**
     * 商品Sku
     */
    private String productSku;

    /**
     * 活动开始时间
     */
    private Date activeStartTime;

    /**
     * 活动结束时间
     */
    private Date activeEndTime;

    /**
     * 站点
     */
    private String site;

    /**
     * 币种
     */
    private String currencySymbol;

    /**
     * 活动天数
     */
    private Integer activityDay;

    /**
     * 免仓期
     */
    private Integer freeStoragePeriod;

    /**
     * 发货方式
     */
    private String supportedLogistics;

    /**
     * 活动最小起订量
     */
    private Long quantityMinimum;

    /**
     * 已支付总押金
     */
    private BigDecimal depositPaidTotal;

    /**
     * 已支付总仓储费
     */
    private BigDecimal storageFeePaidTotal;

    /**
     * 仓储费
     */
    private BigDecimal distributorActivityStorageFee;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 活动异常 0无异常
     */
    private Integer exceptionCode;

    /**
     * 活动自提锁货库存总数(锁货)
     */
    private Integer pickupQuantityLocked;

    /**
     * 活动代发锁货库存总数(锁货)
     */
    private Integer dropShippingQuantityLocked;

    /**
     * 活动自提锁货库存已使用数量(订单)
     */
    private Integer pickupLockedUsed;

    /**
     * 活动代发锁货库存已使用数量(订单)
     */
    private Integer dropShippingLockedUsed;

    /**
     * 活动出单总数(订单)
     */
    private Integer orderedTotal;


}
