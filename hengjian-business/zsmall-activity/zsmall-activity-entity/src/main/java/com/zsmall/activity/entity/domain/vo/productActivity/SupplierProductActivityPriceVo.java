package com.zsmall.activity.entity.domain.vo.productActivity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityPrice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;



/**
 * 供应商商品活动定价视图对象 supplier_product_activity_price
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SupplierProductActivityPrice.class)
public class SupplierProductActivityPriceVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 供货商活动编号
     */
    @ExcelProperty(value = "供货商活动编号")
    private String supplierActivityCode;

    /**
     * 供应商租户id
     */
    @ExcelProperty(value = "供应商租户id")
    private String supplierTenantId;

    /**
     * 商品活动表（供货商）主键
     */
    @ExcelProperty(value = "商品活动表", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "供=货商")
    private Long supplierProductActivityId;

    /**
     * 供货商活动单价（供货商设置）
     */
    @ExcelProperty(value = "供货商活动单价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "供=货商设置")
    private BigDecimal supplierActivityUnitPrice;

    /**
     * 供货商活动操作费（供货商设置）
     */
    @ExcelProperty(value = "供货商活动操作费", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "供=货商设置")
    private BigDecimal supplierActivityOperationFee;

    /**
     * 供货商活动尾程派送费（供货商设置）
     */
    @ExcelProperty(value = "供货商活动尾程派送费", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "供=货商设置")
    private BigDecimal supplierActivityFinalDeliveryFee;



    /**
     * 供货商活动自提价（活动单价+活动操作费）
     */
    @ExcelProperty(value = "供货商活动自提价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "活=动单价+活动操作费")
    private BigDecimal supplierActivityPickUpPrice;

    /**
     * 供货商活动代发价（活动自提价+活动尾程派送费）
     */
    @ExcelProperty(value = "供货商活动代发价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "活=动自提价+活动尾程派送费")
    private BigDecimal supplierActivityDropShippingPrice;


}
