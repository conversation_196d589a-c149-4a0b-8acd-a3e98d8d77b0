package com.zsmall.extend.shop.tiktok.job.support;


import com.alibaba.fastjson.JSONObject;
import com.hengjian.common.log.enums.BusinessType;
import com.zsmall.common.domain.vo.XxlJobSearchVO;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.tiktok.TikTokApiEnums;
import com.zsmall.common.enums.tiktok.TikTokEnum;
import com.zsmall.extend.shop.tiktok.job.entity.iservice.ITiktokSyncOrderAddressService;
import com.zsmall.extend.shop.tiktok.job.entity.iservice.ITiktokSyncOrderItemService;
import com.zsmall.extend.shop.tiktok.job.entity.iservice.ITiktokSyncOrderService;
import com.zsmall.extend.shop.tiktok.job.factory.ThirdOrderApiFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/31 14:30
 */
@SuppressWarnings("unchecked")
@Slf4j
@Component
@RequiredArgsConstructor
public class TiktokOrderSupport {
    private final ITiktokSyncOrderService iTiktokSyncOrderService;
    private final ITiktokSyncOrderItemService iTiktokSyncOrderItemService;
    private final ITiktokSyncOrderAddressService iTiktokSyncOrderAddressService;

    @Resource
    private ThirdOrderApiFactory apiFactory;
//    private final ThirdOrderOperationFactory operationFactory;

    /**
     * 功能描述：获取所有订单
     *  json 里包含query和body和shopId
     * @param vo          VO
     * @param targetClass 目标类
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/02/02
     */
    public <T> T getAllOrder(XxlJobSearchVO vo, Class<T> targetClass, String tiktokAppKey,
                             String tiktokAppSecret) {
        JSONObject json = (JSONObject) JSONObject.toJSON(vo);
        // shopId 是所有的shopId 更新渠道下 就是 tenantSalesChannel 表内的所有id

        return (T) apiFactory.getInvokeStrategy(TikTokEnum.TIK_TOK_ORDER.getEnumType())
                         .dataPlatformFlow(BusinessType.IMPORT,json , ChannelTypeEnum.TikTok, TikTokApiEnums.SEARCH_PRODUCTS,vo.getShopId(),targetClass ,tiktokAppKey,tiktokAppSecret);
    }

    public <T> T  getAllProduct(XxlJobSearchVO vo, Class<T> targetClass, String tiktokAppKey,
                                String tiktokAppSecret) {
        JSONObject json = (JSONObject) JSONObject.toJSON(vo);
        return (T) apiFactory.getInvokeStrategy(TikTokEnum.TIK_TOK_ORDER.getEnumType())
                             .dataPlatformFlow(BusinessType.IMPORT,json , ChannelTypeEnum.TikTok, TikTokApiEnums.SEARCH_PRODUCTS,vo.getShopId(),targetClass ,tiktokAppKey,tiktokAppSecret);
    }
}
