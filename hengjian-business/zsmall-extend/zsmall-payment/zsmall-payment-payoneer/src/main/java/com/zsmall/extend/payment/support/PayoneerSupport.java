package com.zsmall.extend.payment.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.redis.utils.RedisUtils;
import com.ijpay.core.IJPayHttpResponse;
import com.ijpay.payoneer.PayoneerApi;
import com.ijpay.payoneer.PayoneerApiConfig;
import com.ijpay.payoneer.PayoneerApiConfigKit;
import com.ijpay.payoneer.accesstoken.AccessToken;
import com.ijpay.payoneer.accesstoken.AccessTokenKit;
import com.ijpay.payoneer.constants.PayoneerConstants;
import com.ijpay.payoneer.enums.PayoneerApiUrl;
import com.ijpay.payoneer.exception.IJPayPayoneerException;
import com.ijpay.payoneer.model.BaseResponseModel;
import com.ijpay.payoneer.model.in.InPaymentDebit;
import com.ijpay.payoneer.model.out.*;
import com.zsmall.common.constant.PayoneerConstant;
import com.zsmall.common.enums.payoneer.PayoneerEnums;
import com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.PayoneerRequest;
import com.zsmall.extend.payment.bean.pay.PayoneerListsResponse.PayoneerListsResponse;
import com.zsmall.extend.payment.config.properties.PayoneerProperties;
import com.zsmall.extend.payment.utils.HttpUtil;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class PayoneerSupport {

    @Value("${distribution.payoneer.userName}")
    public String payoneerUserName;
    @Value("${distribution.payoneer.token}")
    public String payoneerToken;
    @Value("${distribution.payoneer.shopCode}")
    public String payoneerShopCode;
    private static final String PAYONEER_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + "ZSMALL:PAYONEER:PAYMENT:";
    private static final String CHARGE_FEE = "charge_fee";

    private final PayoneerProperties payoneerProperties;


    /**
     * 初始化Payoneer配置
     *
     * @return
     */
    public PayoneerApiConfig getConfig() {
        PayoneerApiConfig apiConfig = PayoneerApiConfigKit.getApiConfig();
        if (apiConfig == null) {
            throw new IllegalStateException("API configuration is not initialized.");
        }
        return apiConfig;
    }

    /**
     * 根据租户Id获取授权重定向地址
     *
     * @param tenantId
     * @return
     */
    public RegistrationLinkModel getRegistrationLink(String tenantId) {
        log.info("进入 getRegistrationLink 方法");
        String payeeId = UUID.fastUUID().toString(true);
        String redisKey = this.getRedisKey(payeeId);
        RedisUtils.setCacheObject(redisKey, tenantId, Duration.ofSeconds(payoneerProperties.getAuthorizeEffectiveTime()));
        RegistrationLinkModel registrationLinkModel = PayoneerApi.getRegistrationLink(getConfig(), payeeId);
        log.info("getRegistrationLink - registrationLinkModel = {}", JSONUtil.toJsonStr(registrationLinkModel));

        return registrationLinkModel;
    }

    /**
     * 根据payoneer的账户id accountId获取payoneer余额列表
     *
     * @param accountId
     * @return
     */
    public BalanceList getPayoneerBalanceList(String accountId) {
        //获取payoneer账户余额列表
        BalanceList balances = PayoneerApi.getBalances(getConfig(), accountId);
        return balances;
    }

    /**
     * 向账户付款
     *
     * @param accountId      payoneer账户id
     * @param inPaymentDebit 付款所需参数
     * @return PaymentDebitModel 付款明细
     */
    public PaymentDebitModel paymentDebit(String accountId, InPaymentDebit inPaymentDebit) {
        log.info("进入 paymentDebit 方法");
        //向账户付款
        PaymentDebitModel paymentDebitModel = PayoneerApi.paymentDebit(getConfig(), accountId, inPaymentDebit);
        log.info("paymentDebit paymentDebitModel = {}", JSONUtil.toJsonStr(paymentDebitModel));
        return paymentDebitModel;
    }

    /**
     * payoneer支付确认操作
     *
     * @param accountId payoneer账户id
     * @param commitId  确认提交 id
     * @return
     */
    public PaymentCommitModel<?> paymentCommit(String accountId, String commitId) {
        log.info("进入 paymentCommit 方法");
        PaymentCommitModel<?> paymentCommitModel = PayoneerApi.paymentCommit(getConfig(), accountId, commitId);
        log.info("paymentCommit paymentCommitModel = {}", JSONUtil.toJsonStr(paymentCommitModel));
        return paymentCommitModel;
    }


    /**
     * 根据403响应路径-用户二次确认后，查询支付提交内容
     *
     * @param accountId
     * @param responsePath
     * @return
     */
    public PaymentCommit getFromResponsePath(String accountId, String responsePath) {
        log.info("进入 getFromResponsePath 方法");
//        PaymentCommit paymentCommit = PayoneerApi.getFromResponsePath(getConfig(), accountId, responsePath);
//        log.info("getFromResponsePath paymentCommit = {}", JSONUtil.toJsonStr(paymentCommit));
        PaymentCommit paymentCommit = null;
        try {
            Map<String, String> headers = getAuthHeaders(getConfig(), accountId);
            Map<String, String> urlReplaces = new HashMap(1);
            urlReplaces.put("{response_path}", responsePath);
            IJPayHttpResponse httpResponse = PayoneerApi.get(getReqUrl(getConfig(), PayoneerApiUrl.PAYMENTS_RESPONSE_PATH, urlReplaces), headers);
            log.debug("httpResponse => {}", httpResponse.toString());
            int status = httpResponse.getStatus();
            if (status == 200) {
                List<String> contentEncodingList = httpResponse.getHeaders().get("Content-Encoding");
                if(CollectionUtil.isNotEmpty(contentEncodingList) && StringUtils.isNotEmpty(contentEncodingList.get(0)) && contentEncodingList.get(0).equals("gzip")){
                    cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(new String(httpResponse.getBodyByte()), false);
                    ResultModel<PaymentCommit> resultModel = (ResultModel)jsonObject.toBean(new TypeReference<ResultModel<PaymentCommit>>() {
                    });
                    paymentCommit = resultModel.getResult();
                    return paymentCommit;
                }
            } else {
                BaseResponseModel baseResponseModel = JSONUtil.toBean(httpResponse.getBody(), BaseResponseModel.class);
                throw new IJPayPayoneerException(baseResponseModel);
            }
        } catch (Throwable var9) {
            throw var9;
        }
        return paymentCommit;
    }

    private static Map<String, String> getAuthHeaders(PayoneerApiConfig config, String accountId) {
        return getAuthHeaders(config, accountId, false);
    }

    private static Map<String, String> getAuthHeaders(PayoneerApiConfig config, String accountId, boolean isForm) {
        AccessToken accessToken = AccessTokenKit.get(accountId);
        String token = accessToken.getAccessToken();
        String tokenType = accessToken.getTokenType();
        Map<String, String> headers = new HashMap(3);
        headers.put("Accept", ContentType.JSON.toString());
        headers.put("Content-Type", isForm ? ContentType.FORM_URLENCODED.toString() : ContentType.JSON.toString());
        headers.put("Authorization", tokenType + " " + token);
        log.debug("headers = {}", JSONUtil.toJsonStr(headers));
        return headers;
    }

    private static String getReqUrl(PayoneerApiConfig config, PayoneerApiUrl payoneerApiUrl, Map<String, String> replaces) {
        String concat = (config.isSandBox() ? PayoneerApiUrl.SANDBOX_GATEWAY.getUrl() : PayoneerApiUrl.LIVE_GATEWAY.getUrl()).concat(payoneerApiUrl.getUrl());
        if (StrUtil.contains(concat, "{program_id}")) {
            concat = StrUtil.replace(concat, "{program_id}", config.getProgramId());
        }

        Map.Entry entry;
        if (MapUtil.isNotEmpty(replaces)) {
            for(Iterator var4 = replaces.entrySet().iterator(); var4.hasNext(); concat = StrUtil.replace(concat, (CharSequence)entry.getKey(), (CharSequence)entry.getValue())) {
                entry = (Map.Entry)var4.next();
            }
        }

        log.debug("requestUrl = {}", concat);
        return concat;
    }

    /**
     * 通过accountId 和 paymentId 获取 paymentStatus
     *
     * @param accountId
     * @param paymentId
     * @return
     */
    public PaymentStatusModel getPaymentByAccountIdAndPaymentId(String accountId, String paymentId) {
        PaymentStatusModel paymentStatus = PayoneerApi.getPaymentStatus(getConfig(), accountId, paymentId);
        log.info("getPayment paymentStatus = {}", JSONUtil.toJsonStr(paymentStatus));
        return paymentStatus;
    }

    /**
     * 通过accountId 和 clientReferenceId 获取 paymentStatus
     *
     * @param accountId
     * @param clientReferenceId
     * @return
     */
    public PaymentStatusModel getPaymentByAccountIdAndClientReferenceId(String accountId, String clientReferenceId) {
        PaymentStatusModel paymentStatus = PayoneerApi.getPaymentStatus(getConfig(), accountId, null, clientReferenceId);
        log.info("getPayment paymentStatus = {}", JSONUtil.toJsonStr(paymentStatus));
        return paymentStatus;
    }

    /**
     * 刷新客户端token
     */
    public void refreshClientToken() {
        this.refreshClientToken(null);
    }

    /**
     * 刷新客户端token
     * @param tokenExpiredDay 过期提前时间
     */
    public void refreshClientToken(Integer tokenExpiredDay) {
        log.info("refreshClientToken: tokenExpiredDay = {}", tokenExpiredDay);
        PayoneerApiConfig config = this.getConfig();

        if(tokenExpiredDay == null) {
            tokenExpiredDay = payoneerProperties.getAdditionalTokenExpiredDay();
        }

        String programId = config.getProgramId();
        String key = GlobalConstants.GLOBAL_REDIS_KEY + AccessTokenKit.KEY_PREFIX_CLIENT_TOKEN + programId;
        log.info("refreshClientToken programId = {}", programId);
        try {
            String tokenJsonString = RedisUtils.getCacheObject(key);
            boolean forceRefresh = StrUtil.isBlank(tokenJsonString);
            log.info("refreshClientToken forceRefresh =1= {}", forceRefresh);
            if (!forceRefresh) {
                AccessToken accessToken = new AccessToken(tokenJsonString, 200);
                log.info("ClientToken accessToken = {}", JSONUtil.toJsonStr(accessToken));
                Long expiredTime = accessToken.getExpiredTime();
                // 判断过期时间是否存在
                if(null != expiredTime){
                    long newExpiredTimeLong = expiredTime - tokenExpiredDay * PayoneerConstants.TOKEN_EXPIRED_ADVANCE_SECONDS * 1000L;
                    long currentTimeMillis = System.currentTimeMillis();
                    log.info("expiredTime = {}, tokenExpiredDay = {}, newExpiredTimeLong = {}",
                        expiredTime, tokenExpiredDay, newExpiredTimeLong);

                    forceRefresh = currentTimeMillis > newExpiredTimeLong;
                    log.info("newExpiredTimeLong = {}, currentTimeMillis = {}, forceRefresh = {}",
                        newExpiredTimeLong, currentTimeMillis, forceRefresh);
                }
            }
            log.info("refreshClientToken forceRefresh =2= {}", forceRefresh);
            if (forceRefresh) {
                AccessToken clientToken = AccessTokenKit.getClientToken(programId, true);
                log.info("ClientToken accessToken new = {}", JSONUtil.toJsonStr(clientToken));
            }
        } catch (Exception e) {
            log.error("客户端Token刷新异常" + e.getMessage(), e);
        }
    }

    /**
     * 刷新访问token
     * @param accountId p卡账户Id
     */
    public boolean refreshAccessToken(String accountId) {
        return this.refreshAccessToken(null, accountId);
    }

    /**
     * 刷新访问token
     * @param accountId p卡账户Id
     */
    public boolean refreshAccessToken(Integer tokenExpiredDay, String accountId) {
        log.info("refreshAccessToken: accountId = {}, tokenExpiredDay = {}", accountId, tokenExpiredDay);
        PayoneerApiConfig config = this.getConfig();

        if(tokenExpiredDay == null) {
            tokenExpiredDay = payoneerProperties.getAdditionalTokenExpiredDay();
        }

        String key = GlobalConstants.GLOBAL_REDIS_KEY + AccessTokenKit.KEY_PREFIX_ACCESS_TOKEN + accountId;
        log.info("accountId:{} ,redisKey:{}",accountId,key);
        String tokenJsonString = RedisUtils.getCacheObject(key);
        log.info("refreshAccessToken key = {}, tokenJsonString = {}", key, tokenJsonString);
        try {
            if (StrUtil.isBlank(tokenJsonString)) {
                log.error("accountId = {} 无AccessToken，无法发起重新获取token", accountId);
                return false;
            }

            AccessToken accessToken = new AccessToken(tokenJsonString, 200);
            log.info("accountId = {},AccessToken = {}",accountId, JSONUtil.toJsonStr(accessToken));

            Long expiredTime = accessToken.getExpiredTime();
            // 判断过期时间是否存在，不存在重新获取
            boolean forceRefresh = true;
//            if(null != expiredTime){
//                long newExpiredTimeLong = expiredTime - tokenExpiredDay * PayoneerConstants.TOKEN_EXPIRED_ADVANCE_SECONDS * 1000L;
//                long currentTimeMillis = System.currentTimeMillis();
//                log.info("expiredTime = {}, tokenExpiredDay = {}, newExpiredTimeLong = {}",
//                    expiredTime, tokenExpiredDay, newExpiredTimeLong);
//
//                forceRefresh = currentTimeMillis > newExpiredTimeLong;
//                log.info("newExpiredTimeLong = {}, currentTimeMillis = {}, forceRefresh = {}",
//                    newExpiredTimeLong, currentTimeMillis, forceRefresh);
//            }
            log.info("refreshAccessToken forceRefresh {}", forceRefresh);
            if (forceRefresh) {
                AccessToken clientToken = AccessTokenKit.get(accountId, true);
                log.info("accountId = {},AccessToken new = {}",accountId, JSONUtil.toJsonStr(clientToken));
            }
            return true;
        } catch (Exception e) {
            log.error("AccessToken刷新异常" + e.getMessage(), e);
        }
        return false;
    }

    /**
     * 获取redis key，是由于回调需要增加全局前缀，避免无法获取缓存
     * @param key
     * @return
     */
    public String getRedisKey(String key) {
        return PAYONEER_PREFIX + key;
    }

    /**
     * 获取重定向地址，并根据需求拼接参数
     * @param builder
     * @return
     */
    public String getLastRedirectUrl(UrlBuilder builder) {
        String url = payoneerProperties.getLastRedirectUrl();
        String connect = url.indexOf("?") > 0 ? "&" : "?";
        url = url + connect + builder.getQueryStr();

        return url;
    }

    /**
     * 根据授权码获取账号Id
     * @param code
     * @return
     */
    public String getAccountId(String code) {
        PayoneerApiConfig config = this.getConfig();
        String accountId = PayoneerApi.getAccountId(config, code);
        log.info("getUserAccount - accountId = {}", accountId);
        return accountId;
    }

    /**
     * state解密，并转换成对应规则内容集合
     * @param state
     * @return
     */
    public List<String> decryptState(String state) {
        String decryptState = PayoneerApi.decryptState(this.getConfig(), state);
        List<String> strings = StrUtil.split(decryptState, "_");
        return strings;
    }

    /**
     * 计算总手续费， 目前取charge_fee
     *
     * @param currency
     * @param fees
     * @return
     */
    public BigDecimal calculateFee(String currency, List<Fee> fees) {
        BigDecimal handlingFee = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(fees)) {
            for (Fee f : fees) {
                if (StrUtil.equals(currency, f.getCurrency()) && StrUtil.equals(CHARGE_FEE, f.getType())) {
                    handlingFee = NumberUtil.add(handlingFee, f.getAmount());
                }
            }
        }
        return handlingFee;
    }

    /**
     * 获取支付再次确认，重定向地址
     * @param url
     * @return
     */
    public String getReconfirmRedirect(String url) {
        return url + "&redirect_uri=" + URLEncodeUtil.encode(payoneerProperties.getRedirectUrl(), StandardCharsets.UTF_8);
    }

    /**
     * 收单服务 创建支付请求
     *
     * @param payoneerRequest
     * @return
     */
    public PayoneerListsResponse createPayRequest(@NonNull PayoneerRequest payoneerRequest){
        payoneerRequest.setDivision(payoneerShopCode);
        String url = PayoneerConstant.SAND_BOX_URL + PayoneerEnums.GET_ACCESS_TOKEN.getPath();
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization","Basic " + Base64.getEncoder().encodeToString((payoneerUserName + ":" + payoneerToken).getBytes(StandardCharsets.UTF_8)));
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(payoneerRequest);
        String result = HttpUtil.postApi(headerMap, url, jsonObject.toJSONString());
        PayoneerListsResponse payoneerListsResponse = JSON.parseObject(result, PayoneerListsResponse.class);
        log.info("派安盈创建支付请求返回："+payoneerListsResponse);
        return payoneerListsResponse;
    }
}
