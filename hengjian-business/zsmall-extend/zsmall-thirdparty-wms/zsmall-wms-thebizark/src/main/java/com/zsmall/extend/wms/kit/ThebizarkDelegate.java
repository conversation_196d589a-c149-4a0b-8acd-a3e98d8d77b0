package com.zsmall.extend.wms.kit;

import com.zsmall.extend.wms.api.*;
import com.zsmall.extend.wms.model.ThebizarkBean;

/**
 * 恒健仓库委托管理类
 */
public class ThebizarkDelegate {

    private ThebizarkBean thebizarkBean;

    /**
     * 商品API
     */
    private ProductApi productApi;

    /**
     * 订单API
     */
    private OrderApi orderApi;

    /**
     * 销售退货计划API
     */
    private InboundPlanApi inboundPlanApi;

    /**
     * 仓库API
     */
    private WarehousingApi warehousingApi;

    /**
     * 库存API
     */
    private StockApi stockApi;

    public ThebizarkDelegate(ThebizarkBean thebizarkBean) {
        this.thebizarkBean = thebizarkBean;
    }

    public ProductApi productApi() {
        return new ProductApi(thebizarkBean);
    }

    public OrderApi orderApi() {
        return new OrderApi(thebizarkBean);
    }

    public InboundPlanApi inboundPlanApi() {
        return new InboundPlanApi(thebizarkBean);
    }

    public StockApi stockApi() {
        return new StockApi(thebizarkBean);
    }

    public WarehousingApi warehousingApi() {
        return new WarehousingApi(thebizarkBean);
    }

}
