package com.zsmall.extend.wms.model;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 恒健仓库 配置信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThebizarkBean {

    /**
     * 域名
     */
    private String domain;
    /**
     * 版本号
     */
    private String version = "v1";
    /**
     * 授权码
     */
    private String authorization;
    /**
     * 渠道账户信息
     */
    private String accountName;

    public ThebizarkBean(String domain, String authorization, String accountName) {
        this.domain = domain;
        this.authorization = authorization;
        this.accountName = accountName;
    }
}
