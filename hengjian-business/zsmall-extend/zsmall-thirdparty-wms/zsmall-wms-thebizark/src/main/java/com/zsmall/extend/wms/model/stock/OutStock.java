package com.zsmall.extend.wms.model.stock;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 响应信息-查询库存
 */
@Setter
@Getter
@Accessors(chain = true)
public class OutStock {

    /**
     * 仓库code
     */
    private String warehouseCode;

    /**
     * 项目名称
     */
    private String lineName;

    /**
     * 产品SKU
     */
    private String sku;

    /**
     * 库存类型 0：可售库存  1： 不可售库存
     */
    private Integer inventoryType;

    /**
     * 可用库存
     */
    private Integer inventoryAvailable;

    /**
     * 预留库存
     */
    private Integer inventoryReserved;

    /**
     * 在途库存
     */
    private String inventoryTransport;

    /**
     * 当前总库存
     */
    private String inventoryCurrent;

    /**
     * 最近更新时间
     */
    private String updatedAt;
    /**
     * 仓库系统code
     */
    private String  warehouseSystemCode;
    /**
     * 单仓标识
     */
    private String singleWarehouseFlag;

}
