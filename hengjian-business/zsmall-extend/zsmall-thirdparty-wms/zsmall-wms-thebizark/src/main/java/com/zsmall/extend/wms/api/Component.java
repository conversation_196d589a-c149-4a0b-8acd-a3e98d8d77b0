package com.zsmall.extend.wms.api;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zsmall.extend.core.MallHttpResponse;
import com.zsmall.extend.wms.exception.ThebizarkException;
import com.zsmall.extend.wms.model.ThebizarkBean;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public abstract class Component {
    private final int SUCCESS_CODE = 200;
    private ThebizarkBean thebizarkBean;

    public Component(ThebizarkBean thebizarkBean) {
        this.thebizarkBean = thebizarkBean;
    }


    /**
     * 获取请求地址
     *
     * @param url
     * @return
     */
    protected String getRequestUrl(String url) {
        String version = this.thebizarkBean.getVersion();
        return this.thebizarkBean.getDomain() + "/" + version + url;
    }
    /**
     * 获取请求地址
     *
     * @param url
     * @return
     */
    protected String getRequestUrlNotExcludeVersion(String url) {
        return this.thebizarkBean.getDomain() + url;
    }

    /**
     * 获取授权请求头
     *
     * @return
     */
    protected Map<String, String> getAuthorizationHeader() {
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", this.thebizarkBean.getAuthorization());
        header.put("x-api-key", this.thebizarkBean.getAuthorization());
        header.put("Content-Type", "application/json");
        return header;
    }

    protected String getAccountName() {
        return this.thebizarkBean.getAccountName();
    }


    /**
     * 校验请求结果
     *
     * @param mallHttpResponse
     */
    protected void checkResultStatus(MallHttpResponse mallHttpResponse) {
        checkResultStatus(ContentType.JSON, mallHttpResponse);
    }

    /**
     * 校验请求结果
     *
     * @param contentType
     * @param mallHttpResponse
     */
    protected void checkResultStatus(ContentType contentType, MallHttpResponse mallHttpResponse) {
        String message = "";
        int statusCode = SUCCESS_CODE;

        if (Objects.equals(contentType, ContentType.JSON)) {
            JSONObject jsonObject = JSONUtil.parseObj(mallHttpResponse.getBody());
            if(ObjectUtil.isEmpty(jsonObject)){
                byte[] bodyByte = mallHttpResponse.getBodyByte();
                String bodyString = new String(bodyByte, StandardCharsets.UTF_8);
                jsonObject = new JSONObject(bodyString);
            }
            statusCode = jsonObject.getInt("statusCode");
            message = jsonObject.getStr("message");
        }

        if (statusCode != SUCCESS_CODE) {
            throw new ThebizarkException(statusCode, message);
        }
    }

}
