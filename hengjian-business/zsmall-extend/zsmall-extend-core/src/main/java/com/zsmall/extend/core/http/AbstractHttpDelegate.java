package com.zsmall.extend.core.http;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.StreamProgress;
import cn.hutool.core.net.SSLContextBuilder;
import cn.hutool.core.net.SSLProtocols;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.zsmall.extend.core.MallHttpResponse;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.KeyManager;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLSocketFactory;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.util.Map;

/**
 * Http 代理类
 */
@Slf4j
@NoArgsConstructor
public abstract class AbstractHttpDelegate {
    // 设置超时时间，单位毫秒
    private int timeout = 120000;

    public AbstractHttpDelegate(int timeout) {
        this.timeout = timeout;
    }

    /**
     * get 请求
     *
     * @param url 请求url
     * @return {@link String} 请求返回的结果
     */
    public String get(String url) {
        return HttpUtil.get(url, timeout);
    }

    /**
     * get 请求
     *
     * @param url 请求url
     * @param isRest 设置是否rest模式，rest模式下get请求不会把参数附加到URL之后
     * @return {@link String} 请求返回的结果
     */
    public String get(String url, boolean isRest) {
        return HttpUtil.createGet(url).timeout(timeout).setRest(isRest).execute().body();
    }

    /**
     * get 请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @return {@link String} 请求返回的结果
     */
    public String get(String url, Map<String, Object> paramMap) {
        return HttpUtil.get(url, paramMap, timeout);
    }

    /**
     * get 请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @param isRest 设置是否rest模式，rest模式下get请求不会把参数附加到URL之后
     * @return {@link String} 请求返回的结果
     */
    public String get(String url, Map<String, Object> paramMap, boolean isRest) {
        return HttpRequest.get(url).form(paramMap).timeout(timeout).setRest(isRest).execute().body();
    }

    /**
     * post 请求
     *
     * @param url  请求url
     * @param data 请求参数
     * @return {@link String} 请求返回的结果
     */
    public String post(String url, String data) {
        return HttpUtil.post(url, data, timeout);
    }

    /**
     * post 请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @return {@link String} 请求返回的结果
     */
    public String post(String url, Map<String, Object> paramMap) {
        return HttpUtil.post(url, paramMap, timeout);
    }

    /**
     * 上传文件
     *
     * @param url      请求url
     * @param data     请求参数
     * @param certPath 证书路径
     * @param certPass 证书密码
     * @param filePath 上传文件路径
     * @param protocol 协议
     * @return {@link String}  请求返回的结果
     */
    public String upload(String url, String data, String certPath, String certPass, String filePath, String protocol) {
        try {
            File file = FileUtil.newFile(filePath);
            SSLSocketFactory sslSocketFactory = getSslSocketFactory(certPath, null, certPass, protocol);
            return HttpRequest.post(url)
                .setSSLSocketFactory(sslSocketFactory)
                .header("Content-Type", "multipart/form-data;boundary=\"boundary\"")
                .form("file", file)
                .form("meta", data)
                .timeout(timeout)
                .execute()
                .body();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 上传文件（二进制）
     *
     * @param url      请求url
     * @param data     请求参数
     * @param certPath 证书路径
     * @param certPass 证书密码
     * @param filePath 上传文件路径
     * @param protocol 协议
     * @return {@link String}  请求返回的结果
     */
    public MallHttpResponse uploadBytes(String url, Map<String, String> headers, byte[] fileBytes, String fileName, Map<String, Object> paramMap) {
        MallHttpResponse response = new MallHttpResponse();
        HttpResponse httpResponse = HttpRequest.post(url)
            .addHeaders(headers)
            .form("file", fileBytes, fileName)
            .form(paramMap)
            .timeout(timeout)
            .execute();

        response.setBody(httpResponse.body());
        response.setStatus(httpResponse.getStatus());
        response.setHeaders(httpResponse.headers());
        return response;
    }

    /**
     * 上传文件
     *
     * @param url      请求url
     * @param data     请求参数
     * @param certPath 证书路径
     * @param certPass 证书密码
     * @param filePath 上传文件路径
     * @return {@link String}  请求返回的结果
     */
    public String upload(String url, String data, String certPath, String certPass, String filePath) {
        return upload(url, data, certPath, certPass, filePath, SSLProtocols.TLSv1);
    }

    /**
     * post 请求
     *
     * @param url      请求url
     * @param data     请求参数
     * @param certPath 证书路径
     * @param certPass 证书密码
     * @param protocol 协议
     * @return {@link String} 请求返回的结果
     */
    public String post(String url, String data, String certPath, String certPass, String protocol) {
        try {
            SSLSocketFactory socketFactory = getSslSocketFactory(certPath, null, certPass, protocol);
            return HttpRequest.post(url)
                .setSSLSocketFactory(socketFactory)
                .body(data)
                .timeout(timeout)
                .execute()
                .body();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * post 请求
     *
     * @param url      请求url
     * @param data     请求参数
     * @param certPath 证书路径
     * @param certPass 证书密码
     * @return {@link String} 请求返回的结果
     */
    public String post(String url, String data, String certPath, String certPass) {
        return post(url, data, certPath, certPass, SSLProtocols.TLSv1);
    }

    /**
     * post 请求
     *
     * @param url      请求url
     * @param data     请求参数
     * @param certFile 证书文件输入流
     * @param certPass 证书密码
     * @param protocol 协议
     * @return {@link String} 请求返回的结果
     */
    public String post(String url, String data, InputStream certFile, String certPass, String protocol) {
        try {
            SSLSocketFactory sslSocketFactory = getSslSocketFactory(null, certFile, certPass, protocol);
            return HttpRequest.post(url)
                .setSSLSocketFactory(sslSocketFactory)
                .body(data)
                .timeout(timeout)
                .execute()
                .body();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * post 请求
     *
     * @param url      请求url
     * @param data     请求参数
     * @param certFile 证书文件输入流
     * @param certPass 证书密码
     * @return {@link String} 请求返回的结果
     */
    public String post(String url, String data, InputStream certFile, String certPass) {
        return post(url, data, certFile, certPass, SSLProtocols.TLSv1);
    }

    /**
     * get 请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @param headers  请求头
     * @return {@link HttpResponse} 请求返回的结果
     */
    protected HttpResponse getToResponse(String url, Map<String, Object> paramMap, Map<String, String> headers) {
        return getToResponse(url, paramMap, headers, false);
    }
    /**
     * get 请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @param headers  请求头
     * @param isRest  设置是否rest模式，rest模式下get请求不会把参数附加到URL之后
     * @return {@link HttpResponse} 请求返回的结果
     */
    protected HttpResponse getToResponse(String url, Map<String, Object> paramMap, Map<String, String> headers, boolean isRest) {
        HttpRequest httpRequest = HttpRequest.get(url)
            .timeout(timeout)
            .setRest(isRest)
            .addHeaders(headers)
            .form(paramMap);
        log.info("httpRequest = {}", httpRequest);
        return httpRequest.execute();
    }

    /**
     * post 请求
     *
     * @param url     请求url
     * @param headers 请求头
     * @param data    请求参数
     * @return {@link HttpResponse} 请求返回的结果
     */
    protected HttpResponse postToResponse(String url, Map<String, String> headers, String data) {
        return postToResponse(url, headers, data, false);
    }

    /**
     * post 请求
     *
     * @param url     请求url
     * @param headers 请求头
     * @param data    请求参数
     * @param isRest  设置是否rest模式，rest模式下get请求不会把参数附加到URL之后
     * @return {@link HttpResponse} 请求返回的结果
     */
    protected HttpResponse postToResponse(String url, Map<String, String> headers, String data, boolean isRest) {
        HttpRequest httpRequest = HttpRequest.post(url)
            .timeout(timeout)
            .setRest(isRest)
            .addHeaders(headers)
            .body(data);
        log.info("httpRequest = {}", httpRequest);
        return httpRequest.execute();
    }
    protected HttpResponse postForFileToResponse(String url, Map<String, String> headers, String data, boolean isRest) {
        HttpRequest httpRequest = HttpRequest.post(url)
                                             .timeout(timeout)
                                             .setRest(isRest)
                                             .addHeaders(headers)
                                             .body(data);
        log.info("httpRequest = {}", httpRequest);
        return httpRequest.execute();
    }

    /**
     * post 请求
     *
     * @param url      请求url
     * @param headers  请求头
     * @param paramMap 请求参数
     * @return {@link HttpResponse} 请求返回的结果
     */
    protected HttpResponse postToResponse(String url, Map<String, String> headers, Map<String, Object> paramMap) {
        return postToResponse(url, headers, paramMap, false);
    }
    /**
     * post 请求
     *
     * @param url      请求url
     * @param headers  请求头
     * @param paramMap 请求参数
     * @param isRest  设置是否rest模式，rest模式下get请求不会把参数附加到URL之后
     * @return {@link HttpResponse} 请求返回的结果
     */
    protected HttpResponse postToResponse(String url, Map<String, String> headers, Map<String, Object> paramMap, boolean isRest) {
        HttpRequest httpRequest = HttpRequest.post(url).timeout(timeout).setRest(isRest).addHeaders(headers).form(paramMap).keepAlive(true);
        log.info("httpRequest = {}", httpRequest);
        return httpRequest.execute();
    }

    /**
     * patch 请求
     *
     * @param url      请求url
     * @param headers  请求头
     * @param paramMap 请求参数
     * @return {@link HttpResponse} 请求返回的结果
     */
    protected HttpResponse patchToResponse(String url, Map<String, String> headers, Map<String, Object> paramMap) {
        return HttpRequest.patch(url)
            .timeout(timeout)
            .addHeaders(headers)
            .form(paramMap)
            .execute();
    }

    /**
     * patch 请求
     *
     * @param url     请求url
     * @param headers 请求头
     * @param data    请求参数
     * @return {@link HttpResponse} 请求返回的结果
     */
    protected HttpResponse patchToResponse(String url, Map<String, String> headers, String data) {
        return HttpRequest.patch(url)
            .timeout(timeout)
            .addHeaders(headers)
            .body(data)
            .execute();
    }

    /**
     * delete 请求
     *
     * @param url     请求url
     * @param headers 请求头
     * @param data    请求参数
     * @return {@link HttpResponse} 请求返回的结果
     */
    protected HttpResponse deleteToResponse(String url, Map<String, String> headers, String data) {
        return HttpRequest.delete(url)
            .timeout(timeout)
            .addHeaders(headers)
            .body(data)
            .execute();
    }

    /**
     * delete 请求
     *
     * @param url      请求url
     * @param headers  请求头
     * @param paramMap 请求参数
     * @return {@link HttpResponse} 请求返回的结果
     */
    protected HttpResponse deleteToResponse(String url, Map<String, String> headers, Map<String, Object> paramMap) {
        return HttpRequest.delete(url)
            .timeout(timeout)
            .addHeaders(headers)
            .form(paramMap)
            .execute();
    }

    /**
     * put 请求
     *
     * @param url     请求url
     * @param headers 请求头
     * @param data    请求参数
     * @return {@link HttpResponse} 请求返回的结果
     */
    protected HttpResponse putToResponse(String url, Map<String, String> headers, String data) {
        HttpRequest httpRequest = HttpRequest.put(url).timeout(timeout).addHeaders(headers).body(data);
        log.info("httpRequest => {}", httpRequest);
        return httpRequest.execute();
    }

    /**
     * put 请求
     *
     * @param url      请求url
     * @param headers  请求头
     * @param paramMap 请求参数
     * @return {@link HttpResponse} 请求返回的结果
     */
    protected HttpResponse putToResponse(String url, Map<String, String> headers, Map<String, Object> paramMap) {
        HttpRequest httpRequest = HttpRequest.put(url)
            .timeout(timeout)
            .addHeaders(headers)
            .body(JSONUtil.toJsonStr(paramMap), "application/json");
        log.info("httpRequest => {}", httpRequest);
        return httpRequest.execute();
    }


    private KeyManager[] getKeyManager(String certPass, String certPath, InputStream certFile) throws Exception {
        KeyStore clientStore = KeyStore.getInstance("PKCS12");
        if (certFile != null) {
            clientStore.load(certFile, certPass.toCharArray());
        } else {
            clientStore.load(Files.newInputStream(Paths.get(certPath)), certPass.toCharArray());
        }
        KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
        kmf.init(clientStore, certPass.toCharArray());
        return kmf.getKeyManagers();
    }

    private SSLSocketFactory getSslSocketFactory(String certPath, InputStream certFile, String certPass, String protocol) throws Exception {
        SSLContextBuilder sslContextBuilder = SSLContextBuilder.create();
        sslContextBuilder.setProtocol(protocol);
        sslContextBuilder.setKeyManagers(getKeyManager(certPass, certPath, certFile));
        sslContextBuilder.setSecureRandom(new SecureRandom());
        return sslContextBuilder.buildChecked().getSocketFactory();
    }


    /**
     * get 请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @param headers  请求头
     * @return {@link MallHttpResponse} 请求返回的结果
     */
    public MallHttpResponse get(String url, Map<String, Object> paramMap, Map<String, String> headers) {
        return get(url, paramMap, headers, false);
    }

    /**
     * get 请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @param headers  请求头
     * @param isRest  设置是否rest模式，rest模式下get请求不会把参数附加到URL之后
     * @return {@link MallHttpResponse} 请求返回的结果
     */
    public MallHttpResponse get(String url, Map<String, Object> paramMap, Map<String, String> headers, boolean isRest) {
        MallHttpResponse response = new MallHttpResponse();
        HttpResponse httpResponse = getToResponse(url, paramMap, headers);
        log.info("httpResponse = {}", httpResponse);

        // 如果是压缩，或者 body无内容而bodyBytes有内容时，需设置下
        boolean gzip = httpResponse.isGzip();
        if (gzip) {
            response.setBodyByte(httpResponse.bodyBytes());
        } else {
            response.setBody(httpResponse.body());
        }
        response.setStatus(httpResponse.getStatus());
        response.setHeaders(httpResponse.headers());
        return response;
    }

    /**
     * post 请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @param headers  请求头
     * @return {@link MallHttpResponse}  请求返回的结果
     */
    public MallHttpResponse post(String url, Map<String, Object> paramMap, Map<String, String> headers) {
        return post(url, paramMap, headers, false);
    }

    /**
     * post 请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @param headers  请求头
     * @param isRest  设置是否rest模式，rest模式下get请求不会把参数附加到URL之后
     * @return {@link MallHttpResponse}  请求返回的结果
     */
    public MallHttpResponse post(String url, Map<String, Object> paramMap, Map<String, String> headers, boolean isRest) {
        MallHttpResponse response = new MallHttpResponse();
        HttpResponse httpResponse = postToResponse(url, headers, paramMap, isRest);
        response.setBody(httpResponse.body());
        response.setStatus(httpResponse.getStatus());
        response.setHeaders(httpResponse.headers());
        return response;
    }

    /**
     * post 请求
     *
     * @param url     请求url
     * @param data    请求参数
     * @param headers 请求头
     * @return {@link MallHttpResponse}  请求返回的结果
     */
    public MallHttpResponse post(String url, String data, Map<String, String> headers) {
        return post(url, data, headers, false);
    }
    public MallHttpResponse postForFile(String url, String data, Map<String, String> headers,
                                        HttpServletResponse response) throws IOException {
        return postForFileDown(url, data, headers, false,response);
    }

    /**
     * post 请求
     *
     * @param url     请求url
     * @param data    请求参数
     * @param headers 请求头
     * @param isRest  设置是否rest模式，rest模式下get请求不会把参数附加到URL之后
     * @return {@link MallHttpResponse}  请求返回的结果
     */
    public MallHttpResponse post(String url, String data, Map<String, String> headers, boolean isRest) {
        MallHttpResponse response = new MallHttpResponse();
        HttpResponse httpResponse = postToResponse(url, headers, data, isRest);
        log.info("httpResponse = {}", httpResponse);
        response.setBody(httpResponse.body());
        response.setStatus(httpResponse.getStatus());
        response.setHeaders(httpResponse.headers());
        return response;
    }

    public MallHttpResponse postForFileDown(String url, String data, Map<String, String> headers, boolean isRest,HttpServletResponse responseForDown) throws IOException {
        MallHttpResponse response = new MallHttpResponse();
        HttpResponse httpResponse = postForFileToResponse(url, headers, data, isRest);
        log.info("httpResponse = {}", httpResponse);
        int status = httpResponse.getStatus();
        if(400==status){
            throw new RuntimeException("SKU尚未同步,暂不支持到货计划下载");
        }
        ServletOutputStream outputStream = responseForDown.getOutputStream();

        httpResponse.writeBody(outputStream,true, new StreamProgress(){
            @Override
            public void start() {
                log.info("开始下载报表...");
            }

            @Override
            public void progress(long total, long progressSize) {

            }

            @Override
            public void finish() {
                log.info("报表下载完成");
            }
            }
        );

        return response;
    }

    /**
     * patch 请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @param headers  请求头
     * @return {@link MallHttpResponse}  请求返回的结果
     */
    public MallHttpResponse patch(String url, Map<String, Object> paramMap, Map<String, String> headers) {
        MallHttpResponse response = new MallHttpResponse();
        HttpResponse httpResponse = patchToResponse(url, headers, paramMap);
        response.setBody(httpResponse.body());
        response.setStatus(httpResponse.getStatus());
        response.setHeaders(httpResponse.headers());
        return response;
    }

    /**
     * patch 请求
     *
     * @param url     请求url
     * @param data    请求参数
     * @param headers 请求头
     * @return {@link MallHttpResponse}  请求返回的结果
     */
    public MallHttpResponse patch(String url, String data, Map<String, String> headers) {
        MallHttpResponse response = new MallHttpResponse();
        HttpResponse httpResponse = patchToResponse(url, headers, data);
        response.setBody(httpResponse.body());
        response.setStatus(httpResponse.getStatus());
        response.setHeaders(httpResponse.headers());
        return response;
    }

    /**
     * delete 请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @param headers  请求头
     * @return {@link MallHttpResponse}  请求返回的结果
     */
    public MallHttpResponse delete(String url, Map<String, Object> paramMap, Map<String, String> headers) {
        MallHttpResponse response = new MallHttpResponse();
        HttpResponse httpResponse = deleteToResponse(url, headers, paramMap);
        response.setBody(httpResponse.body());
        response.setStatus(httpResponse.getStatus());
        response.setHeaders(httpResponse.headers());
        return response;
    }

    /**
     * delete 请求
     *
     * @param url     请求url
     * @param data    请求参数
     * @param headers 请求头
     * @return {@link MallHttpResponse}  请求返回的结果
     */
    public MallHttpResponse delete(String url, String data, Map<String, String> headers) {
        MallHttpResponse response = new MallHttpResponse();
        HttpResponse httpResponse = deleteToResponse(url, headers, data);
        response.setBody(httpResponse.body());
        response.setStatus(httpResponse.getStatus());
        response.setHeaders(httpResponse.headers());
        return response;
    }

    /**
     * put 请求
     *
     * @param url      请求url
     * @param paramMap 请求参数
     * @param headers  请求头
     * @return {@link MallHttpResponse}  请求返回的结果
     */
    public MallHttpResponse put(String url, Map<String, Object> paramMap, Map<String, String> headers) {
        MallHttpResponse response = new MallHttpResponse();
        HttpResponse httpResponse = putToResponse(url, headers, paramMap);
        response.setBody(httpResponse.body());
        response.setStatus(httpResponse.getStatus());
        response.setHeaders(httpResponse.headers());
        return response;
    }

    /**
     * put 请求
     *
     * @param url     请求url
     * @param data    请求参数
     * @param headers 请求头
     * @return {@link MallHttpResponse}  请求返回的结果
     */
    public MallHttpResponse put(String url, String data, Map<String, String> headers) {
        MallHttpResponse response = new MallHttpResponse();
        HttpResponse httpResponse = putToResponse(url, headers, data);
        response.setBody(httpResponse.body());
        response.setStatus(httpResponse.getStatus());
        response.setHeaders(httpResponse.headers());
        return response;
    }

    public MallHttpResponse postWithGraphql(String url, String graphqlQueryString, String requestBodyString, Map<String, String> headers) {
        String graphqlQuery = "{\"query\": \"" + graphqlQueryString + "\",\"variables\":" + requestBodyString + "}";
        log.info("graphqlQuery = {}", graphqlQuery);
        MallHttpResponse response = new MallHttpResponse();
        HttpResponse httpResponse = postToResponse(url, headers, graphqlQuery, true);
        log.info("httpResponse = {}", httpResponse);
        response.setBody(httpResponse.body());
        response.setStatus(httpResponse.getStatus());
        response.setHeaders(httpResponse.headers());
        return response;
    }

}
