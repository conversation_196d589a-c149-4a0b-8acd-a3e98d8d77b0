package com.zsmall.order.biz.utils;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 11:11
 */

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zsmall.common.domain.tiktok.ReverseOrderListQuery;
import com.zsmall.common.domain.tiktok.TikTokAuthParam;
import com.zsmall.common.domain.tiktok.auth.TikTokAuthPro;
import com.zsmall.common.domain.tiktok.base.TikTokAuthCommonParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/26 10:51
 */
@Slf4j
@Component
public class TikTokUtil {

    //    @XxlConf(value = "bizark-multichannel.tiktok.shopId")
    //${distribution.tiktok.shopId}
    @Value("${distribution.tiktok.shopId}")
    private static String TIKTOK_SHOP_ID;

    @Value("${distribution.tiktok.pageSize}")
    private static String pageSize;

    @Resource
    private TikTokApiCallUtils tikTokApiCallUtils;


    /**
     * 构建 Tik Tok 身份验证通用参数
     * <AUTHOR> Theo
     * @param url  网址
     * @param path 路径
     * @return {@link TikTokAuthCommonParam}
     */
    public TikTokAuthCommonParam buildTikTokAuthCommonParam(String url, String path) {
        return TikTokAuthCommonParam.builder().url(url).path(path).build();
    }

//
//    /**
//     * 获得api调用结果
//     * <AUTHOR> Theo
//     * @param reqParam  req param
//     * @param url       网址
//     * @param path      路径
//     * @param shopId
//     * @return 响应JSON
//     */
//    public String getTikTokShopReturnResStr(Object reqParam, String url, String path, String shopId) {
//        TikTokAuthCommonParam query = preDataForCalculateTheSign(shopId);
//        TikTokAuthCommonParam build = dataWrapper(buildTikTokAuthCommonParam(url, path), ReverseOrderListQuery.class);
//        // 这里queryMap用于计算sign需要传足参数
//        return tikTokApiCallUtils.sendPostPro(sendBefore(build), sendBefore(query), JSONObject.toJSONString(reqParam), shopId);
//    }


    /**
     * 获得api调用结果
     * <AUTHOR> Theo
     * @param reqParam  req param
     * @param url       网址
     * @param path      路径
     * @param respParam Resp参数
     * @param shopId
     * @return {@link T}
     */
    public <T> T getTikTokShopReturn(Object reqParam, String url, String path, Class<T> respParam, String shopId,String tiktokAppKey,String tiktokAppSecret) {
        TikTokAuthCommonParam query = preDataForCalculateTheSign(shopId);
        TikTokAuthCommonParam build = dataWrapper(buildTikTokAuthCommonParam(url, path), ReverseOrderListQuery.class);


        // 这里queryMap用于计算sign需要传足参数
        String data = tikTokApiCallUtils.sendPostPro(sendBefore(build), sendBefore(query), JSONObject.toJSONString(reqParam), shopId,tiktokAppKey,tiktokAppSecret);
        T responseMsg = JSONObject.parseObject(data, respParam);
        log.info(JSONObject.toJSONString(responseMsg));
        return responseMsg;
    }


    /**
     * 功能描述：获取 Tik Tok Shop Return V2
     *
     * @param reqParam  req 参数
     * @param url       网址
     * @param path      路径
     * @param respParam
     * @param map       地图
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/01/29
     */
    public <T> T postTikTokShopReturnV2(Object reqParam, String url, String path, Class<T> respParam, Map<String,Object> map,String tiktokAppKey,String tiktokAppSecret) {
        String shopId = (String) map.get("shop_id");

        HashMap<String, Object> urlAndPathMap = new HashMap<>();
        urlAndPathMap.put("url",url);
        urlAndPathMap.put("path",path);

        Map<String,Object> queryMap = (Map) JSONObject.parse((String) reqParam);
        for (Map.Entry<String, Object> entry : queryMap.entrySet()) {
            queryMap.put(entry.getKey(),entry.getValue());
        }

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            urlAndPathMap.put(entry.getKey(),entry.getValue());
        }

        log.info(queryMap.toString());

        // 这里queryMap用于计算sign需要传足参数
        String data = tikTokApiCallUtils.sendPostPro(urlAndPathMap, queryMap,JSONObject.toJSONString(reqParam), shopId,tiktokAppKey,tiktokAppSecret);
        T responseMsg = JSONObject.parseObject(data, respParam);
        log.info(JSONObject.toJSONString(responseMsg));
        return responseMsg;
    }

    /**
     * 功能描述：获取 Tik Tok Shop Return V2
     *  get方法,reqParam和map内容一致
     * @param reqParam  req 参数
     * @param url       网址
     * @param path      路径
     * @param respParam RESP 参数
     * @param map       包含query参数+shop_id(真实的商店id)
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/02/20
     */
    public <T> T getTikTokShopReturnV2(Object reqParam, String url, String path, Class<T> respParam, Map<String,Object> map,String tiktokAppKey,String tiktokAppSecret) {
        String shopId = (String) map.get("shop_id");

        HashMap<String, Object> urlAndPathMap = new HashMap<>();
        urlAndPathMap.put("url",url);
        urlAndPathMap.put("path",path);
        Map<String,Object> queryMap = (Map) JSONObject.parse((String) reqParam);
        if(CollUtil.isNotEmpty(queryMap)){
            for (Map.Entry<String, Object> entry : queryMap.entrySet()) {
                queryMap.put(entry.getKey(),entry.getValue());
            }
            log.info(queryMap.toString());
        }

        if(CollUtil.isNotEmpty(map)){
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                urlAndPathMap.put(entry.getKey(),entry.getValue());
            }
        }

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            urlAndPathMap.put(entry.getKey(),entry.getValue());
        }

        // 这里queryMap用于计算sign需要传足参数
        String data = tikTokApiCallUtils.sendGetPro(urlAndPathMap, queryMap,JSONObject.toJSONString(reqParam), shopId, tiktokAppKey, tiktokAppSecret);
        T responseMsg = JSONObject.parseObject(data, respParam);
        log.info(JSONObject.toJSONString(responseMsg));
        return responseMsg;
    }


    /**
     * JSON对象到逆向订单数据
     * <AUTHOR> Theo
     * @param json 杰森
     * @param t    t
     * @return {@link T}
     */
    public T jsonObjectToObject(JSONObject json, Class<T> t) {
        return JSON.toJavaObject(json, t);
    }

    /**
     * 获得api调用结果
     * sendGet
     * <AUTHOR> Theo
     * @param reqParam  req param
     * @param url       网址
     * @param path      路径
     * @param respParam Resp参数
     * @param shopId
     * @return {@link T}
     */
    public <T> T getTikTokShopReturnByGet(Object reqParam, String url, String path, Class<T> respParam, String shopId,String tiktokAppKey,String tiktokAppSecret) {
        ReverseOrderListQuery query = (ReverseOrderListQuery) preDataForCalculateTheSign(shopId);
        ReverseOrderListQuery build = (ReverseOrderListQuery) dataWrapper(buildTikTokAuthCommonParam(url, path));
        // 这里queryMap用于计算sign需要传足参数
        String data = tikTokApiCallUtils.sendGetPro(sendBefore(build), sendBefore(query), JSONObject.toJSONString(reqParam), shopId,tiktokAppKey , tiktokAppSecret);
        T responseMsg = JSONObject.parseObject(data, respParam);
        log.info(JSONObject.toJSONString(responseMsg));
        return responseMsg;
    }


    /**
     * 常见参数的装饰器
     * <AUTHOR> Theo
     * @param commonParam 常见参数
     * @return {@link TikTokAuthCommonParam}
     */
    public TikTokAuthCommonParam dataWrapper(TikTokAuthCommonParam commonParam) {
        return ReverseOrderListQuery.builder().url(commonParam.getUrl()).path(commonParam.getPath()).build();
    }

    /**
     * 数据包装器
     * 常见参数的装饰器
     * <AUTHOR> Theo
     * @param commonParam 常见参数
     * @param base        基础
     * @return {@link T}
     */
    public <T> T dataWrapper(TikTokAuthCommonParam commonParam, Class<T> base) {
        return getTByObjectToJson(commonParam, base);
    }

    private static <T> T getTByObjectToJson(TikTokAuthCommonParam commonParam, Class<T> base) {
        JSONObject commonJson = (JSONObject) JSON.toJSON(commonParam);
        return JSON.toJavaObject(commonJson, base);
    }

    /**
     * 用于计算sign预数据query
     *
     * @return {@link TikTokAuthCommonParam}
     */
    public TikTokAuthCommonParam preDataForCalculateTheSign(Map map) {
        String shopId = (String) map.get("shopId");
        // 这里要拿到 id 去鉴权Auth里面拿加密过的shop_id
        TikTokAuthPro tikTokAuth = tikTokApiCallUtils.getTikTokAuth(Long.parseLong(shopId));
        String connectStr = tikTokAuth.getConnectStr();
        JSONObject jsonObject = JSONObject.parseObject(connectStr);
        Map queryMap = sendBefore(jsonObject,map);
        TikTokAuthParam build = TikTokAuthParam.builder().shopId((String) map.get("shop_id"))
                                               .pageSize(Integer.valueOf(pageSize))
                                               .pageToken((String) queryMap.get("pageToken")).build();

        return build;
    }

    public TikTokAuthCommonParam preDataForCalculateTheSign(String shopId) {
        // 这里要拿到 id 去鉴权Auth里面拿加密过的shop_id

        TikTokAuthPro tikTokAuth = tikTokApiCallUtils.getTikTokAuth(Long.parseLong(shopId));
        String connectStr = tikTokAuth.getConnectStr();
        JSONObject jsonObject = JSONObject.parseObject(connectStr);
        Map map = sendBefore(jsonObject);
        return ReverseOrderListQuery.builder().shopId((String) map.get("shop_id")).build();
    }

    /**
     * 用于计算sign预数据query
     *
     * @return {@link TikTokAuthCommonParam}
     */
    public <T> T preDataForCalculateTheSign(String shopId, Class<T> query) {
        // 这里要拿到 id 去鉴权Auth里面拿加密过的shop_id
        TikTokAuthPro tikTokAuth = tikTokApiCallUtils.getTikTokAuth(Long.parseLong(shopId));
        String connectStr = tikTokAuth.getConnectStr();
        JSONObject jsonObject = JSONObject.parseObject(connectStr);
        return JSON.toJavaObject(jsonObject, query);
    }

    /**
     * 发送前
     * notes :url+api+shop_id这样固定的类型返回命名commonParamsMap sign内部加密的shopId 等 返回 queryMap
     * <AUTHOR> Theo
     * @param o o
     * @return {@link Map}
     */
    public Map sendBefore(Object o) {
        // 拿到类型,根据类型拆分所有的数据填充到map然后根据map写入url里面 此处顺序不一定是url在前面
        HashMap hashMap = new HashMap<String, String>();
        BeanUtil.copyProperties(o, hashMap);
        hashMap.putIfAbsent("shopId", hashMap.put("shop_id", hashMap.remove("shopId")));
        return hashMap;
    }

    public Map sendBefore(Object o,Map<String, String> map) {
        // 拿到类型,根据类型拆分所有的数据填充到map然后根据map写入url里面 此处顺序不一定是url在前面
        HashMap hashMap = new HashMap<String, String>();
        BeanUtil.copyProperties(o, hashMap);
        hashMap.putIfAbsent("shopId", hashMap.put("shop_id", hashMap.remove("shopId")));
        for (Map.Entry<String, String> entry : map.entrySet()) {
            hashMap.putIfAbsent(entry.getKey(),entry.getValue());
        }
        return hashMap;
    }

}
