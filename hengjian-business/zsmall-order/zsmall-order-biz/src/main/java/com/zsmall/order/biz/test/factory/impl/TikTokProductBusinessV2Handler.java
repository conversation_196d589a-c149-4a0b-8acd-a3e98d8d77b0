package com.zsmall.order.biz.test.factory.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.common.domain.base.ApiReturnMsg;
import com.zsmall.common.domain.base.ExpressSheet;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.common.domain.resp.tiktok.product.extend.SalesAttribute;
import com.zsmall.common.domain.resp.tiktok.product.extend.SkuImg;
import com.zsmall.common.domain.resp.tiktok.product.extend.Skus;
import com.zsmall.common.domain.resp.tiktok.product.resp.ProductDetailsResp;
import com.zsmall.common.domain.resp.tiktok.product.data.RespDetailData;
import com.zsmall.common.domain.resp.tiktok.search.*;
import com.zsmall.common.domain.tiktok.domain.dto.base.TikTokRespBaseEntity;
import com.zsmall.common.domain.vo.XxlJobSearchVO;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.productMapping.MarkUpTypeEnum;
import com.zsmall.common.handler.AbstractThirdBusinessHandler;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.common.util.ImagesUtil;
import com.zsmall.order.biz.factory.ThirdOrderOperationFactory;
import com.zsmall.order.biz.utils.TikTokUtil;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.iservice.IProductMappingService;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zsmall.common.enums.tiktok.TikTokApiEnums.GET_PRODUCT;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/4 14:39
 */
@Slf4j
@Lazy
@Component("tiktokProductV2Handler")
@RequiredArgsConstructor
public class TikTokProductBusinessV2Handler extends AbstractThirdBusinessHandler {
    private final FileProperties fileProperties;
    public static final String ACCESS_TOKEN = "accessToken";
    public static final String REFRESH_TOKEN = "refreshToken";
    public static final String OPEN_ID = "openId";
    public static final String SHOP_CIPHER = "shopCipher";
    @Resource
    private ThirdOrderOperationFactory factory;
    @Resource
    private IProductMappingService iProductMappingService;
    @Resource
    private TikTokUtil tikTokUtil;
    private final ITenantSalesChannelService iTenantSalesChannelService;


    @Resource
    private ImagesUtil imagesUtil;

//    private final TenantSalesChannelService tenantSalesChannelService;

    @Override
    public void insertBusinessData(TikTokRespBaseEntity data, JSONObject json) {
        XxlJobSearchVO vo = JSON.toJavaObject(json, XxlJobSearchVO.class);
        // 线程池拆解包装
        List<ProductMapping> productMappings = parseTikTokDataForDistribution(data, vo);
        iProductMappingService.saveBatch(productMappings);
        // 拆解包装完后组入数据流转

    }

    @Override
    public List dataDismantling(TikTokRespBaseEntity data, JSONObject json) {
        return null;
    }

    @Override
    public void insertBusinessDataV2(List data, JSONObject json, ConcurrentHashMap businessNos,
                                     BusinessTypeMappingEnum mappingEnum, ChannelTypeEnum channelTypeEnum) {

    }

    @Override
    public ConcurrentHashMap<String, ConcurrentHashMap<String, String>> generateBusinessNo(List data) {
        return null;
    }




    @Override
    public boolean isExists(TikTokRespBaseEntity targetData) {
        TikTokRespForProduct tikTokRespForProduct = (TikTokRespForProduct) targetData;
        if(ObjectUtil.isEmpty(tikTokRespForProduct)){
            return true;
        }
        SearchProductResp searchProductResp = tikTokRespForProduct.getData();

        if(ObjectUtil.isEmpty(searchProductResp)){
            return true;
        }

        List<SearchProduct> products = searchProductResp.getProducts();
        if(ObjectUtil.isEmpty(products)){
            return true;
        }
        return false;
    }

    @Override
    public TikTokRespBaseEntity dataScreening(TikTokRespBaseEntity data, JSONObject json) {
        return data;
    }

    @Override
    public void expressSheet(ExpressSheet expressSheet, ApiReturnMsg apiReturnMsg,
                             AtomicInteger atomicInteger,JSON json) {

    }


    /**
     * 功能描述：解析 Tik Tok 数据进行分发
     *
     * @param data 数据
     * @param vo   VO
     * @return {@link List }<{@link OrderReceiveFromThirdDTO }>
     * <AUTHOR>
     * @date 2024/02/02
     */
    private List<ProductMapping> parseTikTokDataForDistribution(TikTokRespBaseEntity data, XxlJobSearchVO vo) {

        TikTokRespForProduct tikTokRespForProduct = (TikTokRespForProduct) data;
        SearchProductResp searchProductResp = tikTokRespForProduct.getData();

        List<SearchProduct> products = searchProductResp.getProducts();
        // 移除products内status!=ACTIVATE的商品
        products = products.stream()
                           .filter(product -> "ACTIVATE".equals(product.getStatus()))
                           .collect(Collectors.toList());

        ThreadPoolExecutor executor = new ThreadPoolExecutor(10, 20, 120L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

        ConcurrentHashMap<String, ProductMapping> productMappings = new ConcurrentHashMap<>();

        if (products == null) {
            return Collections.emptyList();
        }
        CountDownLatch downLatch = new CountDownLatch(products.size());

        for (SearchProduct searchProduct : products) {
            String tiktokAppKey = data.getAppKey();
            String tiktokAppSecret = data.getAppSecret();
            executor.submit(() -> {
                try {
                    getProductMapping(searchProduct, vo, productMappings,tiktokAppKey,tiktokAppSecret);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info("获取产品映射失败:{}", e.getMessage());
                }finally {
                    downLatch.countDown();
                }
            });

        }

        try {
            downLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }finally {
            executor.shutdown();
        }
        return new ArrayList<>(productMappings.values());
    }
    public void getProductMapping(SearchProduct searchProduct,XxlJobSearchVO vo,ConcurrentHashMap<String, ProductMapping> productMappings,String tiktokAppKey,String tiktokAppSecret){
        HashMap hashMap = new HashMap();
        String productId = searchProduct.getId();

        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getShopId(vo.getChannelId());
        String connectStr = tenantSalesChannel.getConnectStr();
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            log.error("tiktok auth error [{}] : accessToken is empty ", vo.getChannelId());
        }
        hashMap.put("shop_id", jsonObject.get("shopId"));
        hashMap.put("return_under_review_version", false);



        String jsonString = JSON.toJSONString(hashMap);
        RespDetailData detail = tikTokUtil.getTikTokShopReturnV2(jsonString, GET_PRODUCT.getUrl(), GET_PRODUCT.getPath()+searchProduct.getId(), RespDetailData.class, hashMap,tiktokAppKey,tiktokAppSecret);
        ProductDetailsResp skusMsg =null;
        if(ObjectUtil.isNotEmpty(detail)){
            skusMsg = detail.getData();
        }

        log.info("产品详情:{}", JSON.toJSONString(skusMsg));
        List<Skus> skusDetail = skusMsg.getSkus();
        Map<String,Skus> skuDetailMap =  skusDetail.stream().collect(Collectors.toMap(Skus::getSellerSku, each->each,(value1, value2) -> value1));
        ConcurrentHashMap<String, Skus> skusDetailMap = new ConcurrentHashMap<>(skuDetailMap);


        List<SearchProductSku> skus = searchProduct.getSkus();
        if(CollUtil.isNotEmpty(skus)){
            Map<String,SearchProductSku> map =  skus.stream().collect(Collectors.toMap(SearchProductSku::getSellerSku, each->each,(value1, value2) -> value1));
            ConcurrentHashMap<String, SearchProductSku> skusMap = new ConcurrentHashMap<>(map);

            Set<Map.Entry<String, SearchProductSku>> entries = skusMap.entrySet();

            for (Map.Entry<String, SearchProductSku> entry : entries) {

                String sellerSku = entry.getKey();
                SearchProductSku searchProductSku = entry.getValue();
                Skus skusVO = skusDetailMap.get(sellerSku);
                List<SalesAttribute> salesAttributes = skusVO.getSalesAttributes();

                SkuImg skuImg ;
                if(CollUtil.isNotEmpty(salesAttributes)){
                    skuImg = salesAttributes.get(0).getSkuImg();
                    List<String> urls;
                    if(ObjectUtil.isNotEmpty(skuImg)){
                        urls = skuImg.getUrls();
                        SysOssVo sysOssVo = imagesUtil.downloadImage(urls.get(0));
                        if(ObjectUtil.isNotNull(sysOssVo)){
                            searchProductSku.setImageSavePath(sysOssVo.getSavePath());
                            searchProductSku.setImageShowUrl(sysOssVo.getUrl());
                        }
                    }else {
                        log.error("当前sku:{}没有图片信息",sellerSku);
                    }
                }else{
                    log.error("当前sku:{}没有图片信息",sellerSku);
                }

                ProductMapping productMapping = new ProductMapping();
                ProductMapping mapping = iProductMappingService.getOne(new LambdaQueryWrapper<ProductMapping>().eq(ProductMapping::getChannelSku, searchProductSku.getSellerSku())
                                                                                                               .eq(ProductMapping::getTenantId, vo.getTenantId())
                                                                                                               .eq(ProductMapping::getDelFlag, 0)
                                                                                                               .last("limit 1"));
                // 后续放到上面去
                if(ObjectUtil.isNotEmpty(mapping)){
                    if (ObjectUtil.isEmpty(mapping.getImageSavePath())){
                        // 直接更新图片
                        mapping.setImageSavePath(searchProductSku.getImageSavePath());
                        mapping.setImageShowUrl(searchProductSku.getImageShowUrl());
                        iProductMappingService.updateById(mapping);

                    }
                    log.info("当前sku跳过:{}", searchProductSku.getSellerSku());
                    continue;
                }
                productMapping = productMappingWrapper(productMapping, searchProduct, searchProductSku, vo, productId);
                productMappings.put(searchProductSku.getSellerSku(), productMapping);

            }
        }

    }

    /**
     * 功能描述：产品映射包装器
     *
     * @param productMapping   产品映射
     * @param searchProduct    产品搜索
     * @param searchProductSku 搜索产品 SKU
     * @param vo               VO
     * @param productId
     * @return {@link ProductMapping }
     * <AUTHOR>
     * @date 2024/02/03
     */
    private ProductMapping productMappingWrapper(ProductMapping productMapping, SearchProduct searchProduct,
                                                 SearchProductSku searchProductSku, XxlJobSearchVO vo, String productId) {
        // 要额外接一个tiktok接口 目前先用 tiktok+sellerSku

        String productName = searchProduct.getTitle();

        String sellerSku = searchProductSku.getSellerSku();

//        productMapping.setProductCode(sellerSku);
        productMapping.setChannelSkuId(searchProductSku.getId());
        productMapping.setChannelProductId(productId);

        // 此处调用tiktok获取产品详情接口

        productMapping.setImageShowUrl(searchProductSku.getImageShowUrl());
        productMapping.setImageSavePath(searchProductSku.getImageSavePath());

        String channelType = ChannelTypeEnum.TikTok.name();

        productMapping.setChannelSku(sellerSku);

        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.valueOf(channelType);

        ProductMapping mapping = new ProductMapping();
        // 税后价格
        // 税前价格
        SearchPrice price = searchProductSku.getPrice();
        if (ObjectUtil.isNotEmpty(price)) {
            String taxExclusivePrice = price.getTaxExclusivePrice();
            if (ObjectUtil.isNotEmpty(taxExclusivePrice)) {
                productMapping.setTaxExclusivePrice(new BigDecimal(taxExclusivePrice));
            } else {
                productMapping.setTaxExclusivePrice(BigDecimal.ZERO);
            }
            String salePrice = price.getSalePrice();
            if(ObjectUtil.isNotEmpty(salePrice)){
                productMapping.setFinalPrice(new BigDecimal(salePrice));
            }else{
                productMapping.setFinalPrice(productMapping.getTaxExclusivePrice());
            }
        }else{
            productMapping.setTaxExclusivePrice(BigDecimal.ZERO);
            productMapping.setFinalPrice(BigDecimal.ZERO);
        }

        if (ObjectUtil.isNotEmpty(vo.getTenantId())) {
            MappingGenerateBoTikTok newGenerateBo = new MappingGenerateBoTikTok()
                .setChannelType(channelTypeEnum)
                .setProductName(productName)
                .setProductSkuCode(sellerSku)
                .setFinalPrice(productMapping.getFinalPrice());
            mapping = generateProductMapping(productMapping, newGenerateBo, vo);
            return mapping;
        }
        return productMapping;
    }

    /**
     * 生成商品映射信息
     *
     * @param productMapping
     * @param vo
     * @return
     */
    private ProductMapping generateProductMapping(ProductMapping productMapping, MappingGenerateBoTikTok generateBo,
                                                  XxlJobSearchVO vo) {
        ChannelTypeEnum channelType = generateBo.getChannelType();
        String productName = generateBo.getProductName();

        MarkUpTypeEnum markUpType = generateBo.getMarkUpType();
        BigDecimal markUpValue = generateBo.getMarkUpValue();
        BigDecimal finalPrice = generateBo.getFinalPrice();


        BigDecimal platformDropShippingPrice = BigDecimal.ZERO;


        BigDecimal basePrice;
        // Wayfair只支持自提，取自提价

        basePrice = platformDropShippingPrice;


        // 如果未填写价格，则默认根据商品支持的物流类型取自提/代发价
        if (finalPrice == null || NumberUtil.isLessOrEqual(finalPrice, BigDecimal.ZERO)) {
            finalPrice = basePrice;
        }


        productMapping.setActivityCode(null);
        productMapping.setActivityType(null);
        productMapping.setChannelType(channelType);
        productMapping.setChannelId(Long.valueOf(vo.getChannelId()));
        productMapping.setProductName(productName);

        productMapping.setMarkUpType(markUpType);
        productMapping.setMarkUpValue(markUpValue);
        productMapping.setFinalPrice(finalPrice);
        productMapping.setTenantId(vo.getTenantId());


        return productMapping;
    }

    /**
     * 生成商品映射信息入参Bo
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    class MappingGenerateBoTikTok {
        private ChannelTypeEnum channelType;
        private String productName;
        private String productSkuCode;
        private String activityCode;
        private MarkUpTypeEnum markUpType;
        private BigDecimal markUpValue;
        private BigDecimal finalPrice;
    }

    /**
     * 功能描述：抖音 wrapper
     *
     * @param tiktokDTO 抖音 DTO
     * @param order     次序
     * @param lineItem  订单项
     * @param vo        VO
     * @return {@link OrderReceiveFromThirdDTO }
     * <AUTHOR>
     * @date 2024/02/02
     */
//    private OrderReceiveFromThirdDTO tiktokDTOWrapper(OrderReceiveFromThirdDTO tiktokDTO, TikTokOrder order,
//                                                      TikTokLineItem lineItem, XxlJobSearchVO vo) {
//        tiktokDTO.setTenantId(vo.getTenantId());
//        tiktokDTO.setThirdChannelFlag(vo.getThirdChannelFlag());
//        tiktokDTO.setOrderNo(order.getTikTokOrderId());
//        tiktokDTO.setOrderStatus(order.getStatus());
//        // 要调用包裹api 获取
//        tiktokDTO.setTotalQuantity(0);
//        tiktokDTO.setCurrencyCode(lineItem.getCurrency());
//        try {
//            tiktokDTO.setCreateTime(MathTimeUtils.longToDate(order.getCreateTime()));
//        } catch (ParseException e) {
//            throw new RuntimeException(e);
//        }
//        String fulfillmentType = order.getFulfillmentType();
//        if (TiktokLogisticsTypeEnum.TIKTOK.name().equals(fulfillmentType)) {
//            fulfillmentType = LogisticsTypeEnum.PickUp.name();
//        }
//        if (TiktokLogisticsTypeEnum.SELLER.name().equals(fulfillmentType)) {
//            fulfillmentType = LogisticsTypeEnum.DropShipping.name();
//        }
//        tiktokDTO.setLogisticsType(fulfillmentType);
//        tiktokDTO.setRemark(order.getBuyerMessage());
//
//        // 物流
//        SaleOrderDetailDTO saleOrderDetailDTO = new SaleOrderDetailDTO();
//        saleOrderDetailDTO.setCarrier(order.getShippingProvider());
//        saleOrderDetailDTO.setLogisticsTrackingNo(order.getTrackingNumber());
//
//
//        // item详情
//        List<SaleOrderItemDTO> itemDTOS = new ArrayList<SaleOrderItemDTO>();
//        SaleOrderItemDTO itemDTO = new SaleOrderItemDTO();
//        itemDTO.setErpSku(lineItem.getSellerSku());
//        itemDTO.setQuantity(0);
//        itemDTO.setUnitPrice(new BigDecimal(lineItem.getSalePrice()));
//        //地址
//        TikTokRecipientAddress recipientAddress = order.getRecipientAddress();
//        recipientAddress.setBuyerEmail(order.getBuyerEmail());
//
//        tiktokDTO.setSaleOrderDetails(saleOrderDetailDTO);
//        tiktokDTO.pushSaleOrderItemsList(itemDTOS);
//        tiktokDTO.setAddress(recipientAddress);
//        return tiktokDTO;
//    }
}
