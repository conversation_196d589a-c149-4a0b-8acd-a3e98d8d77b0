package com.zsmall.order.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.domain.dto.*;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.dto.ErpSaleOrderDTO;
import com.zsmall.order.entity.domain.dto.ErpSaleOrderItemDTO;
import com.zsmall.order.entity.iservice.OrderCodeGenerator;
import com.zsmall.order.entity.mapper.OrderItemMapper;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/9 18:08
 */
@RequiredArgsConstructor
@Service
@Slf4j

public class OrderItemThirdServiceImpl extends ServiceImpl<OrderItemMapper, OrderItem> {

    private final OrderCodeGenerator orderCodeGenerator;
    private final IProductSkuService productSkuService;
    private final ISysTenantService sysTenantService;
    private final ITenantSalesChannelService iTenantSalesChannelService;

    /**
     * 功能描述： 三方 -设置订单标签系统
     *
     * @param orderItem              订购项目
     * @param erpSaleOrderDTO 从ERP DTO接收订单
     * @param orders                 订单
     * @param itemDTO                项目 dto
     * @return {@link OrderItem }
     * <AUTHOR>
     * @date 2024/01/09
     */
    public OrderItem setOrderTagSystem(OrderItem orderItem, ErpSaleOrderDTO erpSaleOrderDTO, Orders orders, ErpSaleOrderItemDTO itemDTO) {


        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductSku::getSku, itemDTO.getSellerSku());
        ProductSku productSku =TenantHelper.ignore(()->productSkuService.getOne(lqw));

        orderItem.setOrderId(orders.getId());
        orderItem.setOrderNo(orders.getOrderNo());
        // 原生订单逻辑
        String orderItemNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderItemNo);
        orderItem.setOrderItemNo(orderItemNo);
        orderItem.setProductSkuCode(productSku.getProductSkuCode());
        orderItem.setTotalQuantity(itemDTO.getQuantity());
        orderItem.setStockManager(Enum.valueOf(StockManagerEnum.class, StockManagerEnum.BizArk.name()));
        // 物流类型
        orderItem.setLogisticsType(LogisticsTypeEnum.DropShipping);
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityType(null);

        // 供货商租户编号-供货商的租户编号逻辑是跟着商品上的 租户id product_sku 表
        orderItem.setSupplierTenantId(productSku.getTenantId());
        // 要与产品协调 估计是建立一个henjian类似的角色 拿固定的id
        // 从店铺里拿对应的tenantId
        TenantSalesChannel tenantSalesChannel = TenantHelper.ignore(()->iTenantSalesChannelService.getByChannelFlag(erpSaleOrderDTO.getChannelFlag(), ChannelTypeEnum.MultiChannel.getValue()));
        orderItem.setTenantId(tenantSalesChannel.getTenantId());

        return orderItem;
    }

    public OrderItem setOrderTagSystem(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders, SaleOrderItemDTO itemDTO) {
        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
        if (ChannelTypeEnum.TikTok.name().equals(orders.getChannelType().name())){
            lqw.eq(ProductSku::getProductSkuCode, itemDTO.getErpSku());
        }else {
            lqw.eq(ProductSku::getSku, itemDTO.getErpSku());
        }

        ProductSku productSku = TenantHelper.ignore(()->productSkuService.getOne(lqw));

        orderItem.setOrderId(orders.getId());
        orderItem.setOrderNo(orders.getOrderNo());
        // 原生订单逻辑
        String orderItemNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderItemNo);
        orderItem.setOrderItemNo(orderItemNo);
        orderItem.setProductSkuCode(productSku.getProductSkuCode());
        orderItem.setTotalQuantity(itemDTO.getQuantity());
        orderItem.setStockManager(Enum.valueOf(StockManagerEnum.class, StockManagerEnum.BizArk.name()));
        // 物流类型
        // 根据channel区分
        if(ChannelTypeEnum.MultiChannel.equals(orderItem.getChannelType())){
            orderItem.setLogisticsType(LogisticsTypeEnum.DropShipping);
        }
        if(ChannelTypeEnum.Erp.equals(orderItem.getChannelType())){
            orderItem.setLogisticsType(LogisticsTypeEnum.PickUp);
        }
        if(ChannelTypeEnum.TikTok.equals(orderItem.getChannelType())){
            orderItem.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()));
        }
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityType(null);

        // 供货商租户编号-供货商的租户编号逻辑是跟着商品上的 租户id product_sku 表
        orderItem.setSupplierTenantId(productSku.getTenantId());
        // 要与产品协调 估计是建立一个henjian类似的角色 拿固定的id
        // 从店铺里拿对应的tenantId
//        SysTenant sysTenant =  sysTenantService.getTenantByThirdChannelFlag(orderReceiveFromErpDTO.getThirdChannelFlag());
        orderItem.setTenantId(orderReceiveFromThirdDTO.getTenantId());

        return orderItem;
    }
    public OrderItem setOrderTagSystemForOpen(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders, SaleOrderItemDTO itemDTO) {
        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductSku::getProductSkuCode, itemDTO.getItemNo());

        ProductSku productSku = TenantHelper.ignore(()->productSkuService.getOne(lqw));

        orderItem.setOrderId(orders.getId());
        orderItem.setOrderNo(orders.getOrderNo());
        // 原生订单逻辑
        String orderItemNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderItemNo);
        orderItem.setOrderItemNo(orderItemNo);
        orderItem.setProductSkuCode(productSku.getProductSkuCode());
        orderItem.setTotalQuantity(itemDTO.getQuantity());
        orderItem.setStockManager(Enum.valueOf(StockManagerEnum.class, StockManagerEnum.BizArk.name()));
        // 物流类型
        // 根据channel区分
//        if(ChannelTypeEnum.MultiChannel.equals(orderItem.getChannelType())){
//            orderItem.setLogisticsType(LogisticsTypeEnum.DropShipping);
//        }
//        if(ChannelTypeEnum.Erp.equals(orderItem.getChannelType())){
//            orderItem.setLogisticsType(LogisticsTypeEnum.PickUp);
//        }
//        if(ChannelTypeEnum.TikTok.equals(orderItem.getChannelType())){
//            orderItem.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()));
//        }
        orderItem.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()));
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityType(null);

        // 供货商租户编号-供货商的租户编号逻辑是跟着商品上的 租户id product_sku 表
        orderItem.setSupplierTenantId(productSku.getTenantId());
        // 要与产品协调 估计是建立一个henjian类似的角色 拿固定的id
        // 从店铺里拿对应的tenantId
//        SysTenant sysTenant =  sysTenantService.getTenantByThirdChannelFlag(orderReceiveFromErpDTO.getThirdChannelFlag());
        orderItem.setTenantId(orderReceiveFromThirdDTO.getTenantId());

        return orderItem;
    }

    public OrderItem setOrderTagSystemForTemu(OrderItem orderItem, TemuOrderDTO temuOrderDTO, Orders orders, TemuOrderItemDTO temuOrderItemDTO) {
//        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(ProductSku::getProductSkuCode, temuOrderItemDTO.getProductSkuCode());
//
//        ProductSku productSku = TenantHelper.ignore(()->productSkuService.getOne(lqw));

        ProductSku productSku = new ProductSku();
        if(StringUtils.isNotEmpty(temuOrderItemDTO.getProductSkuCode())){
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, temuOrderItemDTO.getProductSkuCode())));
        }

        orderItem.setOrderId(orders.getId());
        orderItem.setOrderNo(orders.getOrderNo());
        // 原生订单逻辑
        String orderItemNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderItemNo);
        orderItem.setOrderItemNo(orderItemNo);
        if(null != productSku){
            orderItem.setProductSkuCode(productSku.getProductSkuCode());
            orderItem.setSupplierTenantId(productSku.getTenantId());
        }
//        else {
//            orderItem.setSupplierTenantId("SJN1857");
//        }
        orderItem.setTotalQuantity(temuOrderItemDTO.getQuantity());
        orderItem.setStockManager(Enum.valueOf(StockManagerEnum.class, StockManagerEnum.BizArk.name()));
        // 物流类型
        // 根据channel区分
//        if(ChannelTypeEnum.MultiChannel.equals(orderItem.getChannelType())){
//            orderItem.setLogisticsType(LogisticsTypeEnum.DropShipping);
//        }
//        if(ChannelTypeEnum.Erp.equals(orderItem.getChannelType())){
//            orderItem.setLogisticsType(LogisticsTypeEnum.PickUp);
//        }
//        if(ChannelTypeEnum.TikTok.equals(orderItem.getChannelType())){
//            orderItem.setLogisticsType(temuOrderDTO.getLogisticsType());
//        }
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityType(null);

        // 供货商租户编号-供货商的租户编号逻辑是跟着商品上的 租户id product_sku 表

        // 要与产品协调 估计是建立一个henjian类似的角色 拿固定的id
        // 从店铺里拿对应的tenantId
//        SysTenant sysTenant =  sysTenantService.getTenantByThirdChannelFlag(orderReceiveFromErpDTO.getThirdChannelFlag());
        orderItem.setTenantId(temuOrderDTO.getTenantId());

        return orderItem;
    }


    public OrderItem setOrderTagSystemForAmazonVc(OrderItem orderItem, AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders, AmazonVCOrderItemMessageDTO amazonVCOrderItemMessageDTO) {
//        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(ProductSku::getProductSkuCode, temuOrderItemDTO.getProductSkuCode());
//
//        ProductSku productSku = TenantHelper.ignore(()->productSkuService.getOne(lqw));

        ProductSku productSku = new ProductSku();
        if(StringUtils.isNotEmpty(amazonVCOrderItemMessageDTO.getProductSkuCode())){
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, amazonVCOrderItemMessageDTO.getProductSkuCode())));
        }

        orderItem.setOrderId(orders.getId());
        orderItem.setOrderNo(orders.getOrderNo());
        // 原生订单逻辑
        String orderItemNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderItemNo);
        orderItem.setOrderItemNo(orderItemNo);
        if(null != productSku){
            orderItem.setProductSkuCode(productSku.getProductSkuCode());
            orderItem.setSupplierTenantId(productSku.getTenantId());
        }
//        else {
//            orderItem.setSupplierTenantId("SJN1857");
//        }
        orderItem.setTotalQuantity(amazonVCOrderItemMessageDTO.getQuantity());
        orderItem.setStockManager(Enum.valueOf(StockManagerEnum.class, StockManagerEnum.BizArk.name()));
        // 物流类型
        // 根据channel区分
//        if(ChannelTypeEnum.MultiChannel.equals(orderItem.getChannelType())){
//            orderItem.setLogisticsType(LogisticsTypeEnum.DropShipping);
//        }
//        if(ChannelTypeEnum.Erp.equals(orderItem.getChannelType())){
//            orderItem.setLogisticsType(LogisticsTypeEnum.PickUp);
//        }
//        if(ChannelTypeEnum.TikTok.equals(orderItem.getChannelType())){
//            orderItem.setLogisticsType(temuOrderDTO.getLogisticsType());
//        }
        orderItem.setFulfillmentTime(null);
//        orderItem.setActivityType(null);

        // 供货商租户编号-供货商的租户编号逻辑是跟着商品上的 租户id product_sku 表

        // 要与产品协调 估计是建立一个henjian类似的角色 拿固定的id
        // 从店铺里拿对应的tenantId
//        SysTenant sysTenant =  sysTenantService.getTenantByThirdChannelFlag(orderReceiveFromErpDTO.getThirdChannelFlag());
        orderItem.setTenantId(amazonVCOrderMessageDTO.getTenantId());

        return orderItem;
    }


    public OrderItem setOrderTagSystemForEc(OrderItem orderItem, EcOrderMessageDTO ecOrderMessageDTO, Orders orders, EcOrderItemMessageDTO ecOrderItemMessageDTO) {
//        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(ProductSku::getProductSkuCode, temuOrderItemDTO.getProductSkuCode());
//
//        ProductSku productSku = TenantHelper.ignore(()->productSkuService.getOne(lqw));

        ProductSku productSku = new ProductSku();
        if(StringUtils.isNotEmpty(ecOrderItemMessageDTO.getProductSkuCode())){
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, ecOrderItemMessageDTO.getProductSkuCode())));
        }

        orderItem.setOrderId(orders.getId());
        orderItem.setOrderNo(orders.getOrderNo());
        // 原生订单逻辑
        String orderItemNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderItemNo);
        orderItem.setOrderItemNo(orderItemNo);
        if(null != productSku){
            orderItem.setProductSkuCode(productSku.getProductSkuCode());
            orderItem.setSupplierTenantId(productSku.getTenantId());
        }
//        else {
//            orderItem.setSupplierTenantId("SJN1857");
//        }
        orderItem.setTotalQuantity(ecOrderItemMessageDTO.getQuantity());
        orderItem.setStockManager(Enum.valueOf(StockManagerEnum.class, StockManagerEnum.BizArk.name()));
        // 物流类型
        // 根据channel区分
//        if(ChannelTypeEnum.MultiChannel.equals(orderItem.getChannelType())){
//            orderItem.setLogisticsType(LogisticsTypeEnum.DropShipping);
//        }
//        if(ChannelTypeEnum.Erp.equals(orderItem.getChannelType())){
//            orderItem.setLogisticsType(LogisticsTypeEnum.PickUp);
//        }
//        if(ChannelTypeEnum.TikTok.equals(orderItem.getChannelType())){
//            orderItem.setLogisticsType(temuOrderDTO.getLogisticsType());
//        }
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityType(null);

        // 供货商租户编号-供货商的租户编号逻辑是跟着商品上的 租户id product_sku 表

        // 要与产品协调 估计是建立一个henjian类似的角色 拿固定的id
        // 从店铺里拿对应的tenantId
//        SysTenant sysTenant =  sysTenantService.getTenantByThirdChannelFlag(orderReceiveFromErpDTO.getThirdChannelFlag());
        orderItem.setTenantId(ecOrderMessageDTO.getTenantId());

        return orderItem;
    }


    public OrderItem setOrderTagSystemForAmazonSc(OrderItem orderItem, AmazonScOrderDTO amazonScOrderDTO, Orders orders, AmazonScOrderItemDTO amazonScOrderItemDTO) {
        ProductSku productSku = new ProductSku();
        if(StringUtils.isNotEmpty(amazonScOrderItemDTO.getProductSkuCode())){
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, amazonScOrderItemDTO.getProductSkuCode())));
        }
        orderItem.setOrderId(orders.getId());
        orderItem.setOrderNo(orders.getOrderNo());
        // 原生订单逻辑
        String orderItemNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderItemNo);
        orderItem.setOrderItemNo(orderItemNo);
        if(null != productSku){
            orderItem.setProductSkuCode(productSku.getProductSkuCode());
            orderItem.setSupplierTenantId(productSku.getTenantId());
        }
        orderItem.setTotalQuantity(amazonScOrderItemDTO.getQuantity());
        orderItem.setStockManager(Enum.valueOf(StockManagerEnum.class, StockManagerEnum.BizArk.name()));
        // 物流类型
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityType(null);
        orderItem.setTenantId(amazonScOrderDTO.getTenantId());
        return orderItem;
    }

    /**
     * 功能描述：设置订单标签系统 V3专用
     *
     * @param orderItem                订单项目
     * @param orderReceiveFromThirdDTO 从第三dto接收订单
     * @param channelTypeEnum          通道类型枚举
     * @param saleOrderItemDTO         销售订单项目dto
     * <AUTHOR>
     * @date 2024/07/03
     */
    public void setOrderTagSystem(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, ChannelTypeEnum channelTypeEnum, SaleOrderItemDTO saleOrderItemDTO) {
        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
        SaleOrderItemDTO itemDTO = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0);
        String erpSku = itemDTO.getErpSku();
        ProductSku productSku = null;
        if (ObjectUtil.isNotEmpty(erpSku)){
            if (ChannelTypeEnum.TikTok.name().equals(channelTypeEnum.name())){
                lqw.eq(ProductSku::getProductSkuCode,erpSku);
            }else {
                lqw.eq(ProductSku::getSku, erpSku);
            }
            productSku = TenantHelper.ignore(()->productSkuService.getOne(lqw));
            if(ObjectUtil.isNotEmpty(productSku)){
                orderItem.setProductSkuCode(productSku.getProductSkuCode());
            }
            orderItem.setSupplierTenantId(productSku.getTenantId());
        }
        // 原生订单逻辑
        orderItem.setTotalQuantity(itemDTO.getQuantity());
        orderItem.setStockManager(Enum.valueOf(StockManagerEnum.class, StockManagerEnum.BizArk.name()));
        // 物流类型
        // 根据channel区分
        if(ChannelTypeEnum.MultiChannel.equals(orderItem.getChannelType())){
            orderItem.setLogisticsType(LogisticsTypeEnum.DropShipping);
        }
        if(ChannelTypeEnum.Erp.equals(orderItem.getChannelType())){
            orderItem.setLogisticsType(LogisticsTypeEnum.PickUp);
        }
        if(ChannelTypeEnum.TikTok.equals(orderItem.getChannelType())){
            orderItem.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()));
        }
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityType(null);

        // 供货商租户编号-供货商的租户编号逻辑是跟着商品上的 租户id product_sku 表

        orderItem.setTenantId(orderReceiveFromThirdDTO.getTenantId());
    }
}
