package com.zsmall.order.biz.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.mapper.SysOssMapper;
import com.hengjian.system.service.ISysTenantService;
import com.hengjian.system.service.impl.SysOssServiceImpl;
import com.thoughtworks.xstream.XStream;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.AmazonVCOrderItemMessageDTO;
import com.zsmall.common.domain.dto.TemuOrderDTO;
import com.zsmall.common.domain.dto.TemuOrderItemDTO;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.common.handler.AbstractOrderOperationHandler;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.RegexUtil;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.order.biz.manager.OrderAndItemManager;
import com.zsmall.order.biz.service.DistributorOrderService;
import com.zsmall.order.biz.service.OrderItemPriceService;
import com.zsmall.order.biz.service.OrderItemService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.impl.OrderItemProductSkuThirdServiceImpl;
import com.zsmall.order.biz.service.impl.OrderItemThirdServiceImpl;
import com.zsmall.order.biz.support.OrderActivitySupport;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.biz.support.ThirdPartyLogisticsSupport;
import com.zsmall.order.biz.support.WholesaleOrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.order.OrderPayBo;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.order.entity.mapper.TemuOrderItemsMapper;
import com.zsmall.order.entity.mapper.TemuOrderMapper;
import com.zsmall.order.entity.mapper.ThirdOrderInfoItemMapper;
import com.zsmall.order.entity.mapper.ThirdOrderInfoMapper;
import com.zsmall.product.biz.service.ProductMappingService;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAdjustStockVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo;
import com.zsmall.system.entity.iservice.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年6月5日  11:43
 * @description: temu渠道订单处理
 */

@Slf4j
@Lazy
@Component("temuOrderOperationHandler")
public class TemuOrderOperationHandler extends AbstractOrderOperationHandler<String, TemuOrderDTO, Map<String, Object>, Orders, Object> {
    @Resource
    private IProductMappingService iProductMappingService;
    @Resource
    private SysOssMapper sysOssMapper;
    @Resource
    private BillSupport billSupport;
    @Resource
    private IOrderRefundService iOrderRefundService;
    @Resource
    private WholesaleOrderSupport wholesaleSupports;
    @Resource
    private ThirdPartyLogisticsSupport thirdPartyLogisticsSupport;
    @Resource
    private IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;
    @Resource
    private DistributorOrderService distributorOrderService;
    @Resource
    private IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    //    @XxlConf(value = "distribution.shipping.address.id.erp",defaultValue = "1704748687534034946")

    @Resource
    private SysOssServiceImpl sysOssService;
    @Resource
    private ITenantShippingAddressService tenantShippingAddressService;

    //    @XxlConf(value = "distribution.specify.warehouse.id.hj",defaultValue = "BG94930")
    @Value("${distribution.specify.warehouse.id.bizArk}")
    public String warehouseSystemCode;
    @Resource
    private OrderItemService orderItemService;
    @Resource
    private OrderItemPriceService orderItemPriceService;
    @Resource
    private OrderSupport orderSupport;
    @Resource
    private IProductSkuService iProductSkuService;
    @Resource
    private IProductService iProductService;
    @Resource
    private IOrdersService iOrdersService;
    @Resource
    private OrdersService ordersService;
    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private IProductChannelControlService iProductChannelControlService;
    @Autowired
    ApplicationContext applicationContext;
    @Resource
    private OrderCodeGenerator orderCodeGenerator;
    @Resource
    private IOrderItemService iOrderItemService;
    @Resource
    private OrderItemThirdServiceImpl iOrderItemThirdService;
    @Resource
    private IOrderItemProductSkuService iOrderItemProductSkuService;
    @Resource
    private IOrderLogisticsInfoService iOrderLogisticsInfoService;
    @Resource
    private IOrderAddressInfoService iOrderAddressInfoService;
    @Resource
    private OrderItemProductSkuThirdServiceImpl orderItemProductSkuThirdService;
    @Resource
    private PriceSupportV2 priceSupportV2;
    @Resource
    private TemuOrderMapper temuOrderMapper;
    @Resource
    private TemuOrderItemsMapper temuOrderItemsMapper;
    @Resource
    private ISiteCountryCurrencyService iSiteCountryCurrencyService;
    @Resource
    ThirdOrderInfoMapper thirdOrderInfoMapper;
    @Resource
    ThirdOrderInfoItemMapper thirdOrderInfoItemMapper;
    @Resource
    private IProductSkuPriceService iProductSkuPriceService;
    @Resource
    private OrderActivitySupport orderActivitySupport;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void initialMessageSave(TemuOrderDTO temuOrderDTO) {
        List<ThirdOrderInfo> thirdOrderInfoList = new ArrayList<>();
        List<ThirdOrderInfoItem> thirdOrderInfoItemList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(temuOrderDTO)){
            ThirdOrderInfo thirdOrderInfo = new ThirdOrderInfo();
            thirdOrderInfo.setAccountId(temuOrderDTO.getAccount_id()).setOrderNum(temuOrderDTO.getOrdernum()).setAmount(temuOrderDTO.getAmount()).setCurrencyCode(temuOrderDTO.getCurrency_code())
                          .setStatus(temuOrderDTO.getStatus()).setShippingStatus(temuOrderDTO.getShipping_status()).setBuyName(temuOrderDTO.getBuy_name()).setBuyEmail(temuOrderDTO.getBuy_email())
                          .setFulfillmentChannel(temuOrderDTO.getFulfillment_channel()).setShippingName(temuOrderDTO.getShipping_name()).setAddressline1(temuOrderDTO.getAddressline1()).setAddressline2(temuOrderDTO.getAddressline2())
                          .setCity(temuOrderDTO.getCity()).setStateOrRegion(temuOrderDTO.getState_or_region()).setCountry(temuOrderDTO.getCountry()).setCountryCode(temuOrderDTO.getCountry_code())
                          .setPostalCode(temuOrderDTO.getPostal_code()).setPhone(temuOrderDTO.getPhone()).setCreated(temuOrderDTO.getCreated())
                          .setLatestShipDate(temuOrderDTO.getLatest_ship_date()).setIsBusinessOrder(temuOrderDTO.getIs_business_order()).setPlatform_cancel_time(temuOrderDTO.getPlatform_cancel_time())
                          .setCreateTime(LocalDateTime.now());
            thirdOrderInfo.setTenantId(temuOrderDTO.getTenantId());
            thirdOrderInfoList.add(thirdOrderInfo);
            if(CollUtil.isNotEmpty(temuOrderDTO.getItems())){
                List<TemuOrderItemDTO> items = temuOrderDTO.getItems();
                for(TemuOrderItemDTO temuOrderItemDTO : items){
                    ThirdOrderInfoItem thirdOrderInfoItem = new ThirdOrderInfoItem();
                    thirdOrderInfoItem.setAccountId(temuOrderDTO.getAccount_id()).setOrderNum(temuOrderDTO.getOrdernum()).setCreateTime(LocalDateTime.now());
                    thirdOrderInfoItem.setSku(temuOrderItemDTO.getSku()).setAsin(temuOrderItemDTO.getAsin()).setQuantity(temuOrderItemDTO.getQuantity())
                                      .setSubtotal(temuOrderItemDTO.getSubtotal()).setCurrencyCode(temuOrderItemDTO.getCurrency_code())
                                      .setPrice(temuOrderItemDTO.getPrice()).setSales_total_amount(temuOrderItemDTO.getSales_total_amount()).setSku_name(temuOrderItemDTO.getSku_name()).setCanceled_quantity_before_shipment(temuOrderItemDTO.getCanceled_quantity_before_shipment())
                                      .setChannelOrderItemId(temuOrderItemDTO.getChannel_order_item_id());
                    thirdOrderInfoItem.setTenantId(temuOrderDTO.getTenantId());
                    thirdOrderInfoItemList.add(thirdOrderInfoItem);
                }
            }
            if(CollUtil.isNotEmpty(thirdOrderInfoList)){
                thirdOrderInfoMapper.insertBatch(thirdOrderInfoList);
            }
            if (CollUtil.isNotEmpty(thirdOrderInfoItemList)){
                thirdOrderInfoItemMapper.insertBatch(thirdOrderInfoItemList);
            }
        }
    }

    @Override
    public TemuOrderDTO parseThirdData(String json) throws Exception {
        XStream xStream = new XStream();
        xStream.alias("OrderHeader", TemuOrderDTO.class);
        xStream.alias("OrderItem", TemuOrderItemDTO.class);
        TemuOrderDTO temuOrderDTO = (TemuOrderDTO) xStream.fromXML(json);
        if (ObjectUtil.isEmpty(temuOrderDTO)) {
            throw new AppRuntimeException("订单数据不能为null");
        }
        // 保存报文信息
        try {
            initialMessageSave(temuOrderDTO);
        }catch (Exception e){
            log.error("持久化temu订单信息报错:{}",e.getMessage());
        }

        if(StringUtils.isNotEmpty(temuOrderDTO.getStatus()) && !temuOrderDTO.getStatus().equals("unshipped")){
            return null;
        }
//        if(null == temuOrderDTO.getAmount() || temuOrderDTO.getAmount().compareTo(BigDecimal.ZERO) == 0){
//            throw new AppRuntimeException("订单金额为0!");
//        }

        TenantSalesChannel tenantSalesChannelByChannelName = iTenantSalesChannelService.getTenantSalesChannelByChannelName(temuOrderDTO.getAccount_id(),ChannelTypeEnum.Temu.name());
        if(ObjectUtil.isEmpty(tenantSalesChannelByChannelName)){
            throw new AppRuntimeException("没有对应的渠道信息");
        }
        if(StringUtils.isEmpty(tenantSalesChannelByChannelName.getTenantId())){
            throw new AppRuntimeException("渠道:"+tenantSalesChannelByChannelName.getTenantId()+" 没有租户信息");
        }
        temuOrderDTO.setTenantId(tenantSalesChannelByChannelName.getTenantId());
        temuOrderDTO.setChannelType(ChannelTypeEnum.valueOf(tenantSalesChannelByChannelName.getChannelType()));
        temuOrderDTO.setLogisticsType(tenantSalesChannelByChannelName.getLogisticsType());
        temuOrderDTO.setChannelId(tenantSalesChannelByChannelName.getId());
        temuOrderDTO.setOrderSource(OrderSourceEnum.INTERFACE_ORDER.getValue());
        if(StringUtils.isEmpty(temuOrderDTO.getAddressline1())){
            return null;
        }
        if(CollUtil.isNotEmpty(temuOrderDTO.getItems()) && temuOrderDTO.getItems().size() > 1){
            temuOrderDTO.setIsMultiple(Boolean.TRUE);
        }else {
            temuOrderDTO.setIsMultiple(Boolean.FALSE);
        }
        return temuOrderDTO;
    }

    @Override
    public Boolean msgVerify(TemuOrderDTO temuOrderDTO) {
        // 渠道单号重复Set

//        Set<String> channelOrderNoSet = new HashSet<>();

        List<TemuOrderItemDTO> items = temuOrderDTO.getItems();
        // 订单物流信息
        LogisticsTypeEnum orderLogisticsType = temuOrderDTO.getLogisticsType();
        for (TemuOrderItemDTO temuOrderItemDTO : items) {
            ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(temuOrderDTO.getTenantId(), temuOrderDTO.getChannelId(), temuOrderItemDTO.getSku(), SyncStateEnum.Mapped,temuOrderDTO.getCountry_code());
            if(null != productMapping && StringUtils.isNotEmpty(productMapping.getProductSkuCode())){
                ProductSku productSku = iProductSkuService.queryByProductSkuCode(productMapping.getProductSkuCode());
                if (productSku == null) {
                    continue;
                } else {
                    // 如果商品被管控，则报商品不存在
                    String tenantId = "1";
                    boolean checkUserAllow = iProductChannelControlService.checkUserAllow(tenantId, productMapping.getProductSkuCode(), ChannelTypeEnum.Temu.name());

                    if (!checkUserAllow) {
                        // 日志记录商品不存在
                        return Boolean.FALSE;
                    }
                }
                Product product = iProductService.queryByProductSkuCode(productMapping.getProductSkuCode());
                // 产品的物流信息
                SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();
                // 物流类型校验
                if(SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)){
                    if(orderLogisticsType.equals(LogisticsTypeEnum.DropShipping)){
                        log.error("订单物流类型: {} 和产品支持的物流类型: {} 不匹配,无法生成订单",supportedLogistics,LogisticsTypeEnum.DropShipping);
                        temuOrderDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                    }
                }
                if(SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogistics)){
                    if(orderLogisticsType.equals(LogisticsTypeEnum.PickUp)){
                        log.error("订单物流类型: {} 和产品支持的物流类型: {} 不匹配,无法生成订单",supportedLogistics,LogisticsTypeEnum.PickUp);
                        temuOrderDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                    }
                }
            }
//            if (StrUtil.isNotBlank(temuOrderDTO.getOrdernum())) {
//                if (channelOrderNoSet.contains(temuOrderDTO.getOrdernum())) {
//                    // ?
//                } else {
//                    // 查询此账户所有订单判断是否有重复的，排除Canceled的
//                    boolean orderExists = iOrdersService.existsChannelOrderNo(temuOrderDTO.getOrdernum(), OrderStateType.Canceled);
//                    // 查询导入缓存表
//                    if (orderExists) {
//                        return Boolean.FALSE;
//                        // 存在同一个订单
//                    } else {
//                        channelOrderNoSet.add(temuOrderDTO.getOrdernum());
//                    }
//                }
//            }
            // 规则校验
            if (!RegexUtil.matchQuantity(temuOrderItemDTO.getQuantity().toString())) {
                // 数量异常-正则表达式
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Orders formalOrderAndItemEntry(Map<String, List> map, Orders orders) {
        List<OrderItem> orderItems = (List<OrderItem>) map.get("orderItems");
        List<OrderItemProductSku> skus = (List<OrderItemProductSku>) map.get("orderItemProductsSku");
        iOrdersService.save(orders);
        orderItems.forEach(item -> item.setOrderId(orders.getId()));
        iOrderItemService.saveBatch(orderItems);
        if(CollUtil.isNotEmpty(skus)){
            for (OrderItem item : orderItems) {
                for (OrderItemProductSku sku : skus) {
                    if (item.getOrderItemNo().equals(sku.getOrderItemNo())) {
                        sku.setOrderItemId(item.getId());
                        sku.setOrderNo(item.getOrderNo());
                    }
                }
            }
            iOrderItemProductSkuService.saveBatch(skus);
        }
        log.info("订单操作完成,进行保存操作");
        return orders;
    }

    @Override
    public void formalOrderAboutEntry(Map<String, Object> map) {
        List<OrderLogisticsInfo> logisticsInfo = (List<OrderLogisticsInfo>) map.get("logisticsInfo");
        List<OrderAddressInfo> address = (List<OrderAddressInfo>) map.get("addressInfo");
//        List<OrderItemPrice> itemPrices = (List<OrderItemPrice>) map.get("orderItemPrice");
        Orders orders = (Orders) map.get("orders");
        String tenantId = orders.getTenantId();
        iOrderLogisticsInfoService.saveBatch(logisticsInfo);
        iOrderAddressInfoService.saveBatch(address);
        String logisticsCompanyName = null;
        if(CollUtil.isNotEmpty(logisticsInfo)){
            OrderLogisticsInfo orderLogisticsInfo = logisticsInfo.get(0);
            if (ObjectUtil.isNotEmpty(orderLogisticsInfo)){
                logisticsCompanyName = orderLogisticsInfo.getLogisticsCarrierCode();
            }
        }
//      address转换为map,key为orderNo,value为zipCode
        Map<String, String> addressMap = address.stream()
                                                     .collect(Collectors.toMap(OrderAddressInfo::getOrderNo, OrderAddressInfo::getZipCode));
        //        if(CollUtil.isNotEmpty(itemPrices)){
//            iOrderItemPriceService.saveBatch(itemPrices);
//        }

        if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
            // 重新计算订单价格信息
            List<OrderItemPrice> itemPrices = new ArrayList<>();
            List<OrderItem> orderItemUpdateList = new ArrayList<>();
            List<OrderItem> listByOrderId = iOrderItemService.getListByOrderId(orders.getId());
            HashMap<String,List<String> >stashMap = orderSupport.getStashList(listByOrderId);
            String warehouseSystemCode = null;
            String warehouseCode = null;
            String logisticsCarrierCode = null;
            String logisticsCode = null;
            if(CollUtil.isNotEmpty(listByOrderId)){
                for(OrderItem orderItem : listByOrderId){
                    OrderPriceCalculateDTO orderPriceCalculateDTO = new OrderPriceCalculateDTO();
                    orderPriceCalculateDTO.setOrderItem(orderItem);
                    orderPriceCalculateDTO.setLogisticsType(orders.getLogisticsType());
                    orderPriceCalculateDTO.setActivityCode(orderItem.getActivityCode());
                    List<String> stashList = new ArrayList<>();
                    if(CollUtil.isNotEmpty(stashMap)){
                        stashList = stashMap.get(orderItem.getOrderItemNo());
                    }else {
                        stashList = null;
                    }

                    LocaleMessage calculationOrderItemPriceExceptionMessage = priceSupportV2.calculationOrderItemPrice(orderPriceCalculateDTO,tenantId,addressMap.get(orderItem.getOrderNo()),stashList, orders, OrderFlowEnum.THIRD_CREATE_ORDER, logisticsCompanyName);
                    warehouseSystemCode = orderPriceCalculateDTO.getWarehouseSystemCode();
                    warehouseCode = orderPriceCalculateDTO.getWarehouseSystemCode();
                    logisticsCarrierCode = orderPriceCalculateDTO.getLogisticsCarrierCode();
                    logisticsCode = orderPriceCalculateDTO.getLogisticsCode();
                    if(ObjectUtil.isNotEmpty(calculationOrderItemPriceExceptionMessage) && StringUtils.isNotEmpty(calculationOrderItemPriceExceptionMessage.getEn_US())){
                        orders.setPayErrorMessage(calculationOrderItemPriceExceptionMessage.toJSON());
                    }
                    OrderItemPrice orderItemPrice1 = orderPriceCalculateDTO.getOrderItemPrice();
                    OrderItemPrice orderItemPrice = new OrderItemPrice();
                    BeanUtils.copyProperties(orderItemPrice1, orderItemPrice);
                    orderItemPrice.setTotalQuantity(orderItem.getTotalQuantity()).setOrderItemId(orderItem.getId()).setOrderItemNo(orderItem.getOrderItemNo()).setProductSkuCode(orderItem.getProductSkuCode()).setLogisticsType(orderItem.getLogisticsType());
                    itemPrices.add(orderItemPrice);
                    OrderItem orderItemUpdate = new OrderItem();
                    BeanUtils.copyProperties(orderItem, orderItemUpdate);
                    orderItemUpdateList.add(orderItemUpdate);
                }
                // 重新计算主订单数据
                priceSupportV2.recalculateOrderAmount(orders,itemPrices);
            }

            // 更新订单明细信息
            orderItemPriceService.saveOrSetNUll(itemPrices,orders.getExceptionCode());

            orderItemService.updateOrSetNUll(orderItemUpdateList,orders.getExceptionCode());

            ordersService.updateOrSetNull(orders);
            // 一件代发订单物流信息更新
            if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                iOrderItemProductSkuService.updateOrSetNull(orders.getOrderNo(),warehouseSystemCode, warehouseSystemCode);
            }
            // 一件代发订单物流信息更新
            if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                iOrderLogisticsInfoService.updateOrSetNull(orders.getOrderNo(),logisticsCarrierCode,logisticsCode);
            }

        }
    }

    @Override
    public Map<String, Object> msgForLogistics(TemuOrderDTO temuOrderDTO, Orders orders,
                                               Map<String, List> itemMap) {
        HashMap<String, Object> map = new HashMap<>();
        List<OrderItem> orderItems = (List<OrderItem>) itemMap.get("orderItems");
        OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
        ArrayList<OrderAddressInfo> addressInfos = new ArrayList<>();
        ArrayList<OrderLogisticsInfo> orderLogisticsInfos = new ArrayList<>();
        // 订单物流信息
        orderLogisticsInfo.setOrderId(orders.getId());
        orderLogisticsInfo.setOrderNo(orders.getOrderNo());
        orderLogisticsInfo.setShippingLabelExist(true);
        orderLogisticsInfo.setLogisticsZipCode(temuOrderDTO.getPostal_code());
        orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orders.getLogisticsType()
                                                                                               .name()));
//        orderLogisticsInfo.setLogisticsCompanyName(details.getCarrier());
//        orderLogisticsInfo.setLogisticsServiceName(details.getCarrier());
        orderLogisticsInfo.setZipCode(temuOrderDTO.getPostal_code());
        orderLogisticsInfo.setLogisticsCountryCode(temuOrderDTO.getCountry_code());
        orderLogisticsInfos.add(orderLogisticsInfo);

        // 订单地址信息
        OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
        orderAddressInfo.setOrderId(orders.getId());
        orderAddressInfo.setOrderNo(orders.getOrderNo());
        // 拿默认模版里面
        orderAddressInfo.setRecipient(temuOrderDTO.getBuy_name());

        orderAddressInfo.setPhoneNumber(temuOrderDTO.getPhone());
        // 这三个信息需要调用包裹接口拿到详细的包裹信息
        orderAddressInfo.setCountry(temuOrderDTO.getCountry());
        orderAddressInfo.setCountryCode(temuOrderDTO.getCountry_code());
        String zipCode = temuOrderDTO.getPostal_code();
        String state = temuOrderDTO.getState_or_region();
        orderAddressInfo.setState(state);
        orderAddressInfo.setStateCode(state);

        orderAddressInfo.setCity(temuOrderDTO.getCity());
        orderAddressInfo.setAddress1(temuOrderDTO.getAddressline1());
        orderAddressInfo.setAddress2(temuOrderDTO.getAddressline2());

        orderAddressInfo.setZipCode(zipCode);
//        orderAddressInfo.setEmail(address.getBuyerEmail());
        orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);

        addressInfos.add(orderAddressInfo);

        List<OrderItemPrice> itemPrices = new ArrayList<>();
        if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
            OrderPriceCalculateDTO paramDTO = new OrderPriceCalculateDTO();
            // 订单明细价格
            for (OrderItem orderItem : orderItems) {
                paramDTO.setOrderItem(orderItem);
                paramDTO.setLogisticsType(temuOrderDTO.getLogisticsType());
                paramDTO.setCountry(temuOrderDTO.getCountry_code());
                OrderPriceCalculateDTO calculateDTO = orderSupport.calculationOrderItemPriceForTemu(paramDTO);
                OrderItemPrice itemPrice = calculateDTO.getOrderItemPrice();
                itemPrice.setOrderItemId(orderItem.getId());
                itemPrices.add(itemPrice);
            }
        }
        map.put("logisticsInfo", orderLogisticsInfos);
        map.put("addressInfo", addressInfos);
        map.put("orderItemPrice", itemPrices);
        map.put("orders", orders);
        log.info("temu订单物流信息:{}", JSONUtil.toJsonStr(orderLogisticsInfo));
        return map;
    }

    @Override
    public Map<String, List> msgForItems(TemuOrderDTO temuOrderDTO, Orders orders) {
        List<TemuOrderItemDTO> items = temuOrderDTO.getItems();
        List<OrderItem> orderItems = new ArrayList();
        List<OrderItemProductSku> orderItemProductSkus = new ArrayList<>();
        HashMap<String, List> hashMap = new HashMap<>();

        for (TemuOrderItemDTO temuOrderItemDTO : items) {
            OrderItem orderItem = new OrderItem();
            orderItem.setChannelType(orders.getChannelType());
            orderItem.setChannelSku(temuOrderItemDTO.getSku());
            orderItem.setLogisticsType(temuOrderDTO.getLogisticsType());
            orderItemService.setOrderBusinessFieldForTemu(orderItem, temuOrderDTO, orders, temuOrderItemDTO);
            orderItemService.setChannelTagForTemu(orderItem, temuOrderDTO, orders, temuOrderItemDTO);
            iOrderItemThirdService.setOrderTagSystemForTemu(orderItem, temuOrderDTO, orders, temuOrderItemDTO);
            // 设置币种
            SiteCountryCurrencyVo siteCountryCurrencyVo = iSiteCountryCurrencyService.queryByCountryCode(temuOrderDTO.getCountry_code());
            if(null != siteCountryCurrencyVo){
                orderItem.setCurrency(siteCountryCurrencyVo.getCurrencyCode());
                orderItem.setCurrencySymbol(siteCountryCurrencyVo.getCurrencySymbol());
                orderItem.setCountryCode(siteCountryCurrencyVo.getCountryCode());
                orderItem.setSiteId(siteCountryCurrencyVo.getId());
            }
            orderItems.add(orderItem);
            // 获取商品相关的活动信息
            DistributorProductActivity distributorProductActivity = orderActivitySupport.getDistributorActivityByStock(temuOrderDTO.getTenantId(), temuOrderItemDTO.getProductSkuCode(), temuOrderDTO.getCountry(),orderActivitySupport.logisticsTypeConvert(orders.getLogisticsType()));
            // 产品没有映射不生成 orderItemProductsSku 数据
            if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
                OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
                // 通过 自订单编号进行关联,
                orderItemProductSkuThirdService.setBusinessFieldForTemu(orderItemProductSku, orderItem, temuOrderDTO, orders, temuOrderItemDTO);
                // 活动订单设置为活动仓库
                if (null != distributorProductActivity) {
                    DistributorProductActivityStock activityWarehouseInfo = orderActivitySupport.getActivityWarehouseInfo(distributorProductActivity);
                    if(null != activityWarehouseInfo && null != activityWarehouseInfo.getWarehouseSystemCode()){
                        orderItemProductSku.setSpecifyWarehouse(activityWarehouseInfo.getWarehouseSystemCode());
                        orderItemProductSku.setWarehouseSystemCode(activityWarehouseInfo.getWarehouseSystemCode());
                    }

                }
                orderItemProductSkus.add(orderItemProductSku);
            }
            // 判断活动订单
            if (null != distributorProductActivity) {
                orderItem.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                orderItem.setActivityType(distributorProductActivity.getActivityType());
                orders.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                orders.setActivityType(distributorProductActivity.getActivityType());
            }
        }
        hashMap.put("orderItems", orderItems);
        hashMap.put("orderItemProductsSku", orderItemProductSkus);
        log.info("订单项信息:{}", JSONUtil.toJsonStr(orderItems));
        return hashMap;
    }

    @Override
    public void getShippingLabels(List<Map<String, List>> orderData) {

    }

    @Override
    public Boolean isNeedPay() {
        return Boolean.FALSE;
    }

    @Override
    public String attachmentsFlow(TemuOrderDTO temuOrderDTO,Orders orders) {
        LogisticsTypeEnum logisticsType = orders.getLogisticsType();
// 目前全是代发
//        if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
//            SaleOrderDetailDTO saleOrderDetails = orderReceiveFromThirdDTO.getSaleOrderDetails();
//            List<AttachInfo> attachInfoItems = saleOrderDetails.getAttachInfoItems();
//            for (AttachInfo attachInfoItem : attachInfoItems) {
//                SysOssVo sysOssVo = sysOssService.downloadPdfNotAsync(attachInfoItem.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());
//                OrderUpdateBo bo = new OrderUpdateBo(orders.getOrderNo(), null, true, sysOssVo);
//                // 实际上传完了以后已经有了文件了 不需要再走上传的逻辑了
//                distributorOrderService.uploadShippingLabel(bo);
//            }
//
//        }

        return orders.getOrderNo();
    }

    @Override
    public Boolean isNeedPay(TemuOrderDTO temuOrderDTO) {
        Boolean isPay;
        SiteCountryCurrencyVo siteCountryCurrencyVo = iSiteCountryCurrencyService.queryByCountryCode(temuOrderDTO.getCountry_code());
        if(null!= siteCountryCurrencyVo && null != siteCountryCurrencyVo.getCurrencyCode()){
            isPay = ZSMallSystemEventUtils.checkWalletAutoPaymentEvent(temuOrderDTO.getTenantId(),siteCountryCurrencyVo.getCurrencyCode());
        }else {
            isPay = Boolean.FALSE;
        }
        if(isPay){
            // 异常订单不进行支付
            if(null != temuOrderDTO.getExceptionCode() && !temuOrderDTO.getExceptionCode().equals(OrderExceptionEnum.normal.getValue())){
                isPay = false;
            }
        }
        return isPay;
    }

    @Override
    public Boolean payOrder(TemuOrderDTO temuOrderDTO) throws Exception {
        // 是否开启自动支付
        if (true) {
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, temuOrderDTO.getOrdernum());

            List<String> orderNos = TenantHelper.ignore(() -> iOrdersService.list(lqw)).stream().map(Orders::getOrderNo)
                                                .collect(Collectors.toList());
            OrderPayBo bo = new OrderPayBo();
            bo.addOrderNoList(orderNos);
            bo.setPaymentPassword(null);
            if(CollUtil.isEmpty(orderNos)){
                return false;
            }
            try {
                TenantHelper.ignore(() -> {
                    try {
                        return ordersService.payOrderForDistribution(bo, temuOrderDTO.getTenantId(), true, true);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        throw new RuntimeException(e.getMessage());
                    }
                });
            } catch (Exception e) {
                log.error("支付失败:{}", e.getMessage());
            }

        }

        return true;
    }
    @Override
    public Boolean payOrderForAsync(TemuOrderDTO temuOrderDTO, Boolean isAsync) throws Exception {
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, temuOrderDTO.getOrdernum());

            List<String> orderNos = TenantHelper.ignore(() -> iOrdersService.list(lqw)).stream().map(Orders::getOrderNo)
                                                .collect(Collectors.toList());
            OrderPayBo bo = new OrderPayBo();
            bo.addOrderNoList(orderNos);
            bo.setPaymentPassword(null);
            if(CollUtil.isEmpty(orderNos)){
                return false;
            }
            try {
                TenantHelper.ignore(() -> {
                    try {
//                        SaleOrderDetailDTO saleOrderDetails = orderReceiveFromThirdDTO.getSaleOrderDetails();
//                        Integer isNeedDelivery = saleOrderDetails.getIsNeedDelivery();
                        return ordersService.payOrderForErp(bo, temuOrderDTO.getTenantId(), true, true);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        throw new RuntimeException(e.getMessage());
                    }
                });
            } catch (Exception e) {
                log.error("支付失败:{}", e.getMessage());
            }
        return true;
    }

    @Override
    public Orders thirdToDistribution(TemuOrderDTO temuOrderDTO, Orders orders) throws ParseException {
        Orders orders1 = new Orders();
        String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
        orders1.setOrderNo(orderNo);
        orders1.setOrderExtendId(orderNo);
        if(null != temuOrderDTO.getExceptionCode()){
            orders1.setExceptionCode(temuOrderDTO.getExceptionCode());
        }
        if(null != temuOrderDTO.getPayErrorMessage()){
            orders1.setPayErrorMessage(temuOrderDTO.getPayErrorMessage());
        }
        // 业务属性
        orders1 = ordersService.setOrderBusinessFieldForTemu(temuOrderDTO, orders1);
        orders1 = ordersService.setOrderTagSystemByTemu(temuOrderDTO, orders1);
        orders1 = ordersService.setChannelTagForTemu(temuOrderDTO, orders1);
        // 币种
        SiteCountryCurrencyVo siteCountryCurrencyVo = iSiteCountryCurrencyService.queryByCountryCode(temuOrderDTO.getCountry_code());
        if(null != siteCountryCurrencyVo){
            orders1.setCurrency(siteCountryCurrencyVo.getCurrencyCode());
            orders1.setCurrencySymbol(siteCountryCurrencyVo.getCurrencySymbol());
            orders1.setCountryCode(siteCountryCurrencyVo.getCountryCode());
            orders1.setSiteId(siteCountryCurrencyVo.getId());
        }
        return orders1;
    }

    @Override
    public List<TemuOrderDTO> ordersDisassemble(TemuOrderDTO temuOrderDTO) {
        List<TemuOrderItemDTO> temuOrderItemDTOList = temuOrderDTO.getItems();
        List<TemuOrderDTO> temuOrderDTOS = new ArrayList<>();
        // 把订单项拆开
        for (TemuOrderItemDTO itemDTO : temuOrderItemDTOList) {
            // 需要通过itemOrderId进行搜索
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, temuOrderDTO.getOrdernum())
                                                                             .eq(Orders::getLineOrderItemId,itemDTO.getChannel_order_item_id())
                                                                             .eq(Orders::getDelFlag, 0).last("limit 1");
            Orders order = iOrdersService.getOne(lqw);
            if (ObjectUtil.isNotEmpty(order)) {
                continue;
//                throw new AppRuntimeException("订单已经录入" + temuOrderDTO.getOrdernum());
            }
            List<TemuOrderItemDTO> temuOrderItemDTOList1 = new ArrayList<>();
            TemuOrderDTO temuOrderDTO1 = new TemuOrderDTO();
            ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(temuOrderDTO.getTenantId(), temuOrderDTO.getChannelId(), itemDTO.getSku(), SyncStateEnum.Mapped,temuOrderDTO.getCountry_code());
            TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getTenantSalesChannelByChannelName(temuOrderDTO.getAccount_id(),ChannelTypeEnum.Temu.name());
            if(ObjectUtil.isEmpty(tenantSalesChannel)){
                throw new AppRuntimeException("没有对应的渠道信息");
            }
            if(null != productMapping && StringUtils.isNotEmpty(productMapping.getProductSkuCode())){
                itemDTO.setProductSkuCode(productMapping.getProductSkuCode());
                // 判断渠道和产品的发货方式是否一致
                Product product = iProductService.queryByProductSkuCode(productMapping.getProductSkuCode());
                //校验渠道店铺发货方式和商品发货方式是否不一致
                if (ObjectUtil.isNotNull(product) && ObjectUtil.isNotNull(tenantSalesChannel) ){
                    boolean isSupport = product.getSupportedLogistics()
                                               .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
                    if (!isSupport){
                        temuOrderDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                    }
                }
                // 判断库存是否足够
                ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productMapping.getProductSkuCode());
                Integer stockTotal = adjustStockVo.getStockTotal();
                if (NumberUtil.compare(itemDTO.getQuantity(), stockTotal) > 0) {
                    // todo 测算放开
                    temuOrderDTO.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                }
            }


//            ProductSku one = productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getSku, itemDTO.getSku()));
//            List<ProductMapping> listProductMappingByChannelSkuAndSyncState = iProductMappingService.getListProductMappingByChannelSkuAndSyncState(temuOrderDTO.getTenantId(),temuOrderDTO.getChannelId(),itemDTO.getSku(), SyncStateEnum.Mapped);
//            if(CollUtil.isNotEmpty(listProductMappingByChannelSkuAndSyncState)){
                // 这里直接拿product_sku_code 来比较好
//                itemDTO.setSku(one.getSku());
//            }
            temuOrderItemDTOList1.add(itemDTO);
            BeanUtils.copyProperties(temuOrderDTO, temuOrderDTO1);
            // 判断产品是否映射
            if (ObjectUtil.isEmpty(productMapping)) {
                // 判断商品价格是否存在
                List<ProductMapping> productMappingList = iProductMappingService.getProductMappingByChannelSkuAndSyncState(temuOrderDTO.getTenantId(), temuOrderDTO.getChannelId(), itemDTO.getSku(), SyncStateEnum.Mapped);
                String productSkuCode = null;
                if(CollUtil.isNotEmpty(productMappingList)){
                    productSkuCode = productMappingList.get(0).getProductSkuCode();
                }
                ProductSkuPrice productSkuPrice;
                if(StringUtils.isNotEmpty(productSkuCode)){
                    productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSkuCode, temuOrderDTO.getCountry());
                    if(null == productSkuPrice){
                        LocaleMessage localeMessage = LocaleMessage.byStatusCode(OrderStatusCodeEnum.SKU_REGION_PRICE_NOT_MAINTAINED.args(productSkuCode));
                        temuOrderDTO1.setPayErrorMessage(localeMessage.toJSON());
                        temuOrderDTO.setPayErrorMessage(localeMessage.toJSON());
                    }
                }
                temuOrderDTO1.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                temuOrderDTO.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
            }
            temuOrderDTO1.pushTemuOrderItemDTOList(temuOrderItemDTOList1);
            temuOrderDTO1.setChannel_order_item_id(itemDTO.getChannel_order_item_id());
            temuOrderDTOS.add(temuOrderDTO1);
        }
        return temuOrderDTOS;
    }

    @Override
    public R tripartiteUpdate(Object o) {
        return null;
    }

    @Override
    public Boolean tripartiteDeliverGoods(Object o) {
        return null;
    }

    @Override
    public Boolean tripartiteReceiptGoods(Object o) {
        return null;
    }

    @Override
    public R<Void> test() {
        return null;
    }

    @Override
    public void orderOperationHandler(TemuOrderDTO i, ConcurrentHashMap<String, List<Object>> businessMap,
                                      ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                      ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void priceOperation(ConcurrentHashMap<String, List<Object>> businessMap,
                               ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                               ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void orderOperationHandlerSave(ChannelTypeEnum channelTypeEnum,
                                          ConcurrentHashMap<String, List<Object>> businessMap) {

    }

    @Override
    public List<TemuOrderDTO> ordersDisassembleForList(List<TemuOrderDTO> temuOrderDTOS,
                                                       BusinessTypeMappingEnum mappingEnum) {
        return null;
    }

    @Override
    public List<TemuOrderDTO> parseThirdDataForList(String s) {
        return null;
    }
}
