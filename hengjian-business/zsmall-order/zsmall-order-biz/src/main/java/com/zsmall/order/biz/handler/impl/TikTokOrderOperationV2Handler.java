package com.zsmall.order.biz.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.SysOss;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.mapper.SysOssMapper;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.common.domain.dto.SaleOrderDetailDTO;
import com.zsmall.common.domain.dto.SaleOrderItemDTO;
import com.zsmall.common.domain.dto.ThirdReceiptDTO;
import com.zsmall.common.domain.tiktok.domain.dto.address.SiteMsgBo;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokDistrictInfo;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.common.TikTokVersionEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseChannelCodeEnum;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.common.handler.AbstractOrderOperationHandler;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.RegexUtil;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.lottery.support.PriceBussinessV2Support;
import com.zsmall.order.biz.factory.OrderOperationFactory;
import com.zsmall.order.biz.manager.OrderAndItemManager;
import com.zsmall.order.biz.service.OrderItemService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.impl.OrderItemProductSkuThirdServiceImpl;
import com.zsmall.order.biz.service.impl.OrderItemThirdServiceImpl;
import com.zsmall.order.biz.support.OrderActivitySupport;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.biz.support.ThirdPartyLogisticsSupport;
import com.zsmall.order.biz.support.WholesaleOrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.order.OrderPayBo;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.domain.vo.order.OrderItemVo;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.biz.service.ProductSkuStockService;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.domain.ConfZip;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.iservice.*;
import com.zsmall.system.entity.util.AddressAnalysisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zsmall.common.enums.order.SignalSenderEnum.orderAddress;
import static com.zsmall.common.enums.order.SignalSenderEnum.orderItem;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/4 19:14
 */
@Slf4j
@Lazy
@Component("tiktokOperationV2Handler")
public class TikTokOrderOperationV2Handler extends AbstractOrderOperationHandler<String, OrderReceiveFromThirdDTO, Map<String, Object>, Orders, Object> {

    @Resource
    private ISiteCountryCurrencyService iSiteCountryCurrencyService;

    @Resource
    private AddressAnalysisUtil addressAnalysisUtil;
    @Resource
    private PriceBussinessV2Support priceBussinessV2Support;
    @Resource
    private IProductMappingService iProductMappingService;
    @Resource
    private SysOssMapper sysOssMapper;
    @Resource
    private BillSupport billSupport;
    @Resource
    private IOrderRefundService iOrderRefundService;
    @Resource
    private WholesaleOrderSupport wholesaleSupports;
    @Resource
    private ThirdPartyLogisticsSupport thirdPartyLogisticsSupport;
    @Resource
    private IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;

    @Resource
    private OrderOperationFactory factory;

    @Resource
    private IProductSkuService iProductSkuService;

    @Resource
    private ProductSkuStockService productSkuStockService;

    @Resource
    private IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    //    @XxlConf(value = "distribution.shipping.address.id.erp",defaultValue = "1704748687534034946")
    @Value("${distribution.shipping.address.id.erp}")
    public String addressId;

    @Resource
    private ITenantShippingAddressService tenantShippingAddressService;

    //    @XxlConf(value = "distribution.specify.warehouse.id.hj",defaultValue = "BG94930")
    @Value("${distribution.specify.warehouse.id.bizArk}")
    public String warehouseSystemCode;
    @Resource
    private IOrderItemShippingRecordService iOrderItemShippingRecordService;
    @Resource
    private OrderItemService orderItemService;

    @Resource
    private OrderSupport orderSupport;

    @Resource
    private IProductSkuStockService iProductSkuStockService;

    @Resource
    private ISysTenantService sysTenantService;

    @Resource
    private IProductService iProductService;

    @Resource
    private OrderAndItemManager orderAndItemManager;

    @Resource
    private IOrdersService iOrdersService;

    @Resource
    private OrdersService ordersService;

    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private IProductChannelControlService iProductChannelControlService;

    @Resource
    private IConfZipService iConfZipService;

    @Resource
    private BusinessParameterService businessParameterService;

    @Autowired
    ApplicationContext applicationContext;

    @Resource
    private IWorldLocationService iWorldLocationService;

    @Resource
    private OrderCodeGenerator orderCodeGenerator;


    @Resource
    private IProductSkuAttachmentService iProductSkuAttachmentService;


    @Resource
    private IOrderItemService iOrderItemService;
    @Resource
    private OrderItemThirdServiceImpl iOrderItemThirdService;


    @Resource
    private IOrderItemProductSkuService iOrderItemProductSkuService;
    @Resource
    private IOrderLogisticsInfoService iOrderLogisticsInfoService;
    @Resource
    private IOrderAddressInfoService iOrderAddressInfoService;
    @Resource
    private IOrderItemPriceService iOrderItemPriceService;


    @Resource
    private IProductSkuService productSkuService;

    @Resource
    private OrderItemProductSkuThirdServiceImpl orderItemProductSkuThirdService;

    @Resource
    private OrderActivitySupport orderActivitySupport;


    @Override
    public void initialMessageSave(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {

    }

    @Override
    public OrderReceiveFromThirdDTO parseThirdData(String json) {
        OrderReceiveFromThirdDTO erpDTO = JSONObject.parseObject(json, OrderReceiveFromThirdDTO.class);
        if (ObjectUtil.isEmpty(erpDTO)) {
            throw new AppRuntimeException("订单数据不能为null");
        }
        return erpDTO;
    }

    @Override
    public Boolean msgVerify(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {

        // 渠道单号重复Set
        orderReceiveFromThirdDTO.getSaleOrderItemsList();

        Set<String> channelOrderNoSet = new HashSet<>();

        List<SaleOrderItemDTO> orderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();

        for (SaleOrderItemDTO itemDTO : orderItemsList) {
            ProductSku productSku = iProductSkuService.queryByProductSkuCodeAndWarehouseSystemCode(itemDTO.getErpSku(), WarehouseChannelCodeEnum.TIKTOK.getWarehouseSystemCode());
            // 仓库号,可以通过渠道写死 提前配置好仓库

            if (productSku == null) {

                continue;
            } else {
                // 如果商品被管控，则报商品不存在
                String tenantId = "1";
                boolean checkUserAllow = iProductChannelControlService.checkUserAllow(tenantId, itemDTO.getErpSku(), ChannelTypeEnum.Others.name());

                if (!checkUserAllow) {
                    // 日志记录商品不存在
                    return Boolean.FALSE;
                }
            }

            Product product = iProductService.queryByProductSkuCode(itemDTO.getErpSku());
            if (StrUtil.isNotBlank(orderReceiveFromThirdDTO.getOrderNo())) {
                if (channelOrderNoSet.contains(orderReceiveFromThirdDTO.getOrderNo())) {
                    // ?
                } else {
                    // 查询此账户所有订单判断是否有重复的，排除Canceled的
                    boolean orderExists = iOrdersService.existsChannelOrderNo(orderReceiveFromThirdDTO.getOrderNo(), OrderStateType.Canceled);
                    // 查询导入缓存表
                    if (orderExists) {
                        return Boolean.FALSE;
                        // 存在同一个订单
                    } else {
                        channelOrderNoSet.add(orderReceiveFromThirdDTO.getOrderNo());
                    }
                }
            }
            // 规则校验
            if (!RegexUtil.matchQuantity(itemDTO.getQuantity().toString())) {
                // 数量异常-正则表达式
                return Boolean.FALSE;
            }


            SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

            // 三方默认代发
            String logisticsType = "DropShipping";
            if (StrUtil.equals(logisticsType, "DropShipping")) {
                // 仅支持自提
                if (SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)) {
                    // 商品仅支持自提
                    log.info("订单与商品物流类型冲突,订单号:{},商品sku:{},商品仅支持自提", orderReceiveFromThirdDTO.getOrderNo(), itemDTO.getErpSku());
                    return Boolean.FALSE;
                }
            }

        }
        return Boolean.TRUE;
    }


    /**
     * 功能描述：录入正式订单数据相关
     *
     * @param map 地图
     * <AUTHOR>
     * @date 2024/01/09
     */
    @Override
    public void formalOrderAboutEntry(Map<String, Object> map) {
        List<OrderLogisticsInfo> logisticsInfo = (List<OrderLogisticsInfo>) map.get("logisticsInfo");
        List<OrderAddressInfo> address = (List<OrderAddressInfo>) map.get("addressInfo");
        List<OrderItemPrice> itemPrices = (List<OrderItemPrice>) map.get("orderItemPrice");
        iOrderLogisticsInfoService.saveBatch(logisticsInfo);
        iOrderAddressInfoService.saveBatch(address);
        iOrderItemPriceService.saveBatch(itemPrices);
    }

    @Override
    public Orders formalOrderAndItemEntry(Map<String, List> map, Orders orders) {
        List<OrderItem> orderItems = (List<OrderItem>) map.get("orderItems");
        List<OrderItemProductSku> skus = (List<OrderItemProductSku>) map.get("orderItemProductsSku");
        iOrdersService.save(orders);
        orderItems.forEach(item -> item.setOrderId(orders.getId()));
        iOrderItemService.saveBatch(orderItems);
        for (OrderItem item : orderItems) {
            for (OrderItemProductSku sku : skus) {
                if (item.getOrderItemNo().equals(sku.getOrderItemNo())) {
                    sku.setOrderItemId(item.getId());
                    sku.setOrderNo(item.getOrderNo());
                }
            }
        }
        log.info("订单操作完成,进行保存操作");
        iOrderItemProductSkuService.saveBatch(skus);
        return orders;
    }

    /**
     * 处理订单价格 , 会员价逻辑在计算之前还是计算之后
     *
     * @param orders
     */
    public void dealOrderPrice(Orders orders, List<OrderItem> orderItems, List<OrderItemPrice> itemPrices) {
        //   需付款/实付金额：total   销售额金额：totalAmount  应付金额：productAmount
        //   操作费：operationFee 运费：shippingCost 尾程派送费：finalDeliveryFee 定金： totalDeposit
        SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(orders.getTenantId());
        if (ObjectUtil.isNull(sysTenantVo)) {
            throw new RuntimeException("当前租户id不存在" + orders.getTenantId());
        }
        String tenantType = sysTenantVo.getTenantType();
        //获取价格
        Map<String, com.zsmall.order.entity.domain.OrderItemPrice> orderItemPriceMap = itemPrices.stream()
                                                                                                 .collect(Collectors.toMap(OrderItemPrice::getOrderItemNo, s -> s));
        ArrayList<com.zsmall.order.entity.domain.vo.order.OrderItemVo> orderItemVoArrayList = new ArrayList<>();
        //封装明细
        orderItems.forEach(orderItem -> {
            OrderItemPrice orderItemPrice = orderItemPriceMap.get(orderItem.getOrderItemNo());
            if (ObjectUtil.isNotEmpty(orderItemPrice)) {
                //商品总价（供应商）
                BigDecimal originalPayableTotalAmount = orderItem.getOriginalPayableTotalAmount();
                //已预付总价（供应商）
                BigDecimal originalPrepaidTotalAmount = orderItem.getOriginalPrepaidTotalAmount();
                //实际总价（供应商）
                BigDecimal originalActualTotalAmount = orderItem.getOriginalActualTotalAmount();
                //已预付总价（平台）
                BigDecimal platformPrepaidTotalAmount = orderItem.getPlatformPrepaidTotalAmount();
                //实际总价（平台）
                BigDecimal platformActualTotalAmount = orderItem.getPlatformActualTotalAmount();

                com.zsmall.order.entity.domain.vo.order.OrderItemVo orderItemBody = new com.zsmall.order.entity.domain.vo.order.OrderItemVo();
                orderItemBody.setOrderItemNo(String.valueOf(orderItem.getOrderNo()));
                orderItemBody.setOrderId(String.valueOf(orders.getId()));
                int totalQuantity = orderItem.getChannelType()
                                             .equals(ChannelTypeEnum.TikTok) && orderItem.getTotalQuantity() == 0 ? 1 : orderItem.getTotalQuantity();
                // 计算
                if (Objects.equals(tenantType, TenantType.Supplier.name())) {
                    // 单价
                    orderItemBody.setProductTotalPrice(originalPayableTotalAmount);
                    orderItemBody.setProductUnitPrice(NumberUtil.mul(orderItemPrice.getOriginalUnitPrice(), totalQuantity));
                    orderItemBody.setOperationFee(NumberUtil.mul(orderItemPrice.getOriginalOperationFee(), totalQuantity));
                    orderItemBody.setFinalDeliveryFee(NumberUtil.mul(orderItemPrice.getOriginalFinalDeliveryFee(), totalQuantity));
                    orderItemBody.setTotalPrice(originalActualTotalAmount);
                    orderItemBody.setDepositTotalPrice(originalPrepaidTotalAmount);
                } else {
                    orderItemBody.setUnitPrice(orderItem.getOriginalPayableUnitPrice());
                    orderItemBody.setProductTotalPrice(originalPayableTotalAmount);
                    BigDecimal operationFees = NumberUtil.mul(orderItemPrice.getOriginalOperationFee(), totalQuantity);
                    orderItemBody.setOperationFee(operationFees);
                    BigDecimal finalDeliveryFees = NumberUtil.mul(orderItemPrice.getOriginalFinalDeliveryFee(), totalQuantity);
                    orderItemBody.setFinalDeliveryFee(finalDeliveryFees);
                    BigDecimal unitPrices = NumberUtil.mul(orderItemPrice.getOriginalUnitPrice(), totalQuantity);
                    orderItemBody.setProductUnitPrice(unitPrices);
                    orderItemBody.setTotalPrice(platformActualTotalAmount);
                    orderItemBody.setDepositTotalPrice(platformPrepaidTotalAmount);
                }
                orderItemVoArrayList.add(orderItemBody);
            }
        });
        //处理订单
        Map<String, OrderItemVo> orderItemVoMap = orderItemVoArrayList.stream()
                                                                      .collect(Collectors.toMap(OrderItemVo::getOrderItemNo, item -> item));
        OrderItemVo orderItemVo = orderItemVoMap.get(orders.getOrderNo());
        //   需付款/实付金额：total   销售额金额：totalAmount  应付金额：productAmount
        //   操作费：operationFee 运费：shippingCost 尾程派送费：finalDeliveryFee 定金： totalDeposit
        BigDecimal finalDeliveryFee=BigDecimal.ZERO;
        if (LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())) {
            finalDeliveryFee = NumberUtil.add(finalDeliveryFee, orderItemVo.getFinalDeliveryFee());
        }
        BigDecimal productUnitPrice = ChannelTypeEnum.TikTok.equals(orders.getChannelType()) ?
            (LogisticsTypeEnum.PickUp
                                     .equals(orders.getLogisticsType()) ? orders.getOriginalTotalPickUpPrice() : orders.getOriginalTotalDropShippingPrice()) :
            NumberUtil.add(BigDecimal.ZERO, orderItemVo.getProductUnitPrice());
        if (ObjectUtil.isNotEmpty(orderItemVo)) {
            if (Objects.equals(tenantType, TenantType.Supplier.name())) {
                //销售额金额：
                orders.setOriginalTotalProductAmount(orderItemVo.getProductTotalPrice());
                //原始应付总金额（供货商）
                orders.setOriginalPayableTotalAmount(productUnitPrice);
                //原始操作费总金额（供货商）
                orders.setOriginalTotalOperationFee(orderItemVo.getOperationFee());
                //原始尾程派送费总额
                orders.setOriginalTotalFinalDeliveryFee(finalDeliveryFee);
                //实付金额：
                orders.setOriginalActualTotalAmount(orderItemVo.getTotalPrice());
            } else {
                //实付金额：
                orders.setPlatformActualTotalAmount(orders.getPlatformActualTotalAmount());
                //应付金额
                orders.setPlatformPayableTotalAmount(productUnitPrice);
                //销售额金额：
                orders.setPlatformTotalProductAmount(orders.getPlatformPayableTotalAmount());
                //原始操作费总金额（供货商）
                orders.setPlatformTotalOperationFee(orderItemVo.getOperationFee());
                //原始尾程派送费总额
                orders.setPlatformTotalFinalDeliveryFee(finalDeliveryFee);
            }
        }
        iOrdersService.updateById(orders);
    }

    @Override
    public Map<String, Object> msgForLogistics(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders,
                                               Map<String, List> itemMap) {
        HashMap<String, Object> map = new HashMap<>();
        List<OrderItem> orderItems = (List<OrderItem>) itemMap.get("orderItems");
        List<OrderItem> dealPriceOrderitems= BeanUtil.copyToList(orderItems,OrderItem.class);
        OrderItem item = orderItems.get(0);
        String productSkuCode = item.getProductSkuCode();
        OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
        ArrayList<OrderAddressInfo> addressInfos = new ArrayList<>();
        ArrayList<OrderLogisticsInfo> orderLogisticsInfos = new ArrayList<>();
        SaleOrderDetailDTO details = orderReceiveFromThirdDTO.getSaleOrderDetails();
        String logisticsType = orderReceiveFromThirdDTO.getLogisticsType();
//        List<String> result = Arrays.asList(addressDTO.getPostalCode().split("-"));
        List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        SaleOrderItemDTO dto = saleOrderItemsList.get(0);

        TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();
        String regionCode = address.getRegionCode();
        String finalZipCode = null;
        // 虽然此处遍历,但外层已处理过基本上是一个sku
        for (SaleOrderItemDTO itemDTO : saleOrderItemsList) {
            // 拿默认地址模版
            List<TikTokDistrictInfo> districtInfo = address.getDistrictInfo();
            String country = null;
            String state = null;
            String federalDistrict = null;
            String county = null;
            String city = null;

            for (TikTokDistrictInfo tikTokDistrictInfo : districtInfo) {

                String levelName = tikTokDistrictInfo.getAddressLevelName();
                String addressName = tikTokDistrictInfo.getAddressName();
                if ("country".equalsIgnoreCase(levelName)) {
                    country = addressName;
                }
                if ("state".equalsIgnoreCase(levelName)) {
                    state = addressName;
                }
                if ("Federal District".equalsIgnoreCase(levelName)) {
                    federalDistrict = addressName;
                }
                if ("county".equalsIgnoreCase(levelName)) {
                    county = addressName;
                }
                if ("city".equalsIgnoreCase(levelName)) {
                    city = addressName;
                }
            }
//            WorldLocation worldLocation = iWorldLocationService.getOne(new LambdaQueryWrapper<WorldLocation>()(WorldLocation::getLocationOtherName, country));


            ConfZip confZip;
            confZip = iConfZipService.getStateCodeByStateName(state);
            if(ObjectUtil.isEmpty(confZip)&&ObjectUtil.isNotEmpty(federalDistrict)){
                confZip = iConfZipService.getStateCodeByStateName(federalDistrict);
            }
            if (ObjectUtil.isEmpty(confZip)) {
                confZip = iConfZipService.getStateCodeByCity(city);
            }

            OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
            orderAddressInfo.setCounty(county);
            orderAddressInfo.setOrderId(orders.getId());
            orderAddressInfo.setOrderNo(orders.getOrderNo());
            orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
            // 拿默认模版里面
            orderAddressInfo.setRecipient(address.getName());

            orderAddressInfo.setPhoneNumber(address.getPhoneNumber());
            // 这三个信息需要调用包裹接口拿到详细的包裹信息
            orderAddressInfo.setCountry(country);
            orderAddressInfo.setCountryCode(regionCode);
            String zipCode = null;
            if (ObjectUtil.isNotEmpty(state)) {
                zipCode = iConfZipService.transZip(address.getPostalCode(), state, county, city);
                orderAddressInfo.setState(state);
            }
            if (ObjectUtil.isNotEmpty(federalDistrict)) {
                zipCode = iConfZipService.transZip(address.getPostalCode(), federalDistrict, county, city);
                orderAddressInfo.setState(federalDistrict);
            }
            finalZipCode = zipCode;
            orderAddressInfo.setStateCode(confZip.getStateCode());

            orderAddressInfo.setCity(city);
            orderAddressInfo.setAddress1(address.getAddressLine1());
            orderAddressInfo.setAddress2(address.getAddressLine2());

            orderAddressInfo.setZipCode(zipCode);
            orderAddressInfo.setEmail(address.getBuyerEmail());
            orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);

            addressInfos.add(orderAddressInfo);

            orderLogisticsInfo.setOrderId(orders.getId());
            orderLogisticsInfo.setOrderNo(orders.getOrderNo());
            orderLogisticsInfo.setShippingLabelExist(true);
            orderLogisticsInfo.setLogisticsZipCode(zipCode);
            orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()));
            orderLogisticsInfo.setLogisticsCompanyName(details.getCarrier());
            orderLogisticsInfo.setLogisticsCountryCode(address.getRegionCode());
            orderLogisticsInfo.setLogisticsServiceName(details.getCarrier());
            orderLogisticsInfo.setZipCode(zipCode);
            orderLogisticsInfos.add(orderLogisticsInfo);
        }

        // 物流信息

        // erp 统一为自提
//        orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromErpDTO.getLogisticsType()));

//        orderLogisticsInfo.setLogisticsAccount(logisticsAccount);
//        orderLogisticsInfo.setLogisticsAccountZipCode(logisticsAccountZipCode);


        List<OrderItemTrackingRecord> trackingList = new ArrayList<>();
        String trimTracking = StrUtil.trim(details.getLogisticsTrackingNo());

        if (StrUtil.isNotBlank(trimTracking)) {
            OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();
            trackingRecord.setSku(dto.getErpSku());
            trackingRecord.setProductSkuCode(productSkuCode);
            trackingRecord.setLogisticsCarrier(details.getCarrier());
            trackingRecord.setLogisticsTrackingNo(trimTracking);
            trackingRecord.setOrderNo(orders.getOrderNo());
            trackingRecord.setOrderItemNo(item.getOrderItemNo());
            trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
            // 用的bizArk仓库ID 此处轮询处理  item.getTotalQuantity()
            // 通过product_sku_code查询仓库product_sku_stock logistics_warehouse_relation warehouse 并且可用库存要大于实际需要扣减的库存
            // tag lty 库存判断
            ProductSkuStock stock = productSkuStockService.getStockForDeliver(regionCode,productSkuCode,LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()),item.getTotalQuantity(),Boolean.FALSE, finalZipCode,details.getCarrier(), orders.getTenantId(), item.getSupplierTenantId());
            if(ObjectUtil.isNotEmpty(stock)){
                trackingRecord.setWarehouseSystemCode(stock.getWarehouseSystemCode());
            }else{
                trackingRecord.setWarehouseSystemCode(warehouseSystemCode);
            }

            // tag lty 仓库
            trackingRecord.setQuantity(item.getTotalQuantity());

            trackingList.add(trackingRecord);
        }else{
            OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();

            trackingRecord.setSku(dto.getErpSku());
            trackingRecord.setProductSkuCode(productSkuCode);
            trackingRecord.setLogisticsCarrier(details.getCarrier());
            trackingRecord.setLogisticsTrackingNo(trimTracking);
            trackingRecord.setOrderNo(orders.getOrderNo());
            trackingRecord.setOrderItemNo(item.getOrderItemNo());
            if(ChannelTypeEnum.TikTok.equals(orders.getChannelType())&&LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
                SysOss sysOss = sysOssMapper.selectOne(new LambdaQueryWrapper<SysOss>().eq(SysOss::getBusinessId, orderReceiveFromThirdDTO.getLineOrderItemId()));
                trackingRecord.setLogisticsTrackingNo(sysOss.getBusinessNumber());
            }

            trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
            // 仓库改造后选用模版配置后的最近距离的仓库或随机有库存的仓库
            ProductSkuStock stock = productSkuStockService.getStockForDeliver(regionCode,productSkuCode,LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()),item.getTotalQuantity(),Boolean.FALSE, finalZipCode, details.getCarrier(),orders.getTenantId() ,item.getSupplierTenantId() );
            // 如果找不到对应的合适的仓库,则使用默认的仓库
            if(ObjectUtil.isNotEmpty(stock)){
                trackingRecord.setWarehouseSystemCode(stock.getWarehouseSystemCode());
            }else{
                trackingRecord.setWarehouseSystemCode(warehouseSystemCode);
            }

            trackingRecord.setQuantity(item.getTotalQuantity());
            trackingList.add(trackingRecord);
//            String key = "order:tracking:" + orderReceiveFromThirdDTO.getLineOrderItemId();
//            RedissonClient client = RedisUtils.getClient();
//            client.getBucket(key).set(trackingRecord,20, TimeUnit.MINUTES);
        }
        // 如果是补偿的订单,是有trackingNumber的


        iOrderItemTrackingRecordService.saveBatch(trackingList);


        List<OrderItemPrice> itemPrices = new ArrayList<>();
        OrderPriceCalculateDTO paramDTO = new OrderPriceCalculateDTO();
        // 物流跟踪单

        for (OrderItem orderItem : orderItems) {
            paramDTO.setOrderItem(orderItem);
            paramDTO.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()));
            paramDTO.setSiteId(orders.getSiteId());
            OrderPriceCalculateDTO calculateDTO = orderSupport.calculationOrderItemPriceForThird(paramDTO);
            OrderItemPrice itemPrice = calculateDTO.getOrderItemPrice();
            itemPrice.setOrderItemId(orderItem.getId());
            itemPrices.add(itemPrice);
        }
        //处理订单价格 逻辑变更
        dealOrderPrice(orders,dealPriceOrderitems,itemPrices);
        map.put("logisticsInfo", orderLogisticsInfos);
        map.put("addressInfo", addressInfos);
        map.put("orderItemPrice", itemPrices);
        log.info("物流信息:{}", JSONUtil.toJsonStr(orderLogisticsInfo));
        return map;
    }


    @Override
    public Map<String, List> msgForItems(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        List<OrderItem> orderItems = new ArrayList();
        List<OrderItemProductSku> orderItemProductSkus = new ArrayList<>();
        HashMap<String, List> hashMap = new HashMap<>();

        for (SaleOrderItemDTO saleOrderItemDTO : saleOrderItemsList) {
            OrderItem orderItem = new OrderItem();
            orderItem.setChannelType(orders.getChannelType());
            orderItemService.setOrderBusinessField(orderItem, orderReceiveFromThirdDTO, orders, saleOrderItemDTO);
            orderItemService.setChannelTag(orderItem, orderReceiveFromThirdDTO, orders, saleOrderItemDTO);
            // 获取商品相关的活动信息
            DistributorProductActivity distributorProductActivity = orderActivitySupport.getDistributorActivityByStock(orders.getTenantId(), saleOrderItemDTO.getItemNo(), orders.getCountryCode(),orderActivitySupport.logisticsTypeConvert(orders.getLogisticsType()));
            // 判断活动订单
            if (null != distributorProductActivity) {
                orderItem.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                orderItem.setActivityType(distributorProductActivity.getActivityType());
                orders.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                orders.setActivityType(distributorProductActivity.getActivityType());
            }
            iOrderItemThirdService.setOrderTagSystem(orderItem, orderReceiveFromThirdDTO, orders, saleOrderItemDTO);
            orderItems.add(orderItem);
            OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
            // 通过 自订单编号进行关联,
            orderItemProductSkuThirdService.setBusinessField(orderItemProductSku, orderItem, orderReceiveFromThirdDTO, orders, saleOrderItemDTO);
            // 活动订单设置为活动仓库
            if (null != distributorProductActivity) {
                DistributorProductActivityStock activityWarehouseInfo = orderActivitySupport.getActivityWarehouseInfo(distributorProductActivity);
                if(null != activityWarehouseInfo && null != activityWarehouseInfo.getWarehouseSystemCode()){
                    orderItemProductSku.setSpecifyWarehouse(activityWarehouseInfo.getWarehouseSystemCode());
                    orderItemProductSku.setWarehouseSystemCode(activityWarehouseInfo.getWarehouseSystemCode());
                    orderItemProductSku.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                    orderItemProductSku.setActivityType(distributorProductActivity.getActivityType());
                }

            }
            orderItemProductSkus.add(orderItemProductSku);
        }
        hashMap.put("orderItems", orderItems);
        hashMap.put("orderItemProductsSku", orderItemProductSkus);
        log.info("订单项信息:{}", JSONUtil.toJsonStr(orderItems));
        return hashMap;
    }

    @Override
    public void getShippingLabels(List<Map<String, List>> orderData) {

    }

    @Override
    public Boolean isNeedPay() {

        return Boolean.FALSE;
    }

    @Override
    public String attachmentsFlow(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        return null;
    }

    @Override
    public Boolean isNeedPay(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {
        return ZSMallSystemEventUtils.checkAutoPaymentEvent(orderReceiveFromThirdDTO.getTenantId());
    }

    @Override
    public Boolean payOrder(OrderReceiveFromThirdDTO erpDTO) throws RuntimeException {
        Boolean aBoolean = ZSMallSystemEventUtils.checkWalletAutoPaymentEvent(erpDTO.getTenantId(), erpDTO.getCurrencyCode());
        if (aBoolean) {
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, erpDTO.getOrderNo());
            List<String> orderNos = TenantHelper.ignore(() -> iOrdersService.list(lqw)).stream().map(Orders::getOrderNo)
                                                .collect(Collectors.toList());
            OrderPayBo bo = new OrderPayBo();
            bo.addOrderNoList(orderNos);
            bo.setPaymentPassword(null);
            if(CollUtil.isEmpty(orderNos)){
                return false;
            }
            TenantHelper.ignore(() -> {
                try {
                    return ordersService.payOrderForDistribution(bo, erpDTO.getTenantId(), true, true);
                } catch (Exception e) {
                    throw new RuntimeException(e.getMessage());
                }
            });
        }
        return true;

    }

    @Override
    public Boolean payOrderForAsync(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Boolean isAsync) throws Exception {
        return null;
    }

    @Override
    public Orders thirdToDistribution(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
//        Orders orders = new Orders();
        String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
        orders.setOrderNo(orderNo);
        orders.setOrderExtendId(orderNo);
        // 业务属性
        orders = ordersService.setSiteField(orderReceiveFromThirdDTO, orders);
        orders = ordersService.setOrderBusinessField(orderReceiveFromThirdDTO, orders);
        orders = ordersService.setOrderTagSystem(orderReceiveFromThirdDTO, orders);
        orders = ordersService.setChannelTag(orderReceiveFromThirdDTO, orders);

        return orders;
    }

    /**
     * 功能描述：订单拆解
     *
     * @param orderReceiveFromThirdDTO 从tiktok DTO接收订单
     * @return {@link List }<{@link OrderReceiveFromThirdDTO }>
     * <AUTHOR>
     * @date 2024/01/11
     */
    @Override
    public List<OrderReceiveFromThirdDTO> ordersDisassemble(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {
        List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        List<OrderReceiveFromThirdDTO> tikDTOS = new ArrayList<>();
        // 需要通过itemOrderId进行搜索
        LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getLineOrderItemId, orderReceiveFromThirdDTO.getLineOrderItemId())
                                                                         .eq(Orders::getDelFlag, 0).last("limit 1");

        Orders order = iOrdersService.getOne(lqw);
        if (ObjectUtil.isNotEmpty(order)) {
            throw new AppRuntimeException("订单已经录入" + orderReceiveFromThirdDTO.getOrderNo());
        }
        // 把订单项拆开
        for (SaleOrderItemDTO itemDTO : saleOrderItemsList) {
            List<SaleOrderItemDTO> saleOrderItemDTOS = new ArrayList<>();
            OrderReceiveFromThirdDTO erpDTO = new OrderReceiveFromThirdDTO();
            ProductMapping mapping = iProductMappingService.getOne(new LambdaQueryWrapper<ProductMapping>().eq(ProductMapping::getChannelSku, itemDTO.getErpSku())
                                                                                                           .eq(ProductMapping::getChannelType, ChannelTypeEnum.TikTok.name())
                                                                                                           .eq(ProductMapping::getTenantId, orderReceiveFromThirdDTO.getTenantId())
                                                                                                           .eq(ProductMapping::getDelFlag,0)
                                                                                                           .last("limit 1"));


            if (ObjectUtil.isEmpty(mapping) || ObjectUtil.isEmpty(mapping.getProductSkuCode())) {

                Pattern pattern = Pattern.compile("\\D");
                Matcher matcher = pattern.matcher(itemDTO.getErpSku());
                boolean result = matcher.find();
                if(result){
                    log.error("\n产品尚未更新映射,请先同步映射,channelSku:{},店铺:{},租户标识:{}", itemDTO.getErpSku(),orderReceiveFromThirdDTO.getThirdChannelFlag(),orderReceiveFromThirdDTO.getTenantId());
                }else{
                    log.error("\n产品未建立映射,channelSku:{},店铺:{},租户标识:{}", itemDTO.getErpSku(),orderReceiveFromThirdDTO.getThirdChannelFlag(),orderReceiveFromThirdDTO.getTenantId());
                }
                throw new AppRuntimeException("产品尚未更新映射,请先同步映射");
            }
            if (ObjectUtil.isNotEmpty(mapping) && ObjectUtil.isNotEmpty(mapping.getProductSkuCode())){
                String productSkuCode = mapping.getProductSkuCode();
                ProductSku one = iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, productSkuCode)
                                                                                               .eq(ProductSku::getDelFlag, 0));
                if (ObjectUtil.isEmpty(one)) {
                    throw new AppRuntimeException("产品映射异常,产品被删除:"+productSkuCode);
                }
            }
            // 这里直接拿product_sku_code 来比较好
            itemDTO.setErpSku(mapping.getMappingSku());
            saleOrderItemDTOS.add(itemDTO);
            BeanUtils.copyProperties(orderReceiveFromThirdDTO, erpDTO);
            erpDTO.pushSaleOrderItemsList(saleOrderItemDTOS);

            erpDTO.setLineOrderItemId(orderReceiveFromThirdDTO.getLineOrderItemId());
            tikDTOS.add(erpDTO);
        }


        return tikDTOS;
    }

    /**
     * 功能描述：三方更新
     *
     * @param o o
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/01/12
     */
    @Override
    public R tripartiteUpdate(Object o) {

        return R.ok();
    }

    // tripartiteDeliverGoods   tripartiteReceiptGoods
    @Override
    public Boolean tripartiteReceiptGoods(Object obj) {
        ThirdReceiptDTO bo = (ThirdReceiptDTO) obj;


        LambdaQueryWrapper<Orders> eq = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, bo.getOrderNo());
        List<Orders> ordersList = iOrdersService.list(eq);
        if (CollUtil.isEmpty(ordersList)) {
            throw new AppRuntimeException("渠道订单号不存在:" + bo.getOrderNo());
        }
        for (Orders orders : ordersList) {
            List<OrderItem> orderItems = iOrderItemService.getListByOrderId(orders.getId());
            List<String> orderItemNoList = orderItems.stream().map(OrderItem::getOrderItemNo)
                                                     .collect(Collectors.toList());

            if (CollUtil.isEmpty(orderItemNoList)) {
                throw new AppRuntimeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY.getMessage());
            }

            Set<Long> orderIds = new HashSet<>();
            Set<Long> orderItemIds = new HashSet<>();
            List<OrderItem> saveOrderItems = new ArrayList<>();
            //获取已发货、已支付的子订单
            List<OrderItem> orderItemList = iOrderItemService.getOrderItemListByShipped(orderItemNoList);
            if (CollUtil.isNotEmpty(orderItemList)) {
                for (OrderItem orderItem : orderItemList) {
                    Long orderId = orderItem.getOrderId();
                    Long orderItemId = orderItem.getId();
                    orderItem.setFulfillmentProgress(LogisticsProgress.Fulfilled);
                    orderItem.setFulfillmentTime(new Date());
                    orderIds.add(orderId);
                    orderItemIds.add(orderItemId);
                    saveOrderItems.add(orderItem);
                }
                log.info("手动确认收货的子订单id为 : {}", JSONUtil.toJsonStr(orderItemIds));
                // 更新子订单的物流状态
                iOrderItemService.saveOrUpdateBatch(saveOrderItems);
                // 批量更新主订单的物流状态
                setOrderFulfillment(orderIds);

                // 记账
                billSupport.generateBillDTOByOrderItem(saveOrderItems, null);
            } else {
                throw new AppRuntimeException(ZSMallStatusCodeEnum.ORDER_CANNOT_UPDATE_NOT_BELONG_TO_ME_ERROR.getMessage());
            }
        }
        return true;
    }

    /**
     * 批量更新主订单的物流状态
     *
     * @param orderIds
     */
    public void setOrderFulfillment(Set<Long> orderIds) {
        log.info("setOrderFulfillment orderIds = {}", orderIds);
        List<Orders> orders = iOrdersService.queryListByIds(orderIds);
        if (CollectionUtils.isNotEmpty(orders)) {
            for (Orders order : orders) {
                String orderNo = order.getOrderNo();
                // 处理复合状态
                Set<LogisticsProgress> logisticsProgresses = iOrderItemService.queryFulfillmentTypesByOrderId(order.getId());
                log.info("setOrderFulfillment - orderNo = {}, logisticsProgresses = {}", orderNo, logisticsProgresses);
                LogisticsProgress originFulfillment = order.getFulfillmentProgress();
                order.setFulfillmentProgress(LogisticsProgress.getComplexType(logisticsProgresses));

                // 现货订单若在此完成，需要计入账单（NotEqual是为了判断主订单是否是从其他状态变成Fulfilled的） TODO
                if (ObjectUtil.equals(order.getOrderType(), OrderType.Wholesale)
                    && ObjectUtil.equals(order.getFulfillmentProgress(), LogisticsProgress.Fulfilled)
                    && ObjectUtil.notEqual(order.getFulfillmentProgress(), originFulfillment)) {
                    // wholesaleSupport.wholesaleOrderAddToBill(order);  TODO 待完善
                }
            }
            iOrdersService.saveOrUpdateBatch(orders);
        }
    }

    @Override
    public Boolean tripartiteDeliverGoods(Object obj) {
        // 模拟租户

        ThirdReceiptDTO bo = (ThirdReceiptDTO) obj;
        String channelOrderNo = bo.getOrderNo();
        LambdaQueryWrapper<Orders> eq = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, channelOrderNo);
        List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.list(eq));
        if (CollUtil.isEmpty(ordersList)) {
            log.error("订单不存在:{}", channelOrderNo);
            throw new AppRuntimeException(ZSMallStatusCodeEnum.ORDER_NOT_EXIST.getMessage());

        }
        Orders o = ordersList.get(0);
        SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(o.getTenantId());
        TenantType tenantType = TenantType.Supplier;
        // channelOrderNo:order ---> one:more so the trackingNO & carrier for orders is the same
        OrderItemTrackingRecord orderItemTrackingRecord = iOrderItemTrackingRecordService.getOne(new LambdaQueryWrapper<OrderItemTrackingRecord>().eq(OrderItemTrackingRecord::getOrderNo, o.getOrderNo())
                                                                                                                                                  .last("limit 1"));
        String trackingNo = null;
        String carrier = null;
        if (ObjectUtil.isNotNull(orderItemTrackingRecord)) {
            trackingNo = orderItemTrackingRecord.getLogisticsTrackingNo();
            carrier = orderItemTrackingRecord.getLogisticsCarrier();
        }

        LocaleMessage localeMessage = new LocaleMessage();
        for (Orders order : ordersList) {
            OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderId(order.getId());
            OrderType orderType = order.getOrderType();
            List<OrderItem> orderItems = TenantHelper.ignore(() -> iOrderItemService.getListByOrderId(order.getId()));
            ChannelTypeEnum channelType = order.getChannelType();
            // 存在进行中的退款单，禁止发货
            Integer inProgress = iOrderRefundService.countByInProgress(order.getId());
            OrderItem orderItem = orderItems.get(0);
            OrderStateType orderState = orderItem.getOrderState();
            LogisticsTypeEnum logisticsType = orderItem.getLogisticsType();
            String serviceName = null;
            if (logisticsInfo != null) {
                serviceName = logisticsInfo.getLogisticsServiceName();
            }
            if (Objects.equals(orderState, OrderStateType.Verifying)) {
                log.error("订单退款中，无法履约.渠道订单号:{},订单号:{}", channelOrderNo, o.getOrderNo());
                throw new AppRuntimeException(ZSMallStatusCodeEnum.ORDER_REFUNDING_CANT_FULFILL.getMessage());

            }


            if (inProgress > 0) {
                log.error("订单退款中，无法履约.渠道订单号:{},订单号:{}", channelOrderNo, o.getOrderNo());

            }

            if (ObjectUtil.equals(orderState, OrderStateType.Paid) && ObjectUtil.equals(order.getOrderState(), OrderStateType.Paid)) {
                LogisticsProgress fulfillment = orderItem.getFulfillmentProgress();
                OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
                String sku = orderItemProductSku.getSku();
                String productSkuCode = orderItemProductSku.getProductSkuCode();
                String warehouseSystemCode = orderItemProductSku.getWarehouseSystemCode();

                // 未发货，或管理员账号已发货，才能继续往下走，管理员账号可以操作已发货的子订单重复发货
                if ((ObjectUtil.equals(fulfillment, LogisticsProgress.UnDispatched)
                    && ObjectUtil.equals(tenantType, TenantType.Supplier))
                    || (ObjectUtil.equals(fulfillment, LogisticsProgress.Dispatched) && ObjectUtil.equals(tenantType, TenantType.Manager))) {

                    //创建新的跟踪单信息 遍历order
                    List<OrderItemTrackingRecord> trackingRecordList = new ArrayList<>();

                    // 防止误填多余空格
                    if (StrUtil.contains(trackingNo, " ")) {
                        trackingNo = StrUtil.replace(trackingNo, " ", "");
                    }
                    OrderItemTrackingRecord record = new OrderItemTrackingRecord();
                    record.setOrderNo(order.getOrderNo());
                    record.setOrderItemNo(orderItem.getOrderItemNo());
                    record.setSku(sku);
                    record.setProductSkuCode(productSkuCode);
                    record.setQuantity(orderItem.getTotalQuantity());
                    record.setDispatchedTime(new Date());
                    record.setLogisticsCarrier(carrier);
                    record.setLogisticsService(serviceName);
                    record.setLogisticsTrackingNo(trackingNo);
                    record.setWarehouseSystemCode(warehouseSystemCode);
                    record.setSystemManaged(true);
                    thirdPartyLogisticsSupport.queryLogistics(record);
                    trackingRecordList.add(record);


                    // 子订单强制变为已发货，只有管理员才能再次修改
                    orderItem.setFulfillmentProgress(LogisticsProgress.Dispatched);
                    // 批发、批量自提和代发的订单，发货后直接完结
                    if (ObjectUtil.equals(orderType, OrderType.Wholesale) && (
                        ObjectUtil.equals(logisticsType, LogisticsTypeEnum.PickUp) || ObjectUtil
                            .equals(logisticsType, LogisticsTypeEnum.DropShipping))) {
                        orderItem.setFulfillmentProgress(LogisticsProgress.Fulfilled);
                        orderItem.setFulfillmentTime(new Date());
                    }
                    if (orderItem.getDispatchedTime() == null) {
                        orderItem.setDispatchedTime(new Date());
                        updateRefundExecutableAmount(orderItem);
                    }

                    // 删除所有已存在的跟踪单
                    iOrderItemTrackingRecordService.trackingListDisassociate(orderItem.getOrderItemNo());
                    // 保存新的跟踪单
                    iOrderItemTrackingRecordService.saveBatch(trackingRecordList);
                    // 更新子订单数据
                    iOrderItemService.updateNoTenant(orderItem);
                    // 更新发货单状态
                    iOrderItemShippingRecordService.updateShippingStateByOrderItem(orderItem.getOrderItemNo(), ShippingStateEnum.Shipped);
                    // 查询主订单所有子订单的物流状态，主订单需要设置履约复合状态
                    List<OrderItem> orderItemList = iOrderItemService.getListByOrderIdNotTenant(orderItem.getOrderId());
                    Set<LogisticsProgress> fulfillmentProgresses = orderItemList.stream()
                                                                                .filter(item -> OrderStateType.Paid.equals(item.getOrderState()))
                                                                                .map(OrderItem::getFulfillmentProgress)
                                                                                .collect(Collectors.toSet());
                    order.setFulfillmentProgress(LogisticsProgress.getComplexType(fulfillmentProgresses));
                    iOrdersService.updateNoTenant(order);

                    channelTrackingSync(channelType, orderItem, order);
                    iOrderItemService.updateNoTenant(orderItem);

                    //现货订单若在此完成，需要计入账单
                    if (ObjectUtil.equals(orderType, OrderType.Wholesale) &&
                        ObjectUtil.equals(order.getFulfillmentProgress(), LogisticsProgress.Fulfilled)) {
                        // 此接口下已屏蔽租户
                        wholesaleSupports.wholesaleOrderAddToBill(order);
                    }

                } else {
                    log.error("当前无法操作发货.渠道订单号:{},订单号:{}", channelOrderNo, o.getOrderNo());
                    //ZSMallStatusCodeEnum.ORDER_ALREADY_DISPATCHED.getMessage()
                    throw new AppRuntimeException(ZSMallStatusCodeEnum.ORDER_ALREADY_DISPATCHED.getMessage());

                }
            } else {
                log.error("订单状态不为[已支付]，无法确认发货.渠道订单号:{},订单号:{}", channelOrderNo, o.getOrderNo());
                throw new AppRuntimeException("当前渠道订单,在分销存在未支付的子订单:" + o.getOrderNo());

            }

        }
        return true;
    }

    @Override
    public R<Void> test() {


        return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
    }

    @Override
    public void orderOperationHandler(OrderReceiveFromThirdDTO i, ConcurrentHashMap<String, List<Object>> businessMap,
                                      ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                      ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {
        factory.getInvokeStrategy(TikTokVersionEnum.VERSION_3.getVersionHandler()).orderOperationHandler(i, businessMap, businessNos, channelTypeEnum, mappingEnum);

    }

    @Override
    public void priceOperation(ConcurrentHashMap<String, List<Object>> businessMap,
                               ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                               ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {
        log.info("订单价格计算开始,{}",mappingEnum);
        if(BusinessTypeMappingEnum.HAVE_MAPPING.equals(mappingEnum)){
            log.info("订单价格计算开始,订单相关map:{}", JSON.toJSONString(businessMap));
            List<Orders> orders = businessMap.get(SignalSenderEnum.order.name()).stream().map(obj -> (Orders) obj).collect(Collectors.toList());

            List<OrderItem> items = businessMap.get(orderItem.name()).stream().map(obj -> (OrderItem) obj)
                                               .collect(Collectors.toList());

            List<OrderAddressInfo> addressInfos = businessMap.get(orderAddress.name()).stream().map(obj -> (OrderAddressInfo) obj).collect(Collectors.toList());

            List<OrderItemPrice> itemPrices = businessMap.get(SignalSenderEnum.orderItemPrice.name()).stream().map(obj -> (OrderItemPrice) obj)
                                                         .collect(Collectors.toList());
            List<OrderItemProductSku> orderItemProductSkus = businessMap.get(SignalSenderEnum.orderItemProductSku.name()).stream().map(obj -> (OrderItemProductSku) obj)
                                                                        .collect(Collectors.toList());

            List<OrderLogisticsInfo> logisticsInfos = businessMap.get(SignalSenderEnum.orderLogisticsInfo.name()).stream().map(obj -> (OrderLogisticsInfo) obj).collect(Collectors.toList());

            List<OrderItemTrackingRecord> trackingRecords = businessMap.get(SignalSenderEnum.orderItemTrackingRecord.name()).stream().map(obj -> (OrderItemTrackingRecord) obj).collect(Collectors.toList());

            businessMap.remove(SignalSenderEnum.order.name());
            businessMap.remove(SignalSenderEnum.orderItem.name());
            businessMap.remove(SignalSenderEnum.orderItemPrice.name());
            businessMap.remove(SignalSenderEnum.orderItemProductSku.name());
            businessMap.remove(SignalSenderEnum.orderLogisticsInfo.name());
            businessMap.remove(SignalSenderEnum.orderItemTrackingRecord.name());

            Map<String, DistributorProductActivity> activityHashMap = new HashMap<>();
            Map<String, DistributorProductActivity> activityOrderNoHashMap = new HashMap<>();
            // 活动订单判断
            for(OrderItem orderItemActivity : items){
                // 获取商品相关的活动信息
                DistributorProductActivity distributorProductActivity = orderActivitySupport.getDistributorActivityByStock(orderItemActivity.getTenantId(), orderItemActivity.getProductSkuCode(), orderItemActivity.getCountryCode(),orderActivitySupport.logisticsTypeConvert(orderItemActivity.getLogisticsType()));
                // 判断活动订单
                if (null != distributorProductActivity) {
                    activityOrderNoHashMap.put(orderItemActivity.getOrderNo(),distributorProductActivity);
                    activityHashMap.put(orderItemActivity.getOrderItemNo(),distributorProductActivity);
                    orderItemActivity.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                    orderItemActivity.setActivityType(distributorProductActivity.getActivityType());
                }
            }
            for(OrderItemProductSku orderItemProductSkuActivity : orderItemProductSkus){
                if(CollUtil.isNotEmpty(activityHashMap)){
                    DistributorProductActivity distributorProductActivity = activityHashMap.get(orderItemProductSkuActivity.getOrderItemNo());
                    DistributorProductActivityStock activityWarehouseInfo = orderActivitySupport.getActivityWarehouseInfo(distributorProductActivity);
                    if(null != activityWarehouseInfo && null != activityWarehouseInfo.getWarehouseSystemCode()){
                        orderItemProductSkuActivity.setSpecifyWarehouse(activityWarehouseInfo.getWarehouseSystemCode());
                        orderItemProductSkuActivity.setWarehouseSystemCode(activityWarehouseInfo.getWarehouseSystemCode());
                        orderItemProductSkuActivity.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                        orderItemProductSkuActivity.setActivityType(distributorProductActivity.getActivityType());
                    }
                }
            }
            // 订单主数据活动赋值
            for(Orders order : orders){
                if(CollUtil.isNotEmpty(activityOrderNoHashMap)){
                    DistributorProductActivity distributorProductActivity = activityOrderNoHashMap.get(order.getOrderNo());
                    if(null != distributorProductActivity){
                        order.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                        order.setActivityType(distributorProductActivity.getActivityType());
                    }
                }
            }

            // 移除 businessMap 中 key为SignalSenderEnum.order.name() SignalSenderEnum.orderItem.name() SignalSenderEnum.orderItemPrice.name()的value
            Map<String, OrderItem> itemsMap = items.stream()
                                                   .collect(Collectors.toMap(OrderItem::getOrderNo, Function.identity(), (existing, replacement) -> existing));


            Map<String, OrderAddressInfo> addressInfosMap = addressInfos.stream()
                                                                        .collect(Collectors.toMap(OrderAddressInfo::getOrderNo, Function.identity(), (existing, replacement) -> existing));
            Map<String, OrderItemPrice> itemPricesMap = itemPrices.stream()
                                                                  .collect(Collectors.toMap(OrderItemPrice::getOrderItemNo, Function.identity(), (existing, replacement) -> existing));

            Map<String, OrderItemProductSku> itemProductSkuMap = orderItemProductSkus.stream()
                                                                                     .collect(Collectors.toMap(OrderItemProductSku::getOrderNo, Function.identity(), (existing, replacement) -> existing));
            Map<String, OrderLogisticsInfo> logisticsInfoMap = logisticsInfos.stream()
                                                                             .collect(Collectors.toMap(OrderLogisticsInfo::getOrderNo, Function.identity(), (existing, replacement) -> existing));

            Map<String, OrderItemTrackingRecord> recordMap = trackingRecords.stream()
                                                                            .collect(Collectors.toMap(OrderItemTrackingRecord::getOrderNo, Function.identity(), (existing, replacement) -> existing));
            // 1. 部分订单是需要更新的 2.部分订单是无需修改的
            for (Orders order : orders) {
                // item 判断是否有变化
                OrderItem item = itemsMap.get(order.getOrderNo());
                OrderItemProductSku orderItemProductSku = itemProductSkuMap.get(order.getOrderNo());
                OrderLogisticsInfo orderLogisticsInfo = logisticsInfoMap.get(order.getOrderNo());
                OrderItemTrackingRecord trackingRecord = recordMap.get(order.getOrderNo());
                OrderPriceCalculateDTO calculateDTO = new OrderPriceCalculateDTO();
                calculateDTO.setOrderItem(item);
                calculateDTO.setLogisticsType(order.getLogisticsType());
                calculateDTO.setActivityCode(item.getActivityCode());
                HashMap<String, List<String>> stashMap = orderSupport.getStashList(Collections.singletonList(item));

                String zipCode = addressInfosMap.get(order.getOrderNo()).getZipCode();

                List<String> stashList = new ArrayList<>();
                if(ObjectUtil.isNotEmpty(stashMap)){
                    // 自提也可以查,不会影响自提的价格逻辑,但是可能会影响订单的状态,
                    stashList = stashMap.get(item.getOrderItemNo());
                }
                // 根据 业务类型是否有映射
                priceBussinessV2Support.getTiktokBusinessMap(calculateDTO,order.getTenantId(),zipCode,stashList,item,order,businessMap, orderItemProductSku, orderLogisticsInfo, trackingRecord);
            }
        }
        log.info("订单价格计算结束,{}",JSON.toJSONString(businessMap));
    }

    @Override
    public void orderOperationHandlerSave(ChannelTypeEnum channelTypeEnum,ConcurrentHashMap<String, List<Object>> businessMap) {
        factory.getInvokeStrategy(TikTokVersionEnum.VERSION_3.getVersionHandler()).businessBatchSave(businessMap,channelTypeEnum);
    }

    /**
     * 功能描述：订单细化/移除不符合业务的订单或子订单
     *
     * @param orderReceiveFromThirdDTOS 从第三dto接收订单
     * @param mappingEnum               映射枚举
     * @return {@link List }<{@link OrderReceiveFromThirdDTO }>
     * <AUTHOR>
     * @date 2024/07/02
     */
    @Override
    public List<OrderReceiveFromThirdDTO> ordersDisassembleForList(List<OrderReceiveFromThirdDTO> orderReceiveFromThirdDTOS, BusinessTypeMappingEnum mappingEnum) {
        ArrayList<OrderReceiveFromThirdDTO> dtos = new ArrayList<>();
        for (OrderReceiveFromThirdDTO orderReceiveFromThirdDTO : orderReceiveFromThirdDTOS) {
            // 站点
            TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();
            Long channelId = orderReceiveFromThirdDTO.getShopId();
            String regionCode = address.getRegionCode();
            SiteCountryCurrency site = iSiteCountryCurrencyService.getSiteByCountryCode(regionCode);

            SiteMsgBo siteMsgBo = new SiteMsgBo();
            BeanUtils.copyProperties(site, siteMsgBo);
            siteMsgBo.setSiteId(site.getId());
            address.setSiteBo(siteMsgBo);
            List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
            SaleOrderItemDTO itemDTO = saleOrderItemsList.get(0);

            // 需要通过itemOrderId进行搜索
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getLineOrderItemId, orderReceiveFromThirdDTO.getLineOrderItemId())
                                                                             .eq(Orders::getDelFlag, 0).last("limit 1");
            // 已经录入
            Orders order = TenantHelper.ignore(()->iOrdersService.getOne(lqw));
            if (ObjectUtil.isNotEmpty(order)) {
                log.info("订单:{},子订单:{},已经录入,正常跳出,租户id{}", orderReceiveFromThirdDTO.getOrderNo(), orderReceiveFromThirdDTO.getLineOrderItemId(),orderReceiveFromThirdDTO.getTenantId());
                continue;
            }
            ProductMapping mapping = TenantHelper.ignore(()->iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountryV2(orderReceiveFromThirdDTO.getTenantId(), channelId, itemDTO.getSkuId(), SyncStateEnum.Mapped, site.getCountryCode()));
            List<SaleOrderItemDTO> saleOrderItemDTOS = new ArrayList<>();

            if(BusinessTypeMappingEnum.HAVE_MAPPING.equals(mappingEnum)){
                // 把订单项erpSku不存在的跳过
                if(ObjectUtil.isEmpty(mapping) || ObjectUtil.isEmpty(mapping.getProductSkuCode())){
                    log.info("HAVE_MAPPING-商品已映射流程,但商品未映射跳过的单号(正常跳出):{},租户id{}", orderReceiveFromThirdDTO.getOrderNo(),orderReceiveFromThirdDTO.getTenantId());
                    continue;
                }
                ProductSku one = TenantHelper.ignore(()->iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, mapping.getProductSkuCode())
                                                                                                                       .eq(ProductSku::getDelFlag, 0)));
                if (ObjectUtil.isEmpty(one)) {
                    log.error("HAVE_MAPPING-产品映射异常,产品被删除:{},租户id{},联系客户重新映射商品", mapping.getMappingSku(),orderReceiveFromThirdDTO.getTenantId());
                    log.error("HAVE_MAPPING-因产品删除问题跳过的订单号:{},租户id{}", orderReceiveFromThirdDTO.getOrderNo(),orderReceiveFromThirdDTO.getTenantId());
                    continue;
                }
                itemDTO.setErpSku(mapping.getProductSkuCode());

            }
            // 如果是未映射的流程,需要将已经做过映射的订单排除;1.存在映射但是映射的产品skuCode不为空的 2.不存在映射
            if(BusinessTypeMappingEnum.NO_MAPPING.equals(mappingEnum)){
                // 把订单项erpSku存在的跳过
                if (ObjectUtil.isNotEmpty(mapping)) {
                    if(ObjectUtil.isNotEmpty(mapping.getProductSkuCode())){
                        log.error("NO_MAPPING-产品映射已存在,走HAVE_MAPPING流程,NO_MAPPING跳过的订单号:{},租户id{}", orderReceiveFromThirdDTO.getOrderNo(),orderReceiveFromThirdDTO.getTenantId());
                        continue;
                    }
                }
                itemDTO.setErpSku(null);
            }

            saleOrderItemDTOS.add(itemDTO);
            orderReceiveFromThirdDTO.pushSaleOrderItemsList(saleOrderItemDTOS);
            dtos.add(orderReceiveFromThirdDTO);
        }

        return dtos;
    }

    @Override
    public List<OrderReceiveFromThirdDTO> parseThirdDataForList(String json) {
        //json 解析为List<OrderReceiveFromThirdDTO>
        List<OrderReceiveFromThirdDTO> erpDTOS = JSONObject.parseArray(json, OrderReceiveFromThirdDTO.class);
        if (CollUtil.isEmpty(erpDTOS)) {
            throw new AppRuntimeException("订单数据不能为null");
        }
        return erpDTOS;
    }

    private void channelTrackingSync(ChannelTypeEnum channelType, OrderItem orderItem, Orders order) {
        // Others和OneLink不需要回传物流跟踪信息
        if (ObjectUtil.notEqual(channelType, ChannelTypeEnum.Others) || ObjectUtil
            .notEqual(channelType, ChannelTypeEnum.OneLink)) {
            // 同步物流信息至第三方渠道
            ThirdChannelFulfillmentRecord thirdChannelFulfillmentRecord = new ThirdChannelFulfillmentRecord();

            thirdChannelFulfillmentRecord.setChannelId(orderItem.getChannelId());
            thirdChannelFulfillmentRecord.setChannelType(channelType);
            thirdChannelFulfillmentRecord.setOrderNo(order.getOrderNo());
            thirdChannelFulfillmentRecord.setOrderItemNo(orderItem.getOrderItemNo());
            thirdChannelFulfillmentRecord.setChannelItemNo(orderItem.getChannelItemNo());
            thirdChannelFulfillmentRecord.setChannelOrderNo(order.getChannelOrderNo());
            thirdChannelFulfillmentRecord.setChannelOrderName(order.getChannelOrderName());
            thirdChannelFulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.WaitPush);
            iThirdChannelFulfillmentRecordService.save(thirdChannelFulfillmentRecord);
        }
    }


    /**
     * 刷新子订单的售后可执行金额
     *
     * @param orderItem
     */
    private void updateRefundExecutableAmount(OrderItem orderItem) {
        String orderItemNo = orderItem.getOrderItemNo();
        // 新版售后，活动订单发货后，需要把订金加回到可执行金额中
        OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItem.getOrderItemNo());
        Integer orderItemNum = orderItemPrice.getTotalQuantity();
        BigDecimal depositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
        BigDecimal depositUnitPriceSup = orderItemPrice.getOriginalDepositUnitPrice();
        if (depositUnitPrice == null || depositUnitPriceSup == null) {
            return;
        }
        log.info("刷新可执行金额 orderItemNo = {} orderItemNum = {}, depositUnitPrice = {}, depositUnitPriceSup = {}", orderItemNo,
            orderItemNum, depositUnitPrice, depositUnitPriceSup);

        BigDecimal depositTotalPrice = NumberUtil.mul(depositUnitPrice, orderItemNum);
        BigDecimal depositTotalPriceSup = NumberUtil.mul(depositUnitPriceSup, orderItemNum);
        log.info("刷新可执行金额 orderItemNo = {} depositTotalPrice = {}, depositTotalPriceSup = {}", orderItemNo,
            depositTotalPrice, depositTotalPriceSup);

        BigDecimal refundExecutableAmount = orderItem.getPlatformRefundExecutableAmount();
        BigDecimal refundExecutableAmountSup = orderItem.getOriginalRefundExecutableAmount();

        log.info("刷新可执行金额 orderItemNo = {} refundExecutableAmount（原始值） = {}, refundExecutableAmountSup（原始值） = {}",
            orderItemNo, refundExecutableAmount, refundExecutableAmountSup);

        BigDecimal newRefundExecutableAmount = NumberUtil.add(depositTotalPrice, refundExecutableAmount);
        BigDecimal newRefundExecutableAmountSup = NumberUtil.add(depositTotalPriceSup, refundExecutableAmountSup);

        log.info("刷新可执行金额 orderItemNo = {} refundExecutableAmount（新值） = {}, refundExecutableAmountSup（新值） = {}", orderItemNo,
            newRefundExecutableAmount, newRefundExecutableAmountSup);
        orderItem.setPlatformRefundExecutableAmount(newRefundExecutableAmount);
        orderItem.setOriginalRefundExecutableAmount(newRefundExecutableAmountSup);
    }

}
