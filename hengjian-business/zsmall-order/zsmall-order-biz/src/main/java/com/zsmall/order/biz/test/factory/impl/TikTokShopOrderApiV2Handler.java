package com.zsmall.order.biz.test.factory.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazon.client.invoker.ApiException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.redis.utils.MathTimeUtil;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.SysOss;
import com.hengjian.system.domain.vo.SysOssVo;
import com.hengjian.system.mapper.SysOssMapper;
import com.hengjian.system.service.impl.SysOssServiceImpl;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.AttachInfo;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.common.domain.dto.SaleOrderDetailDTO;
import com.zsmall.common.domain.dto.SaleOrderItemDTO;
import com.zsmall.common.domain.req.SearchProductsReq;
import com.zsmall.common.domain.resp.tiktok.express.TikTokApiReturnMsg;
import com.zsmall.common.domain.resp.tiktok.express.TikTokExpressSheet;
import com.zsmall.common.domain.resp.tiktok.fulfillment.SplittableGroup;
import com.zsmall.common.domain.resp.tiktok.fulfillment.SplittableGroupBody;
import com.zsmall.common.domain.resp.tiktok.fulfillment.TikTokPackShippingDocument;
import com.zsmall.common.domain.resp.tiktok.product.resp.PackageDetailsResp;
import com.zsmall.common.domain.resp.tiktok.search.TikTokRespForProduct;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.domain.tiktok.domain.dto.base.TikTokRespBaseEntity;
import com.zsmall.common.domain.tiktok.domain.dto.express.TikTokExpressRelevant;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokCreatePackageResp;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokPackageResp;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokSyncOrderSearchResp;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokSyncSearchResp;
import com.zsmall.common.domain.tiktok.domain.dto.item.TikTokLineItem;
import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokLineOrderCompensation;
import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokOrder;
import com.zsmall.common.domain.tiktok.domain.dto.packages.TikTokCreatePackageData;
import com.zsmall.common.domain.tiktok.domain.dto.payment.TikTokPayment;
import com.zsmall.common.domain.tiktok.domain.dto.req.TikTokCreatePackagesBody;
import com.zsmall.common.domain.vo.SearchReqBody;
import com.zsmall.common.domain.vo.XxlJobSearchVO;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderAttachmentTypeEnum;
import com.zsmall.common.enums.order.TiktokLogisticsTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.tiktok.OrderStatusEnum;
import com.zsmall.common.enums.tiktok.TikTokApiEnums;
import com.zsmall.common.enums.tiktok.TikTokBusinessHandlerEnums;
import com.zsmall.common.handler.AbstractOrderHandler;
import com.zsmall.common.util.ImagesUtil;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.order.biz.factory.ThirdOrderOperationFactory;
import com.zsmall.order.biz.service.DistributorOrderService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.TiktokLineOrderCompensationService;
import com.zsmall.order.biz.test.factory.ThirdBusinessV2Factory;
import com.zsmall.order.biz.utils.TikTokShopApiUtil;
import com.zsmall.order.biz.utils.TikTokUtil;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.order.OrderPayBo;
import com.zsmall.order.entity.domain.bo.order.OrderUpdateBo;
import com.zsmall.order.entity.iservice.IOrderAttachmentService;
import com.zsmall.order.entity.iservice.IOrderItemTrackingRecordService;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.product.entity.iservice.IProductMappingService;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zsmall.common.enums.tiktok.TikTokApiEnums.*;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/4 14:34
 */
@Slf4j
@Lazy
@Component("tikTokShopOrderApiV2")
public class TikTokShopOrderApiV2Handler extends AbstractOrderHandler<TikTokRespBaseEntity,OrderReceiveFromThirdDTO> {
    public static final String ACCESS_TOKEN = "accessToken";
    public static final String REFRESH_TOKEN = "refreshToken";
    public static final String OPEN_ID = "openId";
    public static final String SHOP_CIPHER = "shopCipher";
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private DistributorOrderService distributorOrderService;
    @Resource
    private IOrderItemTrackingRecordService itemTrackingRecordService;
    @Resource
    private TikTokShopApiUtil tikTokShopApiUtil;
    @Resource
    private ImagesUtil imagesUtil;
    @Resource
    private IProductMappingService iProductMappingService;
    @Resource
    private SysOssServiceImpl sysOssService;

    @Resource
    private IOrdersService iOrdersService;

    @Resource
    private SysOssMapper sysOssMapper;

    @Resource
    private IOrderAttachmentService iOrderAttachmentService;

    @Resource
    private ISiteCountryCurrencyService iSiteCountryCurrencyService;

    @Resource
    private TikTokUtil tikTokUtil;
//    @XxlConf(value = "bizark-erp.tiktok.switch",defaultValue = "false")
//    public static Boolean TIKTOK_SWITCH;

    @Resource
    private TiktokLineOrderCompensationService tiktokLineOrderCompensationService;

    @Resource
    private RedisTemplate redisTemplate;

    //    @Resource
//    private ThirdOrderSaveApiFactory factory;
    @Resource
    private ThirdOrderOperationFactory factory;
    @Resource
    private OrdersService ordersService;

    @Resource
    private ThirdBusinessV2Factory thirdBusinessFactory;

    @Resource
    private ITenantSalesChannelService tenantSalesChannelService;

    @Override
    public boolean isNeedByChannel(JSONObject json, TikTokApiEnums enums) {
        return true;
    }

    @Override
    public boolean isNeedExpressSheetFlow(TikTokRespBaseEntity targetData) {
        // 判断是否是自提单,如果系统已经录入了,将录入的单据移除,不重复拆单
        return true;
    }

    @Override
    public TikTokRespBaseEntity getProductList(JSONObject json, String channel, String shopId,
                                               Class<TikTokRespBaseEntity> targetClass,String tiktokAppKey,String tiktokAppSecret) {

        XxlJobSearchVO vo = JSON.toJavaObject(json, XxlJobSearchVO.class);
        SearchProductsReq searchProductsReq = new SearchProductsReq();
        BeanUtil.copyProperties(vo, searchProductsReq);

//        HashMap<String, String> hashMap = new HashMap<>();
        String query = JSONObject.toJSONString(searchProductsReq);
        Map hashMap = (Map) JSONObject.parse(query);
        // 要把shop_id转换
        TenantSalesChannel tenantSalesChannel = tenantSalesChannelService.getShopId(shopId);
        if (ObjectUtil.isEmpty(tenantSalesChannel)) {
            return null;
        }
        String connectStr = tenantSalesChannel.getConnectStr();

        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            log.error("tiktok auth error [{}] : accessToken is empty ", shopId);
            return null;
        }
        SearchReqBody searchProductsReqBody = new SearchReqBody();


        BeanUtil.copyProperties(vo, searchProductsReqBody);
        hashMap.put("shop_id", jsonObject.get("shopId"));
        String body = JSONObject.toJSONString(searchProductsReqBody);
        TikTokRespForProduct tikTokRespForProduct = tikTokUtil.postTikTokShopReturnV2(body, SEARCH_PRODUCTS.getUrl(), SEARCH_PRODUCTS.getPath(), TikTokRespForProduct.class, hashMap, tiktokAppKey, tiktokAppSecret);
        tikTokRespForProduct.setAppKey(tiktokAppKey);
        tikTokRespForProduct.setAppSecret(tiktokAppKey);
        return tikTokRespForProduct;

    }

//    @Override
//    public TikTokRespBaseEntity getOrderList(JSONObject json, String channel, String shopId,
//                                             Class<TikTokRespBaseEntity> targetClass) {
//
//        return testData(TikTokSyncSearchResp.class);
//
//    }

//    private TikTokRespBaseEntity testProperty(String data,TikTokSyncSearchResp.class){
//        TikTokSyncSearchResp responseMsg = JSONObject.parseObject(data, tikTokSyncSearchRespClass);
//        return responseMsg;
//    }

    @Override
    public TikTokRespBaseEntity getOrderList(JSONObject json, String channel, String shopId,
                                             Class<TikTokRespBaseEntity> targetClass,String tiktokAppKey,String tiktokAppSecret) {
        XxlJobSearchVO vo = JSON.toJavaObject(json, XxlJobSearchVO.class);
        SearchProductsReq searchProductsReq = new SearchProductsReq();
        BeanUtil.copyProperties(vo, searchProductsReq);

        String query = JSONObject.toJSONString(searchProductsReq);

        Map hashMap = (Map) JSONObject.parse(query);
        // 要把shop_id转换
        TenantSalesChannel tenantSalesChannel = tenantSalesChannelService.getShopId(shopId);
        if (ObjectUtil.isEmpty(tenantSalesChannel)) {
            return null;
        }
        String connectStr = tenantSalesChannel.getConnectStr();
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            log.error("tiktok auth error [{}] : accessToken is empty ", shopId);
            return null;
        }
        SearchReqBody searchProductsReqBody = new SearchReqBody();

        BeanUtil.copyProperties(vo, searchProductsReqBody);
        hashMap.put("shop_id", jsonObject.get("shopId"));
        hashMap.put("sort_field", "create_time");
        hashMap.put("sort_order", "DESC");
        String body = JSONObject.toJSONString(searchProductsReqBody);
        // 做个过滤器 tikTokUtil.postTikTokShopReturnV2(body, GET_ORDER_LIST.getUrl(), GET_ORDER_LIST.getPath(), TikTokSyncSearchResp.class, hashMap)
//        TikTokSyncSearchResp tikTokSyncSearchResp = tikTokUtil.postTikTokShopReturnV2(body, GET_ORDER_LIST.getUrl(), GET_ORDER_LIST.getPath(), TikTokSyncSearchResp.class, hashMap);
//        Object data1 = tikTokSyncSearchResp.getData();
////
////
//        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;
//        if(!CollUtil.isNotEmpty(orderResp.getOrders())){
//            log.error("tiktok order list is empty");
//        }else{
//            if (orderResp.getOrders().stream().anyMatch(order -> TiktokLogisticsTypeEnum.SELLER.name().equals(order.getShippingType()))) {
//                // 判断
//                List<TikTokOrder> filteredOrders = orderResp.getOrders().stream()
//                                                            .filter(order -> TiktokLogisticsTypeEnum.SELLER.name().equals(order.getTikTokOrderId()))
//                                                            .collect(Collectors.toList());
//
//                orderResp.setOrders(filteredOrders);
//                log.info(JSONObject.toJSONString(orderResp));
//                tikTokSyncSearchResp.setData(orderResp);
//            }
//        }

//
//        log.info(tikTokSyncSearchResp.toString());
        // 筛选 子id
//        TikTokSyncSearchResp tikTokSyncSearchResp = tikTokUtil.postTikTokShopReturnV2(body, GET_ORDER_LIST.getUrl(), GET_ORDER_LIST.getPath(), TikTokSyncSearchResp.class, hashMap);
//        Object data1 = tikTokSyncSearchResp.getData();
//
//        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;
//        List<TikTokOrder> orders = orderResp.getOrders();
//        ArrayList<String> lineOrderItemIds = new ArrayList<>();
//        lineOrderItemIds.add("576616977252979564");
//        Set<String> itemIdSet = new HashSet<>(lineOrderItemIds);
//        orders = orders.stream()
//                       // 过滤出那些 lineItems 不包含需要移除的 line item 的 TikTokOrder
//                       .filter(order -> {
//                           order.setLineItems(order.getLineItems().stream()
//                                                   .filter(lineItem -> !itemIdSet.contains(lineItem.getId()))
//                                                   .collect(Collectors.toList()));
//                           // 如果 lineItems 列表为空，则返回 false 以移除整个 TikTokOrder
//                           return !order.getLineItems().isEmpty();
//                       })
//                       .collect(Collectors.toList());
//
//        orderResp.setOrders(orders);
//        tikTokSyncSearchResp.setData(orderResp);
//        log.info(tikTokSyncSearchResp.toString());
//        return tikTokSyncSearchResp;
        // 此处要抓模拟数据
//        tikTokUtil.postTikTokShopReturnV2(body, GET_ORDER_LIST.getUrl(), GET_ORDER_LIST.getPath(), TikTokSyncSearchResp.class, hashMap);
        return tikTokUtil.postTikTokShopReturnV2(body, GET_ORDER_LIST.getUrl(), GET_ORDER_LIST.getPath(), TikTokSyncSearchResp.class, hashMap,tiktokAppKey,tiktokAppSecret);
//        return tikTokSyncSearchResp;

    }


    @Override
    public TikTokRespBaseEntity getOrderListSpecify(JSONObject json, String channel, String shopId,
                                                    Class<TikTokRespBaseEntity> targetClass,String tiktokAppKey,String tiktokAppSecret) {
        XxlJobSearchVO vo = JSON.toJavaObject(json, XxlJobSearchVO.class);
        SearchProductsReq searchProductsReq = new SearchProductsReq();
        BeanUtil.copyProperties(vo, searchProductsReq);

        String query = JSONObject.toJSONString(searchProductsReq);

        Map hashMap = (Map) JSONObject.parse(query);
        // 要把shop_id转换
        TenantSalesChannel tenantSalesChannel = tenantSalesChannelService.getShopId(shopId);
        if (ObjectUtil.isEmpty(tenantSalesChannel)) {
            return null;
        }
        String connectStr = tenantSalesChannel.getConnectStr();
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            log.error("tiktok auth error [{}] : accessToken is empty ", shopId);
            return null;
        }
        SearchReqBody searchProductsReqBody = new SearchReqBody();

        BeanUtil.copyProperties(vo, searchProductsReqBody);
        hashMap.put("shop_id", jsonObject.get("shopId"));
        String body = JSONObject.toJSONString(searchProductsReqBody);
        // 做个过滤器 tikTokUtil.postTikTokShopReturnV2(body, GET_ORDER_LIST.getUrl(), GET_ORDER_LIST.getPath(), TikTokSyncSearchResp.class, hashMap)
        TikTokSyncSearchResp tikTokSyncSearchResp = tikTokUtil.postTikTokShopReturnV2(body, GET_ORDER_LIST.getUrl(), GET_ORDER_LIST.getPath(), TikTokSyncSearchResp.class, hashMap,tiktokAppKey,tiktokAppSecret);
        Object data1 = tikTokSyncSearchResp.getData();


        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;
        List<String> orderNos = new ArrayList<>();
        // 这里走配置化
        orderNos.add("576617329512321685");
        orderNos.add("576617057940378494");
        if (orderResp.getOrders().stream().anyMatch(order -> orderNos.contains(order.getTikTokOrderId()))) {
            // 判断
            List<TikTokOrder> filteredOrders = orderResp.getOrders().stream()
                                                        .filter(order -> !orderNos.contains(order.getTikTokOrderId()))
                                                        .collect(Collectors.toList());

            orderResp.setOrders(filteredOrders);
            log.info(JSONObject.toJSONString(orderResp));
            tikTokSyncSearchResp.setData(orderResp);
        }
        return tikTokSyncSearchResp;
//
//        log.info(tikTokSyncSearchResp.toString());
        // 筛选 子id
//        TikTokSyncSearchResp tikTokSyncSearchResp = tikTokUtil.postTikTokShopReturnV2(body, GET_ORDER_LIST.getUrl(), GET_ORDER_LIST.getPath(), TikTokSyncSearchResp.class, hashMap);
//        Object data1 = tikTokSyncSearchResp.getData();
//
//        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;
//        List<TikTokOrder> orders = orderResp.getOrders();
//        ArrayList<String> lineOrderItemIds = new ArrayList<>();
//        lineOrderItemIds.add("576616977252979564");
//        Set<String> itemIdSet = new HashSet<>(lineOrderItemIds);
//        orders = orders.stream()
//                       // 过滤出那些 lineItems 不包含需要移除的 line item 的 TikTokOrder
//                       .filter(order -> {
//                           order.setLineItems(order.getLineItems().stream()
//                                                   .filter(lineItem -> !itemIdSet.contains(lineItem.getId()))
//                                                   .collect(Collectors.toList()));
//                           // 如果 lineItems 列表为空，则返回 false 以移除整个 TikTokOrder
//                           return !order.getLineItems().isEmpty();
//                       })
//                       .collect(Collectors.toList());
//
//        orderResp.setOrders(orders);
//        tikTokSyncSearchResp.setData(orderResp);
//        log.info(tikTokSyncSearchResp.toString());
//        return tikTokSyncSearchResp;
//        return tikTokUtil.postTikTokShopReturnV2(body, GET_ORDER_LIST.getUrl(), GET_ORDER_LIST.getPath(), TikTokSyncSearchResp.class, hashMap);

    }

    /**
     * 功能描述：插入业务数据,后因为功能扩展需要加入面单流程,导致面单处理流程被包含内此方法内
     *
     * @param type            类型
     * @param channelTypeEnum 通道类型枚举
     * @param json            json
     * @param targetData      目标数据
     * @param data            数据
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBusinessData(BusinessType type, ChannelTypeEnum channelTypeEnum, JSONObject json,
                                   Class<TikTokRespBaseEntity> targetData, TikTokRespBaseEntity data) {
        thirdBusinessFactory.getInvokeStrategy(TikTokBusinessHandlerEnums.getHandlerByTag(type.name()))
                            .insertBusinessData(data, json);

    }

    @Override
    public TikTokRespBaseEntity dataScreening(BusinessType type, ChannelTypeEnum channelTypeEnum, JSONObject json,
                                              Class<TikTokRespBaseEntity> targetData, TikTokRespBaseEntity data) {
        return thirdBusinessFactory.getInvokeStrategy(TikTokBusinessHandlerEnums.getHandlerByTag(type.name()))
                                   .dataScreening(data, json);
    }
    /**
     * 功能描述：抖音 wrapper
     *
     * @param tiktokDTO         抖音 DTO
     * @param order             次序
     * @param lineItem          订单项
     * @param vo                VO
     * @param packageDetailsMap
     * @return {@link OrderReceiveFromThirdDTO }
     * <AUTHOR>
     * @date 2024/02/02
     */
    private OrderReceiveFromThirdDTO tiktokDTOWrapper(OrderReceiveFromThirdDTO tiktokDTO, TikTokOrder order,
                                                      TikTokLineItem lineItem, XxlJobSearchVO vo,
                                                      ConcurrentHashMap<String, PackageDetailsResp> packageDetailsMap) {


        tiktokDTO.setTenantId(vo.getTenantId());

        tiktokDTO.setThirdChannelFlag(vo.getThirdChannelFlag());
        tiktokDTO.setOrderNo(order.getTikTokOrderId());
        tiktokDTO.setOrderStatus(order.getStatus());
        //军哥说tiktok items 内按照每一件货物来分组,所以这里默认都给1即可
        tiktokDTO.setTotalQuantity(1);
        TikTokRecipientAddress address = order.getRecipientAddress();
        String regionCode = address.getRegionCode();
        log.info("orderNo:{},tk-regionCode:{}", order.getTikTokOrderId(),regionCode);
        SiteCountryCurrency site = iSiteCountryCurrencyService.getSiteByCountryCode(regionCode);
        tiktokDTO.setCurrencyCode(site.getCurrencyCode());
        try {
            tiktokDTO.setCreateTime(MathTimeUtil.longToDate(order.getCreateTime()));
            tiktokDTO.setPaidTime(MathTimeUtil.longToDate(order.getPaidTime()));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        String shippingType = order.getShippingType();
        if (TiktokLogisticsTypeEnum.TIKTOK.name().equals(shippingType)) {
            shippingType = LogisticsTypeEnum.PickUp.name();
        }
        if (TiktokLogisticsTypeEnum.SELLER.name().equals(shippingType)) {
            shippingType = LogisticsTypeEnum.DropShipping.name();
        }
        tiktokDTO.setLogisticsType(shippingType);
        tiktokDTO.setRemark(order.getBuyerMessage());

        // 物流
        SaleOrderDetailDTO saleOrderDetailDTO = new SaleOrderDetailDTO();
        // 用子订单
        saleOrderDetailDTO.setCarrier(lineItem.getShippingProviderName());
        // 这里需要根据lineItem的size看 一件代发拿order 自提应该拿的是lineItem的trackingNumber

//        saleOrderDetailDTO.setLogisticsTrackingNo(order.getTrackingNumber());
        saleOrderDetailDTO.setLogisticsTrackingNo(lineItem.getTrackingNumber());

        // item详情
        List<SaleOrderItemDTO> itemDTOS = new ArrayList<SaleOrderItemDTO>();
        SaleOrderItemDTO itemDTO = new SaleOrderItemDTO();
        itemDTO.setErpSku(lineItem.getSellerSku());
        //军哥说tiktok items 内按照每一件货物来分组,所以这里默认都给1即可
//        itemDTO.setQuantity(packageSkuMsg.getQuantity());
        itemDTO.setQuantity(1);
        itemDTO.setSkuId(lineItem.getSkuId());
        itemDTO.setUnitPrice(new BigDecimal(lineItem.getSalePrice()));
        itemDTOS.add(itemDTO);
        //地址
        TikTokRecipientAddress recipientAddress = order.getRecipientAddress();
        recipientAddress.setBuyerEmail(order.getBuyerEmail());

        TikTokPayment payment = order.getPayment();
        tiktokDTO.setSubTotal(payment.getSubTotal());
        tiktokDTO.setTotalAmount(payment.getTotalAmount());
        tiktokDTO.setSaleOrderDetails(saleOrderDetailDTO);
        tiktokDTO.pushSaleOrderItemsList(itemDTOS);
        tiktokDTO.setAddress(recipientAddress);
        tiktokDTO.setLineOrderItemId(lineItem.getId());
        return tiktokDTO;
    }
    private List<OrderReceiveFromThirdDTO> parseTikTokDataForDistribution(TikTokRespBaseEntity data,
                                                                          XxlJobSearchVO vo) {
        List<OrderReceiveFromThirdDTO> tiktokDTOS = new ArrayList<>();
        Object data1 = data.getData();
        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;
        // 原始订单存在一个订单多个子订单项
        List<TikTokOrder> orders = orderResp.getOrders();

        // 正式环境
        for (TikTokOrder order : orders) {
            log.info("tiktok数据拆解开始---,渠道订单号:{}", order.getTikTokOrderId());
            List<TikTokLineItem> lineItems = order.getLineItems();
            for (TikTokLineItem lineItem : lineItems) {
                OrderReceiveFromThirdDTO tiktokDTO = new OrderReceiveFromThirdDTO();
                // 拆分成1个tiktok订单 1个tiktok订单项 用以录入分销系统内 (原本多个子订单的会以sku纬度录入系统)
                tiktokDTO = tiktokDTOWrapper(tiktokDTO, order, lineItem, vo, null);
                tiktokDTOS.add(tiktokDTO);
            }

        }
        log.info("tiktok数据拆解完成---,数据:{}", JSON.toJSONString(tiktokDTOS));
        return tiktokDTOS;
    }

    @Override
    public List<OrderReceiveFromThirdDTO> dataScreeningV2(BusinessType type,
                                                          ChannelTypeEnum channelTypeEnum,
                                                          JSONObject json,
                                                          Class<TikTokRespBaseEntity> targetData,
                                                          TikTokRespBaseEntity data, String shopId) {
        XxlJobSearchVO vo = JSON.toJavaObject(json, XxlJobSearchVO.class);
        // 拆解包装
        List<String> lineOrderItemIds = new ArrayList<>();
        List<OrderReceiveFromThirdDTO> dtos1 = new ArrayList<OrderReceiveFromThirdDTO>();
        List<OrderReceiveFromThirdDTO> dtos = parseTikTokDataForDistribution(data, vo);
        for (OrderReceiveFromThirdDTO dto : dtos) {
            List<SaleOrderItemDTO> saleOrderItemsList = dto.getSaleOrderItemsList();
            SaleOrderDetailDTO saleOrderDetails = dto.getSaleOrderDetails();
            if(ObjectUtil.isNotEmpty(saleOrderDetails)){
                List<AttachInfo> attachInfoItems = saleOrderDetails.getAttachInfoItems();
                if(CollUtil.isNotEmpty(attachInfoItems)){
                    for (AttachInfo attachInfoItem : attachInfoItems) {
                        attachInfoItem.setFileType(OrderAttachmentTypeEnum.ShippingLabel.getCode());
                    }
                }
            }
            // 需要通过itemOrderId进行搜索
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getLineOrderItemId, dto.getLineOrderItemId())
                                                                             .eq(Orders::getDelFlag, 0).last("limit 1");

            Orders order = TenantHelper.ignore(()->iOrdersService.getOne(lqw));
            if (ObjectUtil.isNotEmpty(order)) {
                // 需要过滤的子订单
                lineOrderItemIds.add(dto.getLineOrderItemId());
                continue;
            }
            // 新业务 此处不需要再移除未映射的订单
//            for (SaleOrderItemDTO itemDTO : saleOrderItemsList) {
//
////                Pattern pattern = Pattern.compile("\\D");
////                Matcher matcher = pattern.matcher(itemDTO.getErpSku());
////                boolean result = matcher.find();
////                if(!result){
////                    log.error("\n产品格式异常,或为直播产品不予录入,channelSku:{},店铺:{},租户标识:{}", itemDTO.getErpSku(),dto.getThirdChannelFlag(),dto.getTenantId());
////                    lineOrderItemIds.add(dto.getLineOrderItemId());
////                }
////                lineOrderItemIds.add(dto.getLineOrderItemId());
//            }
            dto.setShopId(Long.valueOf(shopId));
            dtos1.add(dto);
        }
        // 把data内的数据过滤掉
        Object data1 = data.getData();

        // 对异常子订单进行过滤,如果订单内的商品全部不符合,则过滤掉主订单
        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;
        List<TikTokOrder> orders = orderResp.getOrders();
        Set<String> itemIdSet = new HashSet<>(lineOrderItemIds);
        orders = orders.stream()
                       // 过滤出那些 lineItems 不包含需要移除的 line item 的 TikTokOrder
                       .filter(order -> {
                           order.setLineItems(order.getLineItems().stream()
                                                   .filter(lineItem -> !itemIdSet.contains(lineItem.getId()))
                                                   .collect(Collectors.toList()));
                           // 如果 lineItems 列表为空，则返回 false 以移除整个 TikTokOrder
                           return !order.getLineItems().isEmpty();
                       })
                       .collect(Collectors.toList());
        log.info(data.toString());
        orderResp.setOrders(orders);
        data.setData(orderResp);
//        log.info("过滤后的数据:{}", JSONObject.toJSONString(data));
        return dtos1;
    }
    @Override
    public Boolean isNeedPay(List<OrderReceiveFromThirdDTO> orderReceiveFromThirdDTO) {

        return Boolean.TRUE;
    }


    @Override
    public void operationalDataForBusiness(BusinessType type, ChannelTypeEnum channelTypeEnum, JSONObject json,
                                           Class<TikTokRespBaseEntity> targetClass, List<OrderReceiveFromThirdDTO> list, BusinessTypeMappingEnum mappingEnum) {
        thirdBusinessFactory.getInvokeStrategy(TikTokBusinessHandlerEnums.getHandlerByTag(type.name()))
                            .insertBusinessDataTemplate(list, json, mappingEnum, channelTypeEnum);
    }

    @Override
    public TikTokRespBaseEntity getBusinessData(List<OrderReceiveFromThirdDTO> list,
                                                BusinessTypeMappingEnum type) {
        return null;
    }

    @Override
    public ConcurrentHashMap<String, ConcurrentHashMap<String, String>> generateBusinessNo(
        ConcurrentHashMap<String, TikTokRespBaseEntity> map) {
        return null;
    }

    @Override
    public boolean dataValidation(TikTokRespBaseEntity targetData) {
        Object data = targetData.getData();
        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data;

        if(CollUtil.isEmpty(orderResp.getOrders())){
            return false;
        }
        return true;
    }

    @Override
    public boolean dataValidation(List<OrderReceiveFromThirdDTO> targetData) {
        if (CollectionUtil.isEmpty(targetData)) {
            return false;
        }
        return true;
    }

    @Override
    public void payOrder(TikTokRespBaseEntity targetData, String name) {
        Object data1 = targetData.getData();
        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;
        List<TikTokOrder> orders = orderResp.getOrders();
        if(ObjectUtil.isNotEmpty(orders)){
            TikTokOrder tikTokOrder = orders.get(0);
            Orders order = iOrdersService.getOne(new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, tikTokOrder.getTikTokOrderId())
                                                                                 .last("limit 1"));
            List<String> lineItemIds = orders.stream().flatMap(item -> item.getLineItems().stream())
                                             .map(TikTokLineItem::getId).collect(Collectors.toList());
//      ZSMallSystemEventUtils.checkAutoPaymentEvent(order.getTenantId()) 过滤掉映射异常的,此处由于使用的是tk原始数据,确实会存在 有映射和无映射的两种情况,所以下面的逻辑要对无映射的情况进行排除
            if (ZSMallSystemEventUtils.checkWalletAutoPaymentEvent(order.getTenantId(),order.getCurrency())) {
                LambdaQueryWrapper<Orders> lqw =null;
                // 自提单
                if(TiktokLogisticsTypeEnum.TIKTOK.name().equals(tikTokOrder.getShippingType())){
                    lqw = new LambdaQueryWrapper<Orders>().in(Orders::getLineOrderItemId, lineItemIds)
                                                          .eq(Orders::getIsSplit, true)
                                                          .eq(Orders::getDelFlag, 0)
                                                          .ne(Orders::getExceptionCode,1);
                }
                // 一件代发单
                if(TiktokLogisticsTypeEnum.SELLER.name().equals(tikTokOrder.getShippingType())){
                    lqw = new LambdaQueryWrapper<Orders>().in(Orders::getLineOrderItemId, lineItemIds)
                                                          .eq(Orders::getDelFlag, 0)
                                                          .ne(Orders::getExceptionCode,1);
                }

                LambdaQueryWrapper<Orders> finalLqw = lqw;
                List<String> orderNos = TenantHelper.ignore(() -> iOrdersService.list(finalLqw)).stream().map(Orders::getOrderNo)
                                                    .collect(Collectors.toList());
                if(CollUtil.isEmpty(orderNos)){
                    return;
                }
                OrderPayBo bo = new OrderPayBo();
                bo.addOrderNoList(orderNos);
                bo.setPaymentPassword(null);

                try {
                    TenantHelper.ignore(() -> {
                        try {
                            return ordersService.payOrderForErp(bo, order.getTenantId(), true, true);
                        } catch (Exception e) {
                            log.error("支付失败", e);
                            throw new RuntimeException(e.getMessage());
                        }
                    });
                } catch (Exception e) {
                    log.error("支付失败", e);
                    log.error("支付失败:{}", e.getMessage());
                }

            }
        }


    }

    @Override
    public void expandBusiness(TikTokRespBaseEntity targetData, String name) {
        // 订单数据处理,打分割标识
        Object data1 = targetData.getData();
        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;
        List<TikTokOrder> orders = orderResp.getOrders();
        // 拿到orders内所有的lineItems的id
        List<String> lineItemIds = orders.stream().filter(order -> TiktokLogisticsTypeEnum.TIKTOK.name()
                                                                                                 .equals(order.getShippingType()))
                                         .flatMap(order -> order.getLineItems().stream())
                                         .map(TikTokLineItem::getId).collect(Collectors.toList());
        HashMap<String, Object> map = new HashMap<>();
        if (CollUtil.isNotEmpty(lineItemIds)) {
            lineItemIds.forEach(item -> {

                SysOss oss = sysOssMapper.selectOne(new LambdaQueryWrapper<SysOss>().eq(SysOss::getBusinessId, item).last("limit 1"));
                SysOssVo sysOssVo = MapstructUtils.convert(oss, SysOssVo.class);
                // 后续开异步了再用中间件
//                RedissonClient client = RedisUtils.getClient();
//                String key = "order:sys:oss:"+item;
//                SysOssVo sysOssVo = (SysOssVo) client.getBucket(key).get();
//                if(ObjectUtil.isEmpty(sysOssVo)){
//                    sysOssVo = sysOssMapper.selectByLineOrderItemId(item);
//                }
                if (ObjectUtil.isNull(sysOssVo)) {
                    log.error("子订单获取面单失败:{}", item);
                }
                if (ObjectUtil.isNotEmpty(sysOssVo)) {
                    map.put(item, sysOssVo);
                }
            });
            // 查到所有的orders
            List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.list(new LambdaQueryWrapper<Orders>()
                .eq(Orders::getLogisticsType, LogisticsTypeEnum.PickUp)
                .in(Orders::getLineOrderItemId, lineItemIds)));
            if (ordersList.size() < lineItemIds.size()) {
                // 如果查到的订单数和lineItemIds的数量不一致,说明部分订单录入失败,以查到的订单数为准
                // 找到ordersList中没有,但lineItemIds里有的id,通过Orders.lineOrderItemId=lineItemIds
                Set<String> orderLineItemIdsSet = ordersList.stream()
                                                            .map(Orders::getLineOrderItemId)
                                                            .collect(Collectors.toSet());
                List<String> missingIds = lineItemIds.stream()
                                                     .filter(id -> !orderLineItemIdsSet.contains(id))
                                                     .collect(Collectors.toList());
                log.error("Missing orders: {}", missingIds);
            }

            // 上传附件-面单
            List<Orders> list = new ArrayList<>();
            Map<String, TikTokOrder> orderMap = orders.stream()
                                                      .collect(Collectors.toMap(
                                                          TikTokOrder::getTikTokOrderId,
                                                          order -> order,
                                                          (existing, replacement) -> existing // 当键冲突时，保留现有的值并丢弃新的值
                                                      ));

//            List<OrderItemTrackingRecord> records = new ArrayList<>();
            for (Orders order : ordersList) {
                TikTokOrder tikTokOrder = orderMap.get(order.getChannelOrderNo());

                if (TiktokLogisticsTypeEnum.SELLER.name().equals(tikTokOrder.getShippingType())) {
                    continue;
                }

                SysOssVo sysOssVo = (SysOssVo) map.get(order.getLineOrderItemId());
                if (ObjectUtil.isNotEmpty(sysOssVo)) {
                    OrderUpdateBo bo = new OrderUpdateBo(order.getOrderNo(), null, true, sysOssVo);
                    // 实际上传完了以后已经有了文件了 不需要再走上传的逻辑了
                    distributorOrderService.uploadShippingLabel(bo);
                    list.add(order);
                    // 如果没有trackingNumber的话查redis
//                    OrderItemTrackingRecord one = itemTrackingRecordService.getOne(new LambdaQueryWrapper<OrderItemTrackingRecord>().eq(OrderItemTrackingRecord::getOrderNo, order.getOrderNo())
//                                                                                                                                                        .last("limit 1"));

//                    if(ObjectUtil.isEmpty(one)||ObjectUtil.isEmpty(one.getLogisticsTrackingNo())){
////                        String key = "order:tracking:" + order.getLineOrderItemId();
////                        RedissonClient client = RedisUtils.getClient();
////                        OrderItemTrackingRecord record =(OrderItemTrackingRecord) client
////                            .getBucket(key).get();
//                        log.info("订单号:{},从redis中获取附件信息: {}",order.getOrderNo(),JSONObject.toJSON(record));
//                        record.setLogisticsTrackingNo(sysOssVo.getBusinessNumber());
//                        records.add(record);
//                    }

                }
            }
            List<Orders> updatedOrdersList = list.stream()
                                                 .map(order -> {
                                                     order.setSplit(true);
                                                     return order;
                                                 })
                                                 .collect(Collectors.toList());
            if(CollUtil.isNotEmpty(updatedOrdersList)){
                iOrdersService.updateBatchById(updatedOrdersList);
            }
//
//            if(CollUtil.isNotEmpty(records)){
//                itemTrackingRecordService.saveBatch(records);
//            }


        }


    }

    @Override
    public void expressSheet(TikTokRespBaseEntity target, String shopId,String tiktokAppKey,String tiktokAppSecret) {

        Object data1 = target.getData();
        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;

        List<TikTokOrder> orders = orderResp.getOrders();

        for (TikTokOrder order : orders) {
            // 过滤掉非自提单
            if (TiktokLogisticsTypeEnum.SELLER.name().equals(order.getShippingType())) {
                continue;
            }
            // 分销系统内已经录入的订单不再进行面单获取
//            if (order.getTikTokOrderId().equals("576609381234021316")) {
//                expressSheetApart(order, shopId);
//            }
            expressSheetApart(order, shopId,tiktokAppKey,tiktokAppSecret);
        }

    }

    /**
     * 功能描述：快递单拆分
     *
     * @param order  顺序
     * @param shopId 店铺id
     * <AUTHOR>
     * @date 2024/04/08
     */
    private void expressSheetApart(TikTokOrder order, String shopId,String tiktokAppKey,String tiktokAppSecret) {
        // 补偿计划 异常情况:1.TrackingNo为空 2.包裹id为空
        TikTokExpressSheet tikTokExpressSheet = getExpressSheet(order);
        TikTokApiReturnMsg tikTokApiReturnMsg = getTikTokApiReturnMsg(tikTokExpressSheet);
        AtomicInteger atomicInteger = null;
        // 面单url 相关信息
        TikTokExpressRelevant tikTokExpressRelevant = new TikTokExpressRelevant();
        TikTokPackageResp tikTokPackageResp;
        List<String> ids = new ArrayList<>();
        List<TikTokLineItem> lineItems = order.getLineItems();
        List<TikTokLineOrderCompensation> tikTokLineOrderCompensations = new ArrayList<>();

        try {

            if (tikTokApiReturnMsg != null) {
                tikTokApiReturnMsg.setChannel(tikTokExpressSheet.getChannel());
                tikTokApiReturnMsg.setOrderNo(tikTokExpressSheet.getOrderNo());
            }


            //1.1 判断订单物流模式是平台提供还是卖家自己提供，卖家自己提供不买面单
            if (LogisticsTypeEnum.DropShipping.name().equals(tikTokExpressSheet.getShippingType())) {
                log.info("channel {} getLabel orderNo {} 物流模式为卖家自提供", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo());
                return;
            }

            //1.2 判断订单状态 不是AWAITING_SHIPMENT不购买面单
            if (OrderStatusEnum.AWAITING_SHIPMENT.name().equals(tikTokExpressSheet.getOrderStatus())) {
                log.info("channel {} getLabel orderNo {} 状态为AWAITING_SHIPMENT，可购买面单", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo());
                // 如果购买过面单 就不再购买 直接获取面单信息
//2.1 订单行数大于1，先拆分订单
                if (order.getLineItems().size() > 1) {
                    if (!"split".equals(tikTokExpressSheet.getSplitOrCombineTag())) {
                        HashMap hashMap = new HashMap<>();
                        TenantSalesChannel tenantSalesChannel = tenantSalesChannelService.getShopId(shopId);
                        String connectStr = tenantSalesChannel.getConnectStr();

                        JSONObject jsonObject = JSON.parseObject(connectStr);

                        hashMap.put("shop_id", jsonObject.get("shopId"));
                        // 子项目单
                        SplittableGroup splittableGroup = new SplittableGroup();
                        splittableGroup.setId(String.valueOf(Math.abs(UUID.fastUUID().getLeastSignificantBits())));
                        List<String> collect = order.getLineItems().stream().map(TikTokLineItem::getId)
                                                    .collect(Collectors.toList());
                        splittableGroup.addOrderLineItemIds(collect);
                        List<SplittableGroup> splittableGroups = new ArrayList<>();
                        splittableGroups.add(splittableGroup);
                        SplittableGroupBody splittableGroupBody = new SplittableGroupBody(splittableGroups);
                        //拆单
                        String body = JSONObject.toJSONString(splittableGroupBody);
                        tikTokPackageResp = tikTokUtil.postTikTokShopReturnV2(body, SPLIT_ORDERS.getUrl(), SPLIT_ORDERS.formatPath(order.getTikTokOrderId(), "{order_id}"), TikTokPackageResp.class, hashMap,tiktokAppKey,tiktokAppSecret);
                        log.info("channel {} getLabel orderNo {} 拆单结果：{}", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo(), JSONObject.toJSONString(tikTokPackageResp));
                        if (ObjectUtil.isEmpty(tikTokPackageResp)) {
                            log.info("channel {} getLabel orderNo {} 拆单失败", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo());
                            throw new ApiException(500, "Internal Server Error");
                        }
                    }
                }

            } else if (!OrderStatusEnum.AWAITING_COLLECTION.name().equals(tikTokExpressSheet.getOrderStatus())) {
                return;
            }


            //2.2 获取面单信息
            for (TikTokLineItem lineItem : lineItems) {
                String status = lineItem.getDisplayStatus();
                if (OrderStatusEnum.AWAITING_SHIPMENT.name()
                                                     .equals(status) || OrderStatusEnum.AWAITING_COLLECTION.name()
                                                                                                           .equals(status)) {
                    if (ObjectUtil.isEmpty(lineItem)) {
                        log.info("channel {} getLabel orderNo {} 订单明细行不存在", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo());
                        return;
                    }
                    //2.2.1 判断是否有包裹ID，无包裹ID先创建包裹
                    String packageId = lineItem.getPackageId();
                    TikTokCreatePackagesBody packagesBody = new TikTokCreatePackagesBody();

                    packagesBody.setOrderId(order.getTikTokOrderId());
                    packagesBody.setOrderLineItemIds(Arrays.asList(lineItem.getId()));
                    String body = JSONObject.toJSONString(packagesBody);
                    TikTokCreatePackageResp tikTokCreatePackageResp = null;
                    if (StrUtil.isEmpty(packageId)) {
                        tikTokCreatePackageResp = tikTokShopApiUtil.createPackage(body, shopId,tiktokAppKey,tiktokAppSecret);

                        if (ObjectUtil.isNotEmpty(tikTokCreatePackageResp)) {
                            TikTokCreatePackageData data = tikTokCreatePackageResp.getData();
                            if (ObjectUtil.isNotEmpty(data)) {
                                packageId = data.getPackageId();

//                            TikTokShippingServiceInfo shippingServiceInfo = data.getShippingServiceInfo();
                                // 将shippingServiceInfo转为json
//                            JSONObject shippingServiceInfoJson = (JSONObject) JSONObject.toJSON(shippingServiceInfo);
                            }
                            tikTokApiReturnMsg.setMsg(null);
                            tikTokApiReturnMsg.setErrorMsg(null);

                        } else {
                            throw new ApiException(500, "Internal Server Error");
                        }
                        //休眠5秒避免获取包裹详细信息时获取不到tracking
                        try {
                            TimeUnit.SECONDS.sleep(15);
                        } catch (InterruptedException e) {
                            log.error("tiktok获取面单休眠异常：{}", e.getMessage());
                        }
                    }

                    if (StrUtil.isBlank(packageId)) {
//                        tikTokApiReturnMsg.getOrderNo() order.getTikTokOrderId()
                        TikTokLineOrderCompensation tikTokLineOrderCompensation = getCompensation(tikTokApiReturnMsg.getOrderNo(),lineItem.getId(),null,null,order.getTikTokOrderId(),ZSMallStatusCodeEnum.TIKTOK_PACKAGE_NOT_EXISTS, order, shopId);
                        tikTokLineOrderCompensations.add(tikTokLineOrderCompensation);
                        log.info("channel {} getLabel orderNo {} 创建包裹失败,错误信息:{},订单创建时间", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo(), tikTokCreatePackageResp.getMessage(),order.getCreateTime());
                        if (StrUtil.isEmpty(tikTokApiReturnMsg.getMsg())) {
                            tikTokApiReturnMsg.setErrorMsg("创建包裹失败");
                        }
                        continue;
                    }

                    tikTokApiReturnMsg.setErrorMsg(null);

                    //2.2.2 获取包裹信息
                    if (StrUtil.isBlank(lineItem.getTrackingNumber())) {

                        ConcurrentHashMap<String, PackageDetailsResp> packageDetails = tikTokShopApiUtil.getPackageDetails(Arrays.asList(packageId), shopId, tiktokAppKey, tiktokAppSecret);
                        if (CollUtil.isNotEmpty(packageDetails)) {
                            PackageDetailsResp packageDetailsResp = packageDetails.get(packageId);
                            Integer code = packageDetailsResp.getCode();
                            if (21001001 == code) {
                                //packageId发生变更，获取最新packageId

                                List<String> orderIdList = Arrays.asList(order.getTikTokOrderId());
                                List<TikTokOrder> tikTokOrders = tikTokShopApiUtil.getOrderDetail(orderIdList, shopId);
                                // tikTokOrders 中所有的 lineItems 都拿出来放入
                                Map<String, TikTokLineItem> lineMaps = processOrdersWithStreams(tikTokOrders);
                                packageId = lineMaps.get(lineItem.getId()).getPackageId();
                                if (CollectionUtil.isNotEmpty(tikTokOrders)) {


                                    ConcurrentHashMap<String, PackageDetailsResp> packageDetailsNew = tikTokShopApiUtil.getPackageDetails(Arrays.asList(packageId), shopId, tiktokAppKey, tiktokAppSecret );
                                    PackageDetailsResp detailsResp = packageDetailsNew.get(packageId);
                                    if (ObjectUtil.isNotEmpty(detailsResp)) {

                                        String trackingNumber = detailsResp.getTrackingNumber();
                                        String shippingProviderName = detailsResp.getShippingProviderName();

                                        tikTokExpressRelevant.setTrackingNumber(trackingNumber);
                                        tikTokExpressRelevant.setShipMethod(shippingProviderName);
                                    }
                                }
                            } else {

                                String trackingNumber = packageDetailsResp.getTrackingNumber();
                                String shippingProviderName = packageDetailsResp.getShippingProviderName();
                                tikTokExpressRelevant.setTrackingNumber(trackingNumber);
                                tikTokExpressRelevant.setShipMethod(shippingProviderName);
                            }
                        } else {
                            TikTokLineOrderCompensation tikTokLineOrderCompensation = getCompensation(tikTokApiReturnMsg.getOrderNo(),lineItem.getId(),null,null,order.getTikTokOrderId(),ZSMallStatusCodeEnum.TIKTOK_TRACKING_NO_NOT_EXISTS, order, shopId);
                            tikTokLineOrderCompensations.add(tikTokLineOrderCompensation);
                            log.error("channel {} getLabel orderNo {} 获取TrackingNo失败,订单创建时间:{}", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo(),order.getCreateTime());
                            log.info("包裹详情获取失败");
                            continue;
                        }
                    } else {
                        tikTokExpressRelevant.setTrackingNumber(lineItem.getTrackingNumber());
                        tikTokExpressRelevant.setShipMethod(lineItem.getShippingProviderName());
                    }

                    if (StringUtils.isBlank(tikTokExpressRelevant.getTrackingNumber())) {
                        TikTokLineOrderCompensation tikTokLineOrderCompensation = getCompensation(tikTokApiReturnMsg.getOrderNo(),lineItem.getId(),null,null,order.getTikTokOrderId(),ZSMallStatusCodeEnum.TIKTOK_TRACKING_NO_NOT_EXISTS, order, shopId);
                        tikTokLineOrderCompensations.add(tikTokLineOrderCompensation);
                        log.error("channel {} getLabel orderNo {} 获取TrackingNo失败,订单创建时间:{}", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo(),order.getCreateTime());
                        continue;
                    }

                    //2.2.3 获取面单URL
                    TikTokPackShippingDocument trackingUrl = tikTokShopApiUtil.getPackageShippingDocument(packageId, shopId,tiktokAppKey,tiktokAppSecret);
                    tikTokExpressRelevant.setFileUrl(trackingUrl.getData().getDocUrl());

                    if (ObjectUtil.isNull(trackingUrl)) {
                        TikTokLineOrderCompensation tikTokLineOrderCompensation = getCompensation(tikTokApiReturnMsg.getOrderNo(),lineItem.getId(),lineItem.getTrackingNumber(),packageId,order.getTikTokOrderId(),ZSMallStatusCodeEnum.TIKTOK_PACKAGE_URL_NOT_EXISTS, order, shopId);
                        tikTokLineOrderCompensations.add(tikTokLineOrderCompensation);
                        log.info("channel {} getLabel orderNo {} 获取面单失败,订单创建时间:{}", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo(),order.getCreateTime());
                        tikTokApiReturnMsg.setErrorMsg("获取面单失败");

                    } else {
                        // 上传oss
                        sysOssService.downloadPdfNotAsync(trackingUrl.getData().getDocUrl(),lineItem.getId(),tikTokExpressRelevant.getTrackingNumber());
//                        sysOssVo.setTrackingNumber(tikTokExpressRelevant.getTrackingNumber());
                        // 保存到redis内,key是lineItemId
                        // 将 lineItem的id 作为key , sysOssVo 作为value 存入redis,过期时间是15分钟,当第一次访问后直接删除
//                        RedissonClient client = RedisUtils.getClient();
//                        String key = "order:sys:oss:"+lineItem.getId();
//                        client.getBucket(key).set(sysOssVo, 15, TimeUnit.MINUTES);
                        log.info("oss上传成功");
                        ids.add(lineItem.getId());
                    }
                }


            }

        } catch (ApiException e) {
            // 自旋3次
            if (atomicInteger.get() <= 3) {
                atomicInteger.incrementAndGet();
                try {
                    TimeUnit.SECONDS.sleep(15);
                } catch (InterruptedException ee) {
                    log.error("channel {} getLabel orderNo {} 获取面单休眠异常：{}", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo(), e.getMessage());
                }
                log.info("channel {} getLabel orderNo {} 获取面单重试第 {} 次", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo(), atomicInteger.get());
                this.expressSheetApart(order, shopId,tiktokAppKey,tiktokAppSecret);
            } else {
                // 失败id记录落表
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("channel {} getLabel orderNo {} 获取面单失败: {}", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo(), e.getMessage());
        }
        //
        if(CollUtil.isNotEmpty(tikTokLineOrderCompensations)){
            tiktokLineOrderCompensationService.saveBatch(tikTokLineOrderCompensations);
        }
    }

    public static Map<String, TikTokLineItem> processOrdersWithStreams(List<TikTokOrder> tikTokOrders) {
        return tikTokOrders.stream() // 将List转换为Stream
                           .flatMap(order -> order.getLineItems().stream()) // 将每个order的lineItems展平为一个单独的Stream
                           .collect(Collectors.toMap(TikTokLineItem::getId, lineItem -> lineItem, (existing, replacement) -> existing)); // 收集到Map中，处理可能的键冲突（这里我们选择保留现有的值）
    }

    /**
     * 功能描述：获得补偿
     *
     * @param orderNo              订单号
     * @param lineItemId           行项目id
     * @param trackingNo           跟踪否
     * @param packageId            程序包id
     * @param orderId              订单id
     * @param zsMallStatusCodeEnum zs商城状态代码枚举
     * @param order
     * @param shopId
     * @return {@link TikTokLineOrderCompensation }
     * <AUTHOR>
     * @date 2024/06/04
     */
    public TikTokLineOrderCompensation getCompensation(String orderNo, String lineItemId, String trackingNo, String packageId, String orderId, ZSMallStatusCodeEnum zsMallStatusCodeEnum, TikTokOrder order,
                                                       String shopId) {
        LocaleMessage localeMessage = new LocaleMessage();
        TikTokLineOrderCompensation tikTokLineOrderCompensation = new TikTokLineOrderCompensation();
//        tikTokApiReturnMsg.getOrderNo()
        tikTokLineOrderCompensation.setChannelOrderNo(orderNo);
//
        tikTokLineOrderCompensation.setOrderItemId(Long.valueOf(lineItemId));
        tikTokLineOrderCompensation.setTrackingNo(trackingNo);
        tikTokLineOrderCompensation.setPackageId(packageId);
        tikTokLineOrderCompensation.setStatus(0);
//        tikTokApiReturnMsg.getOrderNo() order.getTikTokOrderId()
        tikTokLineOrderCompensation.setOrderId(orderId);
        tikTokLineOrderCompensation.setStatus(0);
        tikTokLineOrderCompensation.setChannelType(ChannelTypeEnum.TikTok.name());
        tikTokLineOrderCompensation.setShippingType(order.getShippingType());
        tikTokLineOrderCompensation.setSplitOrCombineTag(order.getSplitOrCombineTag());
        tikTokLineOrderCompensation.setOrderStatus(order.getStatus());
        tikTokLineOrderCompensation.setShopId(shopId);
        localeMessage.append(zsMallStatusCodeEnum.args(lineItemId));

        cn.hutool.json.JSONObject json = localeMessage.toJSON();
        tikTokLineOrderCompensation.setErrorTips(json);
        return tikTokLineOrderCompensation;
    }
    /**
     * 功能描述：获取tick-fire返回消息
     *
     * @param tikTokExpressSheet 取快递单
     * @return {@link TikTokApiReturnMsg }
     * <AUTHOR>
     * @date 2024/04/01
     */
    private TikTokApiReturnMsg getTikTokApiReturnMsg(TikTokExpressSheet tikTokExpressSheet) {
        TikTokApiReturnMsg tikTokApiReturnMsg = new TikTokApiReturnMsg();
        tikTokApiReturnMsg.setChannel(tikTokExpressSheet.getChannel())
                          .setOrderNo(tikTokExpressSheet.getOrderNo());
        return tikTokApiReturnMsg;
    }

    /**
     * 功能描述：获取快递单
     *
     * @param order 顺序
     * @return {@link TikTokExpressSheet }
     * <AUTHOR>
     * @date 2024/04/01
     */
    private TikTokExpressSheet getExpressSheet(TikTokOrder order) {
        TikTokExpressSheet tikTokExpressSheet = new TikTokExpressSheet();
        tikTokExpressSheet.setSplitOrCombineTag(order.getSplitOrCombineTag());
        String shippingType = order.getShippingType();
        if (TiktokLogisticsTypeEnum.TIKTOK.name().equals(shippingType)) {
            shippingType = LogisticsTypeEnum.PickUp.name();
        }
        if (TiktokLogisticsTypeEnum.SELLER.name().equals(shippingType)) {
            shippingType = LogisticsTypeEnum.DropShipping.name();
        }
        tikTokExpressSheet.setShippingType(shippingType);
        tikTokExpressSheet.setOrderStatus(order.getStatus());
        tikTokExpressSheet.setChannel(ChannelTypeEnum.TikTok.name());
        tikTokExpressSheet.setOrderNo(order.getTikTokOrderId());


        return tikTokExpressSheet;
    }

    @Override
    public TikTokRespBaseEntity getOrderListForTest(String msg,
                                                    Class<TikTokRespBaseEntity> targetClass) {

        TikTokRespBaseEntity tikTokRespBase = JSON.parseObject(msg, TikTokRespBaseEntity.class);
        Object data = tikTokRespBase.getData();
        TikTokSyncOrderSearchResp javaObject = JSONObject.toJavaObject((JSONObject) data, TikTokSyncOrderSearchResp.class);
        tikTokRespBase.setData(javaObject);

        return tikTokRespBase;
    }


    @Override
    public boolean isExists(TikTokRespBaseEntity targetData, String type) {

        return thirdBusinessFactory.getInvokeStrategy(TikTokBusinessHandlerEnums.getHandlerByTag(type))
                                   .isExists(targetData);
    }


}
