package com.zsmall.order.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.system.domain.vo.SysOssVo;
import com.hengjian.system.service.ISysTenantService;
import com.hengjian.system.service.impl.SysOssServiceImpl;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.OrderAttachmentDTO;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.common.CarrierTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.common.InstantMessagingAppType;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderRefund.RefundDisputeStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.ImagesUtil;
import com.zsmall.common.util.PDFUtil;
import com.zsmall.lottery.support.PriceBussinessV2Support;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.order.biz.service.DistributorOrderService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.anno.annotaion.OptAttachmentCheck;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.OrderItemNoListBo;
import com.zsmall.order.entity.domain.bo.OssReq;
import com.zsmall.order.entity.domain.bo.order.*;
import com.zsmall.order.entity.domain.bo.orderImport.TempOrderUpdateBo;
import com.zsmall.order.entity.domain.bo.refund.CancelRefundRequestBo;
import com.zsmall.order.entity.domain.bo.refund.PlatformInterventionBo;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.domain.vo.order.TrackingInfo;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationVo;
import com.zsmall.system.entity.iservice.IWorldLocationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/15 19:17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DistributorOrderServiceImpl implements DistributorOrderService {

    private final IOrderItemService iOrderItemService;
    private final IOrdersService iOrdersService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderAttachmentService iOrderAttachmentService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final BusinessParameterService businessParameterService;
    private final IWorldLocationService iWorldLocationService;
    private final IProductSkuService iProductSkuService;
    private final OrderSupport orderSupport;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IOrderRefundService iOrderRefundService;
    private final IOrderRefundItemService iOrderRefundItemService;
    private final PriceSupportV2 priceSupportV2;
    private final PriceBussinessV2Support priceBussinessSupport;
    private final BillSupport billSupport;
    private final ISysTenantService sysTenantService;

    private final SysOssServiceImpl sysOssService;
    /**
     * 手动确认收货
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> confirmReceiptOrder(OrderItemNoListBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);
        String tenantId = loginUser.getTenantId();

        List<String> orderItemNoList = bo.getOrderItemNoList();

        if (CollUtil.isEmpty(orderItemNoList)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        Set<Long> orderIds = new HashSet<>();
        Set<Long> orderItemIds = new HashSet<>();
        List<OrderItem> saveOrderItems = new ArrayList<>();
        //获取已发货、已支付的子订单
        List<OrderItem> orderItemList = iOrderItemService.getOrderItemListByShipped(orderItemNoList);
        if (CollUtil.isNotEmpty(orderItemList)) {
            for (OrderItem orderItem : orderItemList) {
                Long orderId = orderItem.getOrderId();
                Long orderItemId = orderItem.getId();
                orderItem.setFulfillmentProgress(LogisticsProgress.Fulfilled);
                orderItem.setFulfillmentTime(new Date());
                orderIds.add(orderId);
                orderItemIds.add(orderItemId);
                saveOrderItems.add(orderItem);
            }
            log.info("手动确认收货的子订单id为 : {}", JSONUtil.toJsonStr(orderItemIds));
            // 更新子订单的物流状态
            iOrderItemService.saveOrUpdateBatch(saveOrderItems);
            // 批量更新主订单的物流状态
            this.setOrderFulfillment(orderIds);

            // 记账
            billSupport.generateBillDTOByOrderItem(saveOrderItems, null);
        } else {
            return R.fail(ZSMallStatusCodeEnum.ORDER_CANNOT_UPDATE_NOT_BELONG_TO_ME_ERROR);
        }
        return R.ok();
    }

    /**
     * 改变订单详情
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<Void> changeOrderDetails(ChangeOrderDetailsBo bo) {
        String orderNo = bo.getOrderNo();
        String logisticsType = bo.getLogisticsType();
        Integer num = bo.getNum();
        String logisticsAccount = StrUtil.trim(bo.getLogisticsAccount());
        String logisticsServiceName = StrUtil.trim(bo.getLogisticsServiceName());
        List<TrackingInfo> trackingInfoList = bo.getTrackingInfoList();
        String carrier = bo.getCarrier();
        // 承运商转换Map，为了避免频繁操作，使用Redis存取，Redis超时时间6个小时 TODO
        /*Map<String, String> carrierMapRedis = globalCommonService.getCarrierSwitchMap();
        if (StringUtils.isNotBlank(carrier)) {
            if (!StringUtils.equalsAny(carrier, "FedEx", "UPS")) {
                String mapCarrier = carrierMapRedis.get(carrier);
                if (StringUtils.isNotBlank(mapCarrier)) {
                    carrier = mapCarrier;
                }
            }
        }*/

        //获取对应订单
        Orders orders = iOrdersService.getByOrderNo(orderNo);
        String tenantId = orders.getTenantId();
        //获取订单渠道
        ChannelTypeEnum productChannelType = orders.getChannelType();

        if (ObjectUtil.isNull(orders)) {
            return R.fail(ZSMallStatusCodeEnum.ORDERS_IS_NOT_EXIST);
        }

        //获取物流信息
        OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderId(orders.getId());

        //根据不同发货类型处理订单物流信息
        if (StrUtil.equals(logisticsType, LogisticsTypeEnum.PickUp.name())) {
            //自提需要填写物流信息
            orderLogisticsInfo.setLogisticsCompanyName(carrier);
            orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.PickUp);
            orderLogisticsInfo.setLogisticsAccount(StrUtil.blankToDefault(logisticsAccount, null));
            orderLogisticsInfo.setLogisticsServiceName(logisticsServiceName);

            // 若之前是代发，改成自提，需要清除快递标签
            if (LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())) {
                orderLogisticsInfo.setShippingLabelExist(false);
            }

            // 若之前是非第三方，改成第三方，需要清除快递标签
            if (StrUtil.isNotBlank(logisticsAccount)) {
                orderLogisticsInfo.setShippingLabelExist(false);
            } else {  // 反之，清除物流账户邮政编码
                orderLogisticsInfo.setLogisticsAccountZipCode(null);
            }

            orders.setLogisticsType(LogisticsTypeEnum.PickUp);
        } else if (StrUtil.equals(logisticsType, LogisticsTypeEnum.DropShipping.name())) {
            //代发需要清除物流数据和快递标签
            orderLogisticsInfo.setLogisticsAccount(null);
            orderLogisticsInfo.setLogisticsServiceName(null);
            orderLogisticsInfo.setLogisticsCompanyName(null);
            orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.DropShipping);
            orderLogisticsInfo.setLogisticsAccountZipCode(null);
            orderLogisticsInfo.setShippingLabelExist(false);
            orders.setLogisticsType(LogisticsTypeEnum.DropShipping);
        }
        //获取子订单
        List<OrderItem> orderItems = iOrderItemService.getListByOrderId(orders.getId());
        //获取邮编
        String zipCode = orderLogisticsInfo.getZipCode();

        //国家
        String country = orderLogisticsInfo.getLogisticsCountryCode();
        //物流账号
        String logisticsCompanyName = "";
        //确定是否存在第三方支持
        Boolean thirdCarrierSupport = null;
        if (ObjectUtil.isNotNull(orderLogisticsInfo)) {
            logisticsCompanyName = orderLogisticsInfo.getLogisticsCompanyName();
            if (StrUtil.isNotBlank(orderLogisticsInfo.getLogisticsAccount())) {
                thirdCarrierSupport = true;
            }
        }

        //是否拥有物流信息
        Boolean hasTracking = false;
        if (Objects.equals(productChannelType, ChannelTypeEnum.Others) && StringUtils.isNotBlank(logisticsCompanyName)) {
            hasTracking = true;
        }

        //主订单总数
        Integer totalNum = 0;
        List<OrderItem> orderItemList = new ArrayList<>();
        List<OrderItemPrice> orderItemPriceList = new ArrayList<>();
        String warehouseSystemCodeForProduct = null;
        //计算
        for (OrderItem orderItem : orderItems) {
            log.info("商品总价:{}", orderItem.getPlatformPayableTotalAmount());
            OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItem.getOrderItemNo());
            orderItemPriceList.add(orderItemPrice);
            OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
            String warehouseSystemCode = orderProductSku.getWarehouseSystemCode();
            String activityCode = orderProductSku.getActivityCode();
            String productSkuCode = orderProductSku.getProductSkuCode();
            String orderItemNo = orderItem.getOrderItemNo();

            //获取商品Sku
            ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
            if (ObjectUtil.isNull(productSku)) {
                return R.fail(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXIST);
            }

            orderItem.setTotalQuantity(num);
            orderItem.setLogisticsType(LogisticsTypeEnum.valueOf(logisticsType));
            OrderPriceCalculateDTO orderFeeCalculationDTO = new OrderPriceCalculateDTO();
            orderFeeCalculationDTO.setOrderItem(orderItem);
            orderFeeCalculationDTO.setCountry(country);
            orderFeeCalculationDTO.setChannelTypeEnum(productChannelType);
            orderFeeCalculationDTO.setLogisticsType(orderItem.getLogisticsType());
            orderFeeCalculationDTO.setWarehouseSystemCode(warehouseSystemCode);
            orderFeeCalculationDTO.setActivityCode(activityCode);
            //计算订单费用
            LocaleMessage localeMessage = priceSupportV2.calculationOrderItemPrice(orderFeeCalculationDTO, tenantId, zipCode, Collections.singletonList(warehouseSystemCode), orders, OrderFlowEnum.EDIT_ADDRESS_ORDER, carrier );
            String logisticsCarrierCode = null;
            String logisticsCode = null;
            if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                logisticsCarrierCode = orderFeeCalculationDTO.getLogisticsCarrierCode();
                logisticsCode = orderFeeCalculationDTO.getLogisticsCode();
                orderLogisticsInfo.setLogisticsCarrierCode(logisticsCarrierCode);
                orderLogisticsInfo.setLogisticsServiceName(logisticsCode);
                iOrderItemProductSkuService.updateOrSetNull(orders.getOrderNo(),orderFeeCalculationDTO.getWarehouseSystemCode(),orderFeeCalculationDTO.getWarehouseSystemCode() );
            }

//            LocaleMessage localeMessage = orderSupport.calculationOrderItemPrice(orderFeeCalculationDTO);
            if (localeMessage.hasData()) {
                return R.fail(localeMessage.toMessage());
            }

            log.info("商品总价:{}", orderItem.getPlatformPayableTotalAmount());
            totalNum += num;

            // 更新跟踪单数据
            iOrderItemTrackingRecordService.trackingListDisassociate(orderItemNo);
            List<OrderItemTrackingRecord> trackingList = new ArrayList<>();
            //只要自提并且非第三方物流需要填写跟踪单数据
            if (StrUtil.equals(logisticsType, LogisticsTypeEnum.PickUp.name()) && StrUtil.isBlank(logisticsAccount)) {
                if (StrUtil.isBlank(logisticsAccount)) {
                    for (TrackingInfo oneTracking : trackingInfoList) {
                        String trimTracking = StrUtil.trim(oneTracking.getTrackingNo());
                        OrderItemTrackingRecord trackingOrders = new OrderItemTrackingRecord();
                        trackingOrders.setSku(productSku.getSku());
                        trackingOrders.setLogisticsTrackingNo(trimTracking);
                        trackingOrders.setOrderNo(orderNo);
                        trackingOrders.setOrderItemNo(orderItemNo);
                        trackingOrders.setQuantity(orderItem.getTotalQuantity());
                        if(ObjectUtil.isNotEmpty(logisticsCarrierCode)){
                            trackingOrders.setLogisticsCarrier(logisticsCarrierCode);
                        }else {
                            trackingOrders.setLogisticsCarrier(carrier);
                        }

                        trackingOrders.setLogisticsService(logisticsServiceName);
                        trackingOrders.setDispatchedTime(new Date());
                        trackingOrders.setSystemManaged(true);
                        trackingOrders.setLogisticsService(orderLogisticsInfo.getLogisticsServiceName());
                        trackingOrders.setLogisticsProgress(LogisticsProgress.UnDispatched);
                        trackingList.add(trackingOrders);
                    }
                } else {
                    //第三方账号，没有物流单号和附件
                    iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.Zip);
                    iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
                }
            }
            //更新跟踪单数据
            if (CollUtil.isNotEmpty(trackingList)) {
                log.info("trackingList: {} ", JSONUtil.toJsonStr(trackingList));
                // 保存新的跟踪单
                iOrderItemTrackingRecordService.saveBatch(trackingList);
            }
            orderItemList.add(orderItem);
        }
        //重新计算主订单价格 更新的地方要注意,可能会存在setNull
        priceSupportV2.recalculateOrderAmount(orders, orderItemPriceList);
        orders.setTotalQuantity(totalNum);
        iOrdersService.updateById(orders);
        iOrderItemService.saveOrUpdateBatch(orderItemList);
        iOrderLogisticsInfoService.saveOrUpdate(orderLogisticsInfo);

        return R.ok();
    }

    /**
     * 取消订单
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> cancelOrder(OrderNoBo bo) {
        List<String> orderIds = bo.getOrderIds();

        if (CollUtil.isNotEmpty(orderIds)) {
            List<OrderStateType> orderStateTypes = new ArrayList<>();
            orderStateTypes.add(OrderStateType.UnPaid);
            orderStateTypes.add(OrderStateType.Failed);

            List<Orders> ordersList = iOrdersService.findByOrderNoInAndOrderStateIn(orderIds, orderStateTypes);

            log.info("cancelOrder - ordersList.size = {}", CollectionUtils.size(ordersList));

            for (Orders orders : ordersList) {
                orders.setOrderState(OrderStateType.Canceled);
            }
            iOrdersService.updateBatchById(ordersList);
        } else {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        return R.ok();
    }


    /**
     * 改变订单收件信息
     *
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<Void> changeOrderRecipientInfo(ChangeOrderRecipientInfoBo bo) {
        String orderNo = bo.getOrderNo();
        TempOrderUpdateBo.AddressInfo addressInfo = bo.getAddressInfo();

        String recipientName = addressInfo.getRecipientName();
        String phoneNumber = addressInfo.getPhoneNumber();
        String address1 = addressInfo.getAddress1();
        String address2 = addressInfo.getAddress2();
        String address3 = addressInfo.getAddress3();
        String city = addressInfo.getCity();
        Long countryId = addressInfo.getCountryId();
        Long stateId = addressInfo.getStateId();
        String stateCode = addressInfo.getState();

        String zipCode = addressInfo.getZipCode();

        if (StrUtil.isBlank(city) || ObjectUtil.isNull(countryId) || StrUtil.isBlank(phoneNumber)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        Orders orders = iOrdersService.getByOrderNo(orderNo);
        Boolean isPostCodeChange=false;
        OrderAddressInfo shipAddress = iOrderAddressInfoService.getByOrderNoAndAddressType(orderNo, OrderAddressType.ShipAddress);
        if (shipAddress == null) {
            shipAddress = new OrderAddressInfo();
            shipAddress.setOrderId(orders.getId());
            shipAddress.setOrderNo(orders.getOrderNo());
        }
        if (orders.getLogisticsType().equals(LogisticsTypeEnum.DropShipping)){
            if (ObjectUtil.isNotNull(shipAddress.getZipCode()) && !ObjectUtil.equal(shipAddress.getZipCode(),zipCode)){
                    //算价
                isPostCodeChange=true;
            }
        }

        WorldLocationVo country = iWorldLocationService.queryById(countryId);
        String countryCode = country.getLocationCode();
        if (!StrUtil.equals(countryCode, "US")) {
            String parameterSupport = businessParameterService.getValueFromString(BusinessParameterType.SUPPORT_COUNTRY);
            JSONArray SUPPORT_COUNTRY = JSONUtil.parseArray(parameterSupport);
            if (!SUPPORT_COUNTRY.contains(countryCode)) {
                return R.fail(ZSMallStatusCodeEnum.UNSUPPORTED_COUNTRIES);
            }

            shipAddress.setCountry(country.getLocationName());
            shipAddress.setCountryCode(country.getLocationCode());
        } else {
            // 美国地区才需要判断是否填写了邮编
            if (StrUtil.isBlank(zipCode)) {
                return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
            }
            shipAddress.setCountry(country.getLocationName());
            shipAddress.setCountryCode(country.getLocationCode());
        }

        if (StrUtil.isBlank(stateCode)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        } else {
            if (ObjectUtil.isNotNull(stateId)) {
                WorldLocationVo worldLocation = iWorldLocationService.queryById(stateId);
                if (worldLocation == null) {
                    return R.fail(ZSMallStatusCodeEnum.STATE_NOT_EXIST);
                }
                shipAddress.setState(worldLocation.getLocationName());
                shipAddress.setStateCode(worldLocation.getLocationCode());
            }else {
                shipAddress.setState(stateCode);
                shipAddress.setStateCode(stateCode);
            }
        }

        shipAddress.setZipCode(zipCode);
        shipAddress.setCity(city);
        shipAddress.setAddress1(address1);
        shipAddress.setAddress2(address2);
        shipAddress.setAddress3(address3);
        shipAddress.setPhoneNumber(phoneNumber);
        shipAddress.setRecipient(recipientName);

        iOrderAddressInfoService.saveOrUpdate(shipAddress);
        OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderId(orders.getId());
        if (orderLogisticsInfo != null) {
            orderLogisticsInfo.setZipCode(zipCode);
            iOrderLogisticsInfoService.updateById(orderLogisticsInfo);
        }
        // 如果用户开启了测算,又改变了邮编,才进行订单的重新计算
        Boolean approvedTenant = sysTenantService.getIsApprovedTenant(LoginHelper.getTenantId(), 1);
        if(approvedTenant){
            if (isPostCodeChange){
                ConcurrentHashMap<String, List<Object>> businessMap = new ConcurrentHashMap<>();
                if(OrderExceptionEnum.product_mapping_exception.getValue().equals(orders.getExceptionCode())){

                }else{
                    priceBussinessSupport.recalculateAndUpdateForPrice(orders,businessMap);
                }

            }
        }


        // 检查是否有需要重新创建的恒健仓库发货单 TODO
        //theBizarkLogisticsSupport.recreateBizarkOrder(orders, "addressException");
        return R.ok();
    }

    /**
     * 改变渠道订单号
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> changeChannelOrderId(ChangeChannelOrderBo bo) {
        String orderNo = bo.getOrderNo();
        String channelOrderId = bo.getChannelOrderId();

        Orders orders = iOrdersService.getByOrderNo(orderNo);
        if (ObjectUtil.isNull(orders)) {
            return R.fail(ZSMallStatusCodeEnum.ORDERS_IS_NOT_EXIST);
        }
        if (StrUtil.isNotBlank(channelOrderId)) {
            orders.setChannelOrderName(channelOrderId);
            orders.setChannelOrderNo(channelOrderId);
        }
        iOrdersService.updateById(orders);
        return R.ok();
    }

    /**
     * 改变店铺链接
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> changeStoreLink(ChangeStoreLinkBo bo) {
        String orderNo = bo.getOrderNo();
        String channelAlias = bo.getChannelAlias();

        Orders orders = iOrdersService.getByOrderNo(orderNo);
        if (ObjectUtil.isNull(orders)) {
            return R.fail(ZSMallStatusCodeEnum.ORDERS_IS_NOT_EXIST);
        }
        if (StrUtil.isNotBlank(channelAlias)) {
            orders.setChannelAlias(channelAlias);
        }
        iOrdersService.updateById(orders);
        return R.ok();
    }


    /**
     * 取消售后申请
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<Void> cancelRefundRequest(CancelRefundRequestBo bo) {
        String orderRefundNo = bo.getOrderRefundNo();
        String orderRefundItemNo = bo.getOrderRefundItemNo();
        if (StrUtil.isAllBlank(orderRefundNo, orderRefundItemNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        // 主单操作
        if (StrUtil.isNotBlank(orderRefundNo)) {
            OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundNo);
            if (orderRefund != null) {
                OrderRefundStateType refundState = orderRefund.getRefundState();
                Orders order = iOrdersService.getByOrderNo(orderRefund.getOrderNo());
//                OrderType orderType = order.getOrderType();

//                if (ObjectUtil.notEqual(orderType, OrderType.Wholesale)) {
//                    return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
//                }

                orderRefund.setRefundState(OrderRefundStateType.Canceled);
                List<OrderRefundItem> orderRefundItemList = iOrderRefundItemService.queryByOrderRefundNo(orderRefundNo);
                List<OrderItem> orderItems = new ArrayList<>();
                for (OrderRefundItem orderRefundItem : orderRefundItemList) {
                    if (ObjectUtil.equals(refundState, OrderRefundStateType.Verifying)
                        || ObjectUtil.equals(refundState, OrderRefundStateType.ManagerVerifying)) {
                        OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderRefundItem.getOrderItemNo());
                        refundOrderStatus(order, orderItem, OrderStateType.Paid);
                        orderItems.add(orderItem);
                    } else {
                        return R.fail(ZSMallStatusCodeEnum.CANNOT_CANCEL_THE_REFUND_APPLICATION);
                    }
                }


                BigDecimal platformRefundAmount = orderRefund.getPlatformRefundAmount();
                BigDecimal originalRefundAmount = orderRefund.getOriginalRefundAmount();

                BigDecimal platformRefundExecutableAmount = order.getPlatformRefundExecutableAmount();
                BigDecimal originalRefundExecutableAmount = order.getOriginalRefundExecutableAmount();
                log.info("取消售后申请（主单模式） platformRefundAmount = {} originalRefundAmount = {}", platformRefundAmount, originalRefundAmount);
                log.info("取消售后申请（主单模式） platformRefundExecutableAmount = {} originalRefundExecutableAmount = {}", platformRefundExecutableAmount, originalRefundExecutableAmount);

                BigDecimal newRefundExecutableAmount = NumberUtil.add(platformRefundExecutableAmount, platformRefundAmount);
                BigDecimal newRefundExecutableAmountSup = NumberUtil.add(originalRefundExecutableAmount, originalRefundAmount);
                log.info("取消售后申请（主单模式） newRefundExecutableAmount = {} newRefundExecutableAmountSup = {}", newRefundExecutableAmount, newRefundExecutableAmountSup);

                order.setPlatformRefundExecutableAmount(newRefundExecutableAmount);
                order.setOriginalRefundExecutableAmount(newRefundExecutableAmountSup);

                iOrdersService.saveOrUpdate(order);
                iOrderItemService.saveOrUpdateBatch(orderItems);
                iOrderRefundService.saveOrUpdate(orderRefund);
            } else {
                return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
            }
        } else {  // 子单操作
            OrderRefundItem orderRefundItem = iOrderRefundItemService.getByOrderRefundItemNo(orderRefundItemNo);
            if (orderRefundItem != null) {
                OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundItem.getOrderRefundNo());
                OrderRefundStateType refundState = orderRefund.getRefundState();
                if (ObjectUtil.equals(refundState, OrderRefundStateType.Verifying)
                    || ObjectUtil.equals(refundState, OrderRefundStateType.ManagerVerifying)) {

                    OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderRefundItem.getOrderItemNo());
                    Orders order = iOrdersService.queryById(orderItem.getOrderId());
                    refundOrderStatus(order, orderItem, OrderStateType.Paid);

                    BigDecimal platformRefundAmount = orderRefund.getPlatformRefundAmount();
                    BigDecimal originalRefundAmount = orderRefund.getOriginalRefundAmount();

                    BigDecimal platformRefundExecutableAmount = orderItem.getPlatformRefundExecutableAmount();
                    BigDecimal originalRefundExecutableAmount = orderItem.getOriginalRefundExecutableAmount();
                    log.info("取消售后申请 platformRefundAmount = {} originalRefundAmount = {}", platformRefundAmount, originalRefundAmount);
                    log.info("取消售后申请 platformRefundExecutableAmount = {} originalRefundExecutableAmount = {}", platformRefundExecutableAmount, originalRefundExecutableAmount);

                    BigDecimal newRefundExecutableAmount = NumberUtil.add(platformRefundExecutableAmount, platformRefundAmount);
                    BigDecimal newRefundExecutableAmountSup = NumberUtil.add(originalRefundExecutableAmount, originalRefundAmount);
                    log.info("取消售后申请 newRefundExecutableAmount = {} newRefundExecutableAmountSup = {}", newRefundExecutableAmount, newRefundExecutableAmountSup);
                    orderItem.setPlatformRefundExecutableAmount(newRefundExecutableAmount);
                    orderItem.setOriginalRefundExecutableAmount(newRefundExecutableAmountSup);
                    orderRefund.setRefundState(OrderRefundStateType.Canceled);

                    iOrdersService.saveOrUpdate(order);
                    iOrderItemService.saveOrUpdate(orderItem);
                    iOrderRefundService.saveOrUpdate(orderRefund);
                } else {
                    return R.fail(ZSMallStatusCodeEnum.CANNOT_CANCEL_THE_REFUND_APPLICATION);
                }
            } else {
                return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
            }
        }

        return R.ok();
    }

    /**
     * 申请平台介入
     * @param bo
     * @return
     */
    @Override
    public R<Void> platformIntervention(PlatformInterventionBo bo) {
        String orderRefundNo = bo.getOrderRefundNo();

        OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundNo);
        if (orderRefund == null) {
            return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
        }else {
            OrderRefundStateType refundState = orderRefund.getRefundState();
            if (ObjectUtil.equals(refundState, OrderRefundStateType.Reject)) {
                String messagingAppId = bo.getMessagingAppId();
                String messagingAppType = bo.getMessagingAppType();
                if (StrUtil.hasBlank(messagingAppId, messagingAppType)) {
                    return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
                }

                InstantMessagingAppType instantMessagingAppType = InstantMessagingAppType.valueOf(messagingAppType);

                orderRefund.setMessagingAppId(messagingAppId);
                orderRefund.setMessagingAppType(instantMessagingAppType);
                orderRefund.setRefundDisputeState(RefundDisputeStateEnum.NotProcessed);
            } else {
                return R.fail(ZSMallStatusCodeEnum.CURRENT_REFUND_CANNOT_BE_PROCESSED);
            }
            iOrderRefundService.saveByNoTenant(orderRefund);
        }
        return R.ok();

    }

    /**
     * 处理平台介入
     * @param bo
     * @return
     */
    @Override
    public R<Void> handlePlatformIntervention(PlatformInterventionBo bo) {
        String orderRefundNo = bo.getOrderRefundNo();

        if (StrUtil.hasBlank(orderRefundNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundNo);
        if (orderRefund == null) {
            return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
        }
        RefundDisputeStateEnum refundDisputeState = orderRefund.getRefundDisputeState();
        OrderRefundStateType refundState = orderRefund.getRefundState();
        if (ObjectUtil.equals(refundState, OrderRefundStateType.Reject)
            && ObjectUtil.equals(refundDisputeState, RefundDisputeStateEnum.NotProcessed)) {
            orderRefund.setRefundDisputeState(RefundDisputeStateEnum.Processed);
        }else {
            return R.fail(ZSMallStatusCodeEnum.CURRENT_REFUND_CANNOT_BE_PROCESSED);
        }
        iOrderRefundService.saveByNoTenant(orderRefund);

        return R.ok();
    }

    @InMethodLog(value = "上传快递标签")
    @Override
    @OptAttachmentCheck
    public R<Void> uploadShippingLabel(OrderUpdateBo bo) {
        String orderNo = bo.getOrderNo();
        Orders order = iOrdersService.getByOrderNo(orderNo);
        if (order == null) {
            return R.fail(ZSMallStatusCodeEnum.ORDER_NOT_EXIST_OR_REVIEW);
        }
        OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);
        if (ObjectUtil.isNull(orderLogisticsInfo)){
            return R.fail("订单物流信息对象为空"+orderNo);
        }
        if (ObjectUtil.isNull(bo.getFileTypeEnum())){
            return R.fail("错误的请求参数,订单附件类型不能为空");
        }

        boolean isUpload = bo.isUpload();
        MultipartFile multipartFile;
        SysOssVo sysOssVo =null;
        if(!isUpload){
            multipartFile= bo.getMultipartFile();
            if (multipartFile == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }
            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            sysOssVo = ossUploadEvent.getSysOssVo();
        }else{
            sysOssVo = bo.getSysOssVo();
        }

        String originalName = sysOssVo.getOriginalName();
        String suffix = FileUtil.getSuffix(originalName);
        OrderAttachment newOrderAttachment = new OrderAttachment();
        newOrderAttachment.setOssId(sysOssVo.getOssId());
        newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
        newOrderAttachment.setAttachmentOriginalName(originalName);
        newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
        newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
        newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
        newOrderAttachment.setAttachmentType(bo.getFileTypeEnum());


        if (ObjectUtil.isNotNull(orderLogisticsInfo) && CarrierTypeEnum.LTL.name().equals(orderLogisticsInfo.getLogisticsCarrierCode())) {
            // 查询相关订单列表
            LambdaQueryWrapper<Orders> q = new LambdaQueryWrapper<>();
            q.eq(Orders::getOrderExtendId, order.getOrderExtendId());
            List<Orders> ordersList = iOrdersService.getBaseMapper().selectList(q);
            Set<String> orderNos = ordersList.stream()
                                         .map(Orders::getOrderNo)
                                         .collect(Collectors.toSet());
            // 遍历订单列表，进行相关操作
            orderNos.forEach(s -> {
                //如果是BOL类型的附件,则删除之前的BOL类型附件,ShippingLabel 无限追加
                if (ObjectUtil.equals(bo.getFileTypeEnum(),OrderAttachmentTypeEnum.BOL)){
                    iOrderAttachmentService.deleteByOrderNoAndAttachmentType(s, OrderAttachmentTypeEnum.BOL);
                }

                newOrderAttachment.setId(IdUtil.getSnowflakeNextId());
                newOrderAttachment.setOrderNo(s);
                iOrderAttachmentService.save(newOrderAttachment);
                //查询所有的OrderLogistics
                OrderLogisticsInfo orderLogisticsInfos = iOrderLogisticsInfoService.getByOrderNo(s);
                orderLogisticsInfos.setShippingLabelExist(true);
                iOrderLogisticsInfoService.updateById(orderLogisticsInfos);
            });
        }else {
            if (ObjectUtil.equals(bo.getFileTypeEnum(), OrderAttachmentTypeEnum.BOL)) {
                iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.BOL);
            }
            newOrderAttachment.setOrderNo(bo.getOrderNo());
            iOrderAttachmentService.save(newOrderAttachment);

            orderLogisticsInfo.setShippingLabelExist(true);
            iOrderLogisticsInfoService.updateById(orderLogisticsInfo);
        }
        // 上传shippingLabel订单异常状态判断，订单异常状态处理如果为获取面单异常，则修改为无异常
        if (ObjectUtil.equals(bo.getFileTypeEnum(),OrderAttachmentTypeEnum.ShippingLabel) && null != order.getExceptionCode() && order.getExceptionCode().equals(OrderExceptionEnum.logistics_attachment_exception.getValue())){
            order.setExceptionCode(OrderExceptionEnum.normal.getValue());
            iOrdersService.updateById(order);
        }
        return R.ok();
    }

    @InMethodLog(value = "上传快递标签")
    @Override
    public R<Void> uploadShippingLabels(OrderUpdateBo bo) {
        String orderNo = bo.getOrderNo();
        Orders order = iOrdersService.getByOrderNo(orderNo);
        if (order == null) {
            return R.fail(ZSMallStatusCodeEnum.ORDER_NOT_EXIST_OR_REVIEW);
        }
        OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);
        boolean isUpload = bo.isUpload();
        MultipartFile multipartFile;
        SysOssVo sysOssVo =null;
        if(!isUpload){
            multipartFile= bo.getMultipartFile();
            if (multipartFile == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }
            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            sysOssVo = ossUploadEvent.getSysOssVo();
        }else{
            sysOssVo = bo.getSysOssVo();
        }

        String originalName = sysOssVo.getOriginalName();
        String suffix = FileUtil.getSuffix(originalName);

        OrderAttachment newOrderAttachment = new OrderAttachment();
        newOrderAttachment.setOssId(sysOssVo.getOssId());
        newOrderAttachment.setOrderNo(orderNo);
        newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
        newOrderAttachment.setAttachmentOriginalName(originalName);
        newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
        newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
        newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
        newOrderAttachment.setAttachmentType(OrderAttachmentTypeEnum.ShippingLabel);

//        iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
        iOrderAttachmentService.save(newOrderAttachment);

        orderLogisticsInfo.setShippingLabelExist(true);
        iOrderLogisticsInfoService.updateById(orderLogisticsInfo);
        return R.ok();
    }

    @InMethodLog(value = "上传订单附件")
    @Override
    public R<Void> uploadOrderAttachment(OrderUpdateBo bo) {

        String orderNo = bo.getOrderNo();
        Orders orders = TenantHelper.ignore(() -> iOrdersService.getByOrderNo(orderNo));
        if (ObjectUtil.isNull(orders)){
            return R.fail("订单号不存在："+orderNo);
        }

        MultipartFile multipartFile = bo.getMultipartFile();
        if (multipartFile == null) {
            return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
        }

        OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
        SpringUtils.context().publishEvent(ossUploadEvent);
        SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
        String originalName = sysOssVo.getOriginalName();
        String suffix = FileUtil.getSuffix(originalName);

        OrderAttachment newOrderAttachment = new OrderAttachment();
        newOrderAttachment.setOssId(sysOssVo.getOssId());
        newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
        newOrderAttachment.setAttachmentOriginalName(originalName);
        newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
        newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
        newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
        newOrderAttachment.setAttachmentType(OrderAttachmentTypeEnum.Zip);

        //查询订单的类型是不是LTL
        OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);
        if (ObjectUtil.isNull(logisticsInfo)){
            return R.fail("订单物流信息对象为空"+orderNo);
        }
        if (ObjectUtil.isNotNull(logisticsInfo) && CarrierTypeEnum.LTL.name().equals(logisticsInfo.getLogisticsCarrierCode())) {
            // 查询相关订单列表
            LambdaQueryWrapper<Orders> q = new LambdaQueryWrapper<>();
            q.eq(Orders::getOrderExtendId, orders.getOrderExtendId());
            List<Orders> ordersList = iOrdersService.getBaseMapper().selectList(q);
            Set<String> orderNos = ordersList.stream()
                                             .map(Orders::getOrderNo)
                                             .collect(Collectors.toSet());
            // 遍历订单列表，进行相关操作
            orderNos.forEach(s -> {
                if (ObjectUtil.equals(bo.getFileTypeEnum(),OrderAttachmentTypeEnum.BOL)){
                    iOrderAttachmentService.deleteByOrderNoAndAttachmentType(s, OrderAttachmentTypeEnum.Zip);
                }
                newOrderAttachment.setId(IdUtil.getSnowflakeNextId());
                newOrderAttachment.setOrderNo(s);
                iOrderAttachmentService.save(newOrderAttachment);
            });
        } else {
            // 物流信息为空或物流承运商代码不为LTL，处理单个订单
            if (ObjectUtil.equals(bo.getFileTypeEnum(),OrderAttachmentTypeEnum.BOL)){
                iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.Zip);
            }
            newOrderAttachment.setOrderNo(orderNo);
            iOrderAttachmentService.save(newOrderAttachment);
        }
        return R.ok();
    }

    @Override
    public Boolean amazonVCOrderCheck(String orderNo) {
        OrderAttachment orderAttachment = iOrderAttachmentService.getByOrderNo(orderNo);
        if(null == orderAttachment){
            Orders orders = iOrdersService.getByOrderNo(orderNo);
            if(null != orders){
                if (LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType()) && ChannelTypeEnum.Amazon_VC.equals(orders.getChannelType())) {
                    if(null != orders.getExceptionCode() && orders.getExceptionCode() == OrderExceptionEnum.logistics_attachment_exception.getValue()){
                        return true;
                    }
                    if (null != orders.getExceptionCode() && orders.getExceptionCode() == OrderExceptionEnum.normal.getValue()){
                        return false;
                    }
                }
            }
        }else {
            return true;
        }
        return false;
    }

    @Override
    public R<Void> deleteAttachment(OssReq req) {
        // 删除单个-LTL的要考虑删除所有的相关的附件
        String orderNo = req.getOrderNo();
        List<Long> attachmentIds = req.getAttachmentIds();
        Orders orders = TenantHelper.ignore(() -> iOrdersService.getByOrderNo(orderNo));
        LambdaQueryWrapper<OrderItemTrackingRecord> queryWrapper = new LambdaQueryWrapper<OrderItemTrackingRecord>().eq(OrderItemTrackingRecord::getOrderNo, orders.getOrderNo()).last("limit 1");
        OrderItemTrackingRecord trackingRecord = TenantHelper.ignore(() -> iOrderItemTrackingRecordService.getOne(queryWrapper));
        LambdaQueryWrapper<OrderAttachment> orderAttachmentLambdaQueryWrapper = new LambdaQueryWrapper<OrderAttachment>().in(OrderAttachment::getId, attachmentIds)
                                                                                                                         .eq(OrderAttachment::getDelFlag, 0);

        List<OrderAttachment> orderAttachments = iOrderAttachmentService.list(orderAttachmentLambdaQueryWrapper);
        if(CollUtil.isEmpty(orderAttachments)){
            return null;
        }
        List<Long> ossIds = orderAttachments.stream().map(OrderAttachment::getOssId)
                                            .collect(Collectors.toList());
        if(ObjectUtil.isNotEmpty(orders)){
            String orderExtendId = orders.getOrderExtendId();
            if (ObjectUtil.isNotEmpty(orderExtendId)&&!orderNo.equals(orderExtendId)&&(ObjectUtil.isNotEmpty(trackingRecord)&& CarrierTypeEnum.LTL.getValue().equals(trackingRecord.getLogisticsCarrier()))){
                // 删除对应的order的附件
                LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<Orders>().eq(Orders::getOrderExtendId, orderExtendId);

                //

                List<Orders> list = iOrdersService.list(wrapper);
                List<String> orderNos = list.stream().map(Orders::getOrderNo).collect(Collectors.toList());
                LambdaUpdateWrapper<OrderAttachment> updateWrapper = new LambdaUpdateWrapper<OrderAttachment>().in(OrderAttachment::getOrderNo, orderNos)
                                                                                                               .in(OrderAttachment::getOssId, ossIds)
                                                                                                               .set(OrderAttachment::getDelFlag, 2);
                iOrderAttachmentService.update(updateWrapper);
            }else {
                LambdaUpdateWrapper<OrderAttachment> updateWrapper = new LambdaUpdateWrapper<OrderAttachment>().eq(OrderAttachment::getOrderNo, orderNo)
                                                                                                               .in(OrderAttachment::getId, attachmentIds)
                                                                                                               .set(OrderAttachment::getDelFlag, 2);
                iOrderAttachmentService.update(updateWrapper);
            }
        }
        return null;
    }



    @Override
    public Set<Long> updateOrderAttachmentIncludeAll(String orderExtendId, List<OrderAttachmentDTO> attachments) {

        List<Orders> orders = iOrdersService.getByOrderExtendId(orderExtendId);
        // 取第一个是因为,更新是更新所有关联的子订单
        Orders order = orders.get(0);
        Set<Long> saveOssIds = new HashSet<>();
        OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(order.getOrderNo());
//        sysOssVo = sysOssService.downloadPdfNotAsyncForOpen(attachInfo.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());
        for (OrderAttachmentDTO attachment : attachments) {
            String trackingNo = attachment.getTrackingNo();
            SysOssVo sysOssVo = null;
            String url = attachment.getUrl();
            Integer fileType = attachment.getFileType();
            sysOssVo = sysOssService.downloadPdfNotAsync(url, String.valueOf(order.getId()), trackingNo);
            String disUrl = sysOssVo.getUrl();
            attachment.setUrl(disUrl);

            String originalName = sysOssVo.getOriginalName();
            String suffix = FileUtil.getSuffix(originalName);
            OrderAttachment newOrderAttachment = new OrderAttachment();
            newOrderAttachment.setOssId(sysOssVo.getOssId());
            newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
            newOrderAttachment.setAttachmentOriginalName(originalName);
            newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
            newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
            newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
            OrderAttachmentTypeEnum typeEnum = OrderAttachmentTypeEnum.getByCode(fileType);
            newOrderAttachment.setAttachmentType(typeEnum);


            if (ObjectUtil.isNotNull(orderLogisticsInfo) && CarrierTypeEnum.LTL.name().equals(orderLogisticsInfo.getLogisticsCarrierCode())) {
                // 查询相关订单列表
                LambdaQueryWrapper<Orders> q = new LambdaQueryWrapper<>();
                q.eq(Orders::getOrderExtendId, orderExtendId);
                List<Orders> ordersList = iOrdersService.getBaseMapper().selectList(q);
                Set<String> orderNos = ordersList.stream()
                                                 .map(Orders::getOrderNo)
                                                 .collect(Collectors.toSet());
                // 遍历订单列表，进行相关操作
                orderNos.forEach(s -> {
                    //如果是BOL类型的附件,则删除之前的BOL类型附件,ShippingLabel 如果是LTL无限追加,非LTL需要校验附件页数和商品数量是否一致
                    if (ObjectUtil.equals(fileType,OrderAttachmentTypeEnum.BOL.getCode())){
                        // 删除失效
                        iOrderAttachmentService.deleteByOrderNoAndAttachmentType(s, OrderAttachmentTypeEnum.BOL);
                    }

                    if (ObjectUtil.equals(fileType, OrderAttachmentTypeEnum.ShippingLabel.getCode())) {
                        iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderExtendId, OrderAttachmentTypeEnum.ShippingLabel);
                    }

                    newOrderAttachment.setId(IdUtil.getSnowflakeNextId());
                    newOrderAttachment.setOrderNo(s);
                    iOrderAttachmentService.save(newOrderAttachment);
                    //查询所有的OrderLogistics
                    OrderLogisticsInfo orderLogisticsInfos = iOrderLogisticsInfoService.getByOrderNo(s);
                    orderLogisticsInfos.setShippingLabelExist(true);
                    iOrderLogisticsInfoService.updateById(orderLogisticsInfos);
                });
            }else {
                // 非LTL单,orderExtendId和orderNo是同一个 非LTL需要校验shippingLabel附件页数和商品数量是否一致
                if (ObjectUtil.equals(fileType, OrderAttachmentTypeEnum.BOL.getCode())) {
                    iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderExtendId, OrderAttachmentTypeEnum.BOL);
                }
                if (ObjectUtil.equals(fileType, OrderAttachmentTypeEnum.ShippingLabel.getCode())) {
                    iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderExtendId, OrderAttachmentTypeEnum.ShippingLabel);
                }
                Integer pageSize = 0;
                if(OrderAttachmentTypeEnum.ShippingLabel.getCode().equals(fileType)){
                    // todo 线上放开
                    String pdfUrl = ImagesUtil.downloadPdf(url);
                    InputStream shippingLabel ;
                    try {
                        shippingLabel = ImagesUtil.downloadPdfAsStream(pdfUrl);
                    } catch (Exception e) {
                        throw new RuntimeException("File download failure!");
                    }

                    List<String> base64List = null;
                    try {
                        base64List = PDFUtil.splitPdfToBase64(shippingLabel, 1);
                        pageSize = base64List.size();
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to split the PDF file!");
                    }
                    if(ObjectUtil.isEmpty(order)){
                        throw new RuntimeException("订单号不存在");
                    }
                    if(!order.getTotalQuantity().equals(pageSize)){
                        throw new RuntimeException("附件页数需要与订单商品数量一致");
                    }
                }

                newOrderAttachment.setOrderNo(orderExtendId);
                iOrderAttachmentService.save(newOrderAttachment);
                saveOssIds.add(newOrderAttachment.getId());
                orderLogisticsInfo.setShippingLabelExist(true);
                iOrderLogisticsInfoService.updateById(orderLogisticsInfo);
            }
            // 上传shippingLabel订单异常状态判断，订单异常状态处理如果为获取面单异常，则修改为无异常
            if (ObjectUtil.equals(fileType,OrderAttachmentTypeEnum.ShippingLabel) && null != order.getExceptionCode() && order.getExceptionCode().equals(OrderExceptionEnum.logistics_attachment_exception.getValue())){
                order.setExceptionCode(OrderExceptionEnum.normal.getValue());
                iOrdersService.updateById(order);
            }
        }


        return saveOssIds;
    }

    @Override
    public void uploadShippingLabelNotBatch(OrderUpdateBo bo) {

        String orderNo = bo.getOrderNo();
        Orders order = iOrdersService.getByOrderNo(orderNo);
        if (order == null) {
            throw new RuntimeException(ZSMallStatusCodeEnum.ORDER_NOT_EXIST_OR_REVIEW.getMessage());

        }
        OrderAttachmentTypeEnum fileTypeEnum = bo.getFileTypeEnum();
        OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);
        boolean isUpload = bo.isUpload();
        MultipartFile multipartFile;
        SysOssVo sysOssVo =null;
        if(!isUpload){
            multipartFile= bo.getMultipartFile();
            if (multipartFile == null) {
                throw new RuntimeException(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY.getMessage());
            }
            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            sysOssVo = ossUploadEvent.getSysOssVo();
        }else{
            sysOssVo = bo.getSysOssVo();
        }

        String originalName = sysOssVo.getOriginalName();
        String suffix = FileUtil.getSuffix(originalName);

        OrderAttachment newOrderAttachment = new OrderAttachment();
        newOrderAttachment.setOssId(sysOssVo.getOssId());
        newOrderAttachment.setOrderNo(orderNo);
        newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
        newOrderAttachment.setAttachmentOriginalName(originalName);
        newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
        newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
        newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
        newOrderAttachment.setAttachmentType(fileTypeEnum);

//        iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
        iOrderAttachmentService.save(newOrderAttachment);

        orderLogisticsInfo.setShippingLabelExist(true);
        iOrderLogisticsInfoService.updateById(orderLogisticsInfo);

    }
    /**
     * 退款单关联的订单和子订单状态调整
     *
     * @param orders
     * @param orderItem
     */
    private void refundOrderStatus(Orders orders, OrderItem orderItem, OrderStateType orderStateType) {
        OrderStateType orderStatus = orders.getOrderState();
        OrderStateType orderItemStatus = orderItem.getOrderState();

        if (Objects.equals(orderStatus, OrderStateType.Verifying)) {
            orders.setOrderState(orderStateType);
        }

        if (Objects.equals(orderItemStatus, OrderStateType.Verifying)) {
            orderItem.setOrderState(orderStateType);
        }
    }

    /**
     * 批量更新主订单的物流状态
     *
     * @param orderIds
     */
    public void setOrderFulfillment(Set<Long> orderIds) {
        log.info("setOrderFulfillment orderIds = {}", orderIds);
        List<Orders> orders = iOrdersService.queryListByIds(orderIds);
        if (CollectionUtils.isNotEmpty(orders)) {
            for (Orders order : orders) {
                String orderNo = order.getOrderNo();
                // 处理复合状态
                Set<LogisticsProgress> logisticsProgresses = iOrderItemService.queryFulfillmentTypesByOrderId(order.getId());
                log.info("setOrderFulfillment - orderNo = {}, logisticsProgresses = {}", orderNo, logisticsProgresses);
                LogisticsProgress originFulfillment = order.getFulfillmentProgress();
                order.setFulfillmentProgress(LogisticsProgress.getComplexType(logisticsProgresses));

                // 现货订单若在此完成，需要计入账单（NotEqual是为了判断主订单是否是从其他状态变成Fulfilled的） TODO
                if (ObjectUtil.equals(order.getOrderType(), OrderType.Wholesale)
                    && ObjectUtil.equals(order.getFulfillmentProgress(), LogisticsProgress.Fulfilled)
                    && ObjectUtil.notEqual(order.getFulfillmentProgress(), originFulfillment)) {
                   // wholesaleSupport.wholesaleOrderAddToBill(order);  TODO 待完善
                }
            }
            iOrdersService.saveOrUpdateBatch(orders);
        }
    }
}
