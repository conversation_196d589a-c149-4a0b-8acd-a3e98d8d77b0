package com.zsmall.order.biz.test.factory.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.redis.utils.MathTimeUtil;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.common.domain.dto.SaleOrderDetailDTO;
import com.zsmall.common.domain.dto.SaleOrderItemDTO;
import com.zsmall.common.domain.resp.tiktok.express.TikTokApiReturnMsg;
import com.zsmall.common.domain.resp.tiktok.express.TikTokExpressSheet;
import com.zsmall.common.domain.resp.tiktok.product.resp.PackageDetailsResp;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.domain.tiktok.domain.dto.base.TikTokRespBaseEntity;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokSyncOrderSearchResp;
import com.zsmall.common.domain.tiktok.domain.dto.item.TikTokLineItem;
import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokOrder;
import com.zsmall.common.domain.tiktok.domain.dto.payment.TikTokPayment;
import com.zsmall.common.domain.vo.XxlJobSearchVO;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.SignalSenderEnum;
import com.zsmall.common.enums.order.TiktokLogisticsTypeEnum;
import com.zsmall.common.handler.AbstractThirdBusinessHandler;
import com.zsmall.order.biz.factory.ThirdOrderOperationFactory;
import com.zsmall.order.biz.utils.TikTokUtil;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.order.entity.iservice.OrderCodeGenerator;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.iservice.IProductMappingService;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/4 14:37
 */
@Slf4j
@Lazy
@Component("tiktokBusinessHandlerV2")
public class TikTokOrderBusinessV2Handler extends AbstractThirdBusinessHandler<TikTokExpressSheet, TikTokApiReturnMsg,OrderReceiveFromThirdDTO> {
    @Resource
    private ThirdOrderOperationFactory factory;

    @Resource
    private ISiteCountryCurrencyService iSiteCountryCurrencyService;

    @Resource
    private TikTokUtil tikTokUtil;
    public static final String ACCESS_TOKEN = "accessToken";
    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private IOrdersService iOrdersService;

    @Resource
    private IProductMappingService iProductMappingService;
    @Resource
    private OrderCodeGenerator orderCodeGenerator;
    @Override
    public void insertBusinessData(TikTokRespBaseEntity data, JSONObject json) {
        XxlJobSearchVO vo = JSON.toJavaObject(json, XxlJobSearchVO.class);
        // 拆解包装
        List<OrderReceiveFromThirdDTO> dtos = parseTikTokDataForDistribution(data, vo);
        //
        log.info("tiktok拆解数据:{}", JSON.toJSONString(dtos));
        // 拆解包装完后组入数据流转
        ThreadPoolExecutor executor = SpringUtils.getBean("ioThreadPoolExecutor", ThreadPoolExecutor.class);
        CountDownLatch downLatch = new CountDownLatch(dtos.size());
        for (OrderReceiveFromThirdDTO dto : dtos) {

            executor.submit(() -> {

                try {
                    Orders orders = new Orders();
                    orders.setChannelType(ChannelTypeEnum.TikTok);
                    // 目前直接在这里进行多线程操作
                    factory.getInvokeStrategy("tiktokOperationV2Handler")
                           .formalTripartiteEntry(JSON.toJSONString(dto), orders);

                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    downLatch.countDown();
                }
            });

        }
        // 用计数器
        try {
            downLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        // 此处完成
        log.info("tiktok数据插入完成,{}",JSON.toJSONString(dtos));
    }

    @Override
    public List<OrderReceiveFromThirdDTO> dataDismantling(TikTokRespBaseEntity data, JSONObject json) {
        XxlJobSearchVO vo = JSON.toJavaObject(json, XxlJobSearchVO.class);
        List<OrderReceiveFromThirdDTO> tiktokDTOS = new ArrayList<>();
        Object data1 = data.getData();
        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;
        // 原始订单存在一个订单多个子订单项
        List<TikTokOrder> orders = orderResp.getOrders();

        // 正式环境
        for (TikTokOrder order : orders) {
            log.info("tiktok数据拆解开始---,渠道订单号:{}", order.getTikTokOrderId());
            List<TikTokLineItem> lineItems = order.getLineItems();
            Boolean isMultiple = false;
            if(CollUtil.isNotEmpty(lineItems)&&lineItems.size()>1){
                isMultiple = true;
            }
            for (TikTokLineItem lineItem : lineItems) {
                OrderReceiveFromThirdDTO tiktokDTO = new OrderReceiveFromThirdDTO();
                // 拆分成1个tiktok订单 1个tiktok订单项 用以录入分销系统内 (原本多个子订单的会以sku纬度录入系统)
                tiktokDTO = tiktokDTOWrapper(tiktokDTO, order, lineItem, vo, null);
                tiktokDTO.setIsMultiple(isMultiple);
                tiktokDTOS.add(tiktokDTO);
            }

        }
        log.info("tiktok数据拆解完成---,数据:{}", JSON.toJSONString(tiktokDTOS));
        return tiktokDTOS;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void insertBusinessDataV2(List<OrderReceiveFromThirdDTO> dtos, JSONObject json,
                                     ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                     BusinessTypeMappingEnum mappingEnum, ChannelTypeEnum channelTypeEnum) {
        // 此处直接调用tiktokOrderBusiness
//        log.info("tiktok拆解数据:{}", JSON.toJSONString(dtos));
        TenantHelper.ignore(()->factory.getInvokeStrategy("tiktokOperationV2Handler")
                                       .formalTripartiteEntryTemplate(JSON.toJSONString(dtos),mappingEnum,businessNos, channelTypeEnum));


    }

    /**
     * 功能描述：发号器
     *
     * @param data 数据
     * @return {@link ConcurrentHashMap }<{@link String }, {@link ConcurrentHashMap }<{@link String }, {@link String }>>
     * <AUTHOR>
     * @date 2024/07/02
     */
    @Override
    public ConcurrentHashMap<String, ConcurrentHashMap<String, String>> generateBusinessNo(List<OrderReceiveFromThirdDTO> data) {

        ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos = new ConcurrentHashMap<>();
        // 根据业务标识拆分和生成订单号 内部无
        for (OrderReceiveFromThirdDTO dto : data) {
            String itemNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderItemNo);
            String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
            ConcurrentHashMap<String, String> businessNo = new ConcurrentHashMap<>();
            businessNo.put(SignalSenderEnum.order.name(), orderNo);
            businessNo.put(SignalSenderEnum.orderItem.name(), itemNo);
            businessNos.put(dto.getLineOrderItemId(), businessNo);
        }
        return businessNos;
    }


    @Override
    public boolean isExists(TikTokRespBaseEntity targetData) {


        if (ObjectUtil.isEmpty(targetData)) {
            return false;
        }
        if (targetData.getCode() != 0) {
            return false;
        }
        Object data1 = targetData.getData();
        if (ObjectUtil.isEmpty(data1)) {
            return false;
        }
        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;

        List<TikTokOrder> orders = orderResp.getOrders();
        if (CollUtil.isEmpty(orders)) {
            return false;
        }
        // 考虑在这里把订单集合清洗清理一下

        return true;
    }

    @Override
    public TikTokRespBaseEntity dataScreening(TikTokRespBaseEntity data, JSONObject json) {
        XxlJobSearchVO vo = JSON.toJavaObject(json, XxlJobSearchVO.class);
        // 拆解包装
        List<String> lineOrderItemIds = new ArrayList<>();
        List<OrderReceiveFromThirdDTO> dtos = parseTikTokDataForDistribution(data, vo);
        for (OrderReceiveFromThirdDTO dto : dtos) {
            List<SaleOrderItemDTO> saleOrderItemsList = dto.getSaleOrderItemsList();

            // 需要通过itemOrderId进行搜索
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getLineOrderItemId, dto.getLineOrderItemId())
                                                                             .eq(Orders::getDelFlag, 0).last("limit 1");

            Orders order = iOrdersService.getOne(lqw);
            if (ObjectUtil.isNotEmpty(order)) {
                // 需要过滤的子订单
                lineOrderItemIds.add(dto.getLineOrderItemId());
                continue;
            }
            // 新业务 此处不需要再移除未映射的订单
            for (SaleOrderItemDTO itemDTO : saleOrderItemsList) {

                ProductMapping mapping = iProductMappingService.getOne(new LambdaQueryWrapper<ProductMapping>().eq(ProductMapping::getChannelSku, itemDTO.getErpSku())
                                                                                                               .eq(ProductMapping::getChannelType, ChannelTypeEnum.TikTok.name())
                                                                                                               .eq(ProductMapping::getTenantId, dto.getTenantId())
                                                                                                               .eq(ProductMapping::getDelFlag,0)
                                                                                                               .last("limit 1"));
                if (ObjectUtil.isEmpty(mapping) || ObjectUtil.isEmpty(mapping.getProductSkuCode())) {
                    Pattern pattern = Pattern.compile("\\D");
                    Matcher matcher = pattern.matcher(itemDTO.getErpSku());
                    boolean result = matcher.find();
                    if(result){
                        log.error("\n产品尚未更新映射,请先同步映射,channelSku:{},店铺:{},租户标识:{}", itemDTO.getErpSku(),dto.getThirdChannelFlag(),dto.getTenantId());
                    }else{
                        log.error("\n产品未建立映射,channelSku:{},店铺:{},租户标识:{}", itemDTO.getErpSku(),dto.getThirdChannelFlag(),dto.getTenantId());
                    }
                    // 需要过滤的母订单
                    lineOrderItemIds.add(dto.getLineOrderItemId());

                }

            }
        }
        // 把data内的数据过滤掉
        Object data1 = data.getData();

        // 对异常子订单进行过滤,如果订单内的商品全部不符合,则过滤掉主订单
        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;
        List<TikTokOrder> orders = orderResp.getOrders();
        Set<String> itemIdSet = new HashSet<>(lineOrderItemIds);
        orders = orders.stream()
                       // 过滤出那些 lineItems 不包含需要移除的 line item 的 TikTokOrder
                       .filter(order -> {
                           order.setLineItems(order.getLineItems().stream()
                                                   .filter(lineItem -> !itemIdSet.contains(lineItem.getId()))
                                                   .collect(Collectors.toList()));
                           // 如果 lineItems 列表为空，则返回 false 以移除整个 TikTokOrder
                           return !order.getLineItems().isEmpty();
                       })
                       .collect(Collectors.toList());
        log.info(data.toString());
        orderResp.setOrders(orders);
        data.setData(orderResp);
        return data;
    }

    /**
     * 功能描述：快递单流程
     *
     * <AUTHOR>
     * @date 2024/03/28
     */
    @Override
    public void expressSheet(TikTokExpressSheet tikTokExpressSheet, TikTokApiReturnMsg tikTokApiReturnMsg,
                             AtomicInteger atomicInteger, JSON json) {

    }

    /**
     * 功能描述：解析 Tik Tok 数据进行分发
     *
     * @param data 数据
     * @param vo   VO
     * @return {@link List }<{@link OrderReceiveFromThirdDTO }>
     * <AUTHOR>
     * @date 2024/02/02
     */
    private List<OrderReceiveFromThirdDTO> parseTikTokDataForDistribution(TikTokRespBaseEntity data,
                                                                          XxlJobSearchVO vo) {
        List<OrderReceiveFromThirdDTO> tiktokDTOS = new ArrayList<>();
        Object data1 = data.getData();
        TikTokSyncOrderSearchResp orderResp = (TikTokSyncOrderSearchResp) data1;
        // 原始订单存在一个订单多个子订单项
        List<TikTokOrder> orders = orderResp.getOrders();

        // 正式环境
        for (TikTokOrder order : orders) {
            log.info("tiktok数据拆解开始---,渠道订单号:{}", order.getTikTokOrderId());
            List<TikTokLineItem> lineItems = order.getLineItems();
            for (TikTokLineItem lineItem : lineItems) {
                OrderReceiveFromThirdDTO tiktokDTO = new OrderReceiveFromThirdDTO();
                // 拆分成1个tiktok订单 1个tiktok订单项 用以录入分销系统内 (原本多个子订单的会以sku纬度录入系统)
                tiktokDTO = tiktokDTOWrapper(tiktokDTO, order, lineItem, vo, null);
                tiktokDTOS.add(tiktokDTO);
            }

        }
        log.info("tiktok数据拆解完成---,数据:{}", JSON.toJSONString(tiktokDTOS));
        return tiktokDTOS;
    }


    /**
     * 功能描述：Incr 键
     *
     * @param key 钥匙
     * @return {@link Long }
     * <AUTHOR>
     * @date 2024/02/27
     */
//    private Long incrKey(String key){
//        String lua = "local key = KEYS[1]\n" +
//            "local result = redis.call('EXISTS', key)\n" +
//            "if result == 1 then\n" +
//            "    redis.call('INCR', key)\n "+
//            "    return redis.call('EXPIRE', key, 360) \n" +
//            "else\n" +
//            "    redis.call('SET', key, 1)\n" +
//            "    return redis.call('EXPIRE', key, 360)\n" +
//            "end";
//        RedissonClient client = RedisUtils.getClient();
//        // 2. 执行lua脚本
//        Long result = client.getScript().eval(RScript.Mode.READ_WRITE, lua, RScript.ReturnType.INTEGER, Collections.singletonList("toErp:"+key));
//        log.info(result.toString());
//        return result;
//    }


    /**
     * 功能描述：抖音 wrapper
     *
     * @param tiktokDTO         抖音 DTO
     * @param order             次序
     * @param lineItem          订单项
     * @param vo                VO
     * @param packageDetailsMap
     * @return {@link OrderReceiveFromThirdDTO }
     * <AUTHOR>
     * @date 2024/02/02
     */
    private OrderReceiveFromThirdDTO tiktokDTOWrapper(OrderReceiveFromThirdDTO tiktokDTO, TikTokOrder order,
                                                      TikTokLineItem lineItem, XxlJobSearchVO vo,
                                                      ConcurrentHashMap<String, PackageDetailsResp> packageDetailsMap) {


        tiktokDTO.setTenantId(vo.getTenantId());

        tiktokDTO.setThirdChannelFlag(vo.getThirdChannelFlag());
        tiktokDTO.setOrderNo(order.getTikTokOrderId());
        tiktokDTO.setOrderStatus(order.getStatus());
        //军哥说tiktok items 内按照每一件货物来分组,所以这里默认都给1即可
        tiktokDTO.setTotalQuantity(1);
        TikTokRecipientAddress address = tiktokDTO.getAddress();
        String regionCode = address.getRegionCode();
        SiteCountryCurrency site = iSiteCountryCurrencyService.getSiteByCountryCode(regionCode);
        tiktokDTO.setCurrencyCode(site.getCurrencyCode());
        try {
            tiktokDTO.setCreateTime(MathTimeUtil.longToDate(order.getCreateTime()));
            tiktokDTO.setPaidTime(MathTimeUtil.longToDate(order.getPaidTime()));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        String shippingType = order.getShippingType();
        if (TiktokLogisticsTypeEnum.TIKTOK.name().equals(shippingType)) {
            shippingType = LogisticsTypeEnum.PickUp.name();
        }
        if (TiktokLogisticsTypeEnum.SELLER.name().equals(shippingType)) {
            shippingType = LogisticsTypeEnum.DropShipping.name();
        }
        tiktokDTO.setLogisticsType(shippingType);
        tiktokDTO.setRemark(order.getBuyerMessage());

        // 物流
        SaleOrderDetailDTO saleOrderDetailDTO = new SaleOrderDetailDTO();
        // 用子订单
        saleOrderDetailDTO.setCarrier(lineItem.getShippingProviderName());
        // 这里需要根据lineItem的size看 一件代发拿order 自提应该拿的是lineItem的trackingNumber

//        saleOrderDetailDTO.setLogisticsTrackingNo(order.getTrackingNumber());
        saleOrderDetailDTO.setLogisticsTrackingNo(lineItem.getTrackingNumber());

        // item详情
        List<SaleOrderItemDTO> itemDTOS = new ArrayList<SaleOrderItemDTO>();
        SaleOrderItemDTO itemDTO = new SaleOrderItemDTO();
        itemDTO.setErpSku(lineItem.getSellerSku());
        //军哥说tiktok items 内按照每一件货物来分组,所以这里默认都给1即可
//        itemDTO.setQuantity(packageSkuMsg.getQuantity());
        itemDTO.setQuantity(1);
        itemDTO.setSkuId(lineItem.getSkuId());
        itemDTO.setUnitPrice(new BigDecimal(lineItem.getSalePrice()));
        itemDTOS.add(itemDTO);
        //地址
        TikTokRecipientAddress recipientAddress = order.getRecipientAddress();
        recipientAddress.setBuyerEmail(order.getBuyerEmail());

        TikTokPayment payment = order.getPayment();
        tiktokDTO.setSubTotal(payment.getSubTotal());
        tiktokDTO.setTotalAmount(payment.getTotalAmount());
        tiktokDTO.setSaleOrderDetails(saleOrderDetailDTO);
        tiktokDTO.pushSaleOrderItemsList(itemDTOS);
        tiktokDTO.setAddress(recipientAddress);
        tiktokDTO.setLineOrderItemId(lineItem.getId());
        return tiktokDTO;
    }
}
