package com.zsmall.order.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.domain.SysApi;
import com.hengjian.openapi.service.ISysApiService;
import com.hengjian.system.mapper.SysConfigMapper;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.domain.dto.CompensationOrderDTO;
import com.zsmall.common.domain.dto.OpenApiCancelOrderDTO;
import com.zsmall.common.domain.resp.tiktok.search.SearchProductResp;
import com.zsmall.common.domain.resp.tiktok.search.TikTokRespForProduct;
import com.zsmall.common.domain.tiktok.domain.dto.base.TikTokRespBaseEntity;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokSyncOrderSearchResp;
import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokOrder;
import com.zsmall.common.domain.vo.XxlJobSearchVO;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.common.enums.tiktok.OrderStatusEnum;
import com.zsmall.extend.core.enums.SortType;
import com.zsmall.extend.wms.kit.ThebizarkDelegate;
import com.zsmall.extend.wms.kit.ThebizarkKit;
import com.zsmall.extend.wms.model.ThebizarkBean;
import com.zsmall.extend.wms.model.base.PageResult;
import com.zsmall.extend.wms.model.base.Result;
import com.zsmall.extend.wms.model.order.CancelOutOrder;
import com.zsmall.extend.wms.model.order.InOrderPage;
import com.zsmall.extend.wms.model.order.OutOrder;
import com.zsmall.order.biz.service.CompensationJobService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.support.CompensationJobTransactionSup;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.biz.test.service.support.TikTokSupportV2;
import com.zsmall.order.biz.utils.TikTokShopApiUtil;
import com.zsmall.order.entity.domain.OrderItemProductSku;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrderItemProductSkuService;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IOrderItemShippingRecordService;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.order.entity.manger.IOrderManger;
import com.zsmall.order.entity.mapper.OrdersMapper;
import com.zsmall.system.biz.service.TenantSalesChannelService;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.XxlConfKeyValue;
import com.zsmall.system.entity.enums.XxlConfEnum;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.warehouse.entity.domain.WarehouseBizArkConfig;
import com.zsmall.warehouse.entity.iservice.IWarehouseBizArkConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * lty notes 领域服务
 *
 * <AUTHOR> Theo
 * @create 2024/2/4 14:01
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CompensationJobServiceImpl implements CompensationJobService {
    @Resource
    private final SysConfigMapper sysConfigMapper;
    @Resource
    private TikTokSupportV2 tikTokSupportV2;
    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final TenantSalesChannelService tenantSalesChannelService;
    private final OrderSupport orderSupport;
    private final IOrdersService iOrdersService;
    private final IOrderItemShippingRecordService iOrderItemShippingRecordService;
    private final OrdersService ordersService;
    private final IWarehouseBizArkConfigService iWarehouseBizArkConfigService;
    private final ISysTenantService sysTenantService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final CompensationJobTransactionSup compensationJobTransaction;
    private final IOrderManger iOrderManger;
    private final OrdersMapper ordersMapper;
    private final ISysApiService iSysApiService;
    private final IOrderItemService iOrderItemService;
    private ThebizarkDelegate initDelegate(String supplierId) {
        WarehouseBizArkConfig bizArkConfig = iWarehouseBizArkConfigService.queryBySupplierId(supplierId);
        if (ObjectUtil.isEmpty(bizArkConfig)) {
            log.error("未找到供应商{}的配置信息", supplierId);
            return null;
        }
        return ThebizarkKit.create(new ThebizarkBean(bizArkConfig.getBizArkApiUrl(), bizArkConfig.getBizArkSecretKey(), bizArkConfig.getBizArkChannelAccount()));
    }
    @Resource
    private TikTokShopApiUtil tikTokShopApiUtil;

    private final TiktokLineOrderCompensationServiceImpl compensationService;
    @Override
    public void pullOrder(String name, String voJson, String tenantId) {
        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.TikTok);
        log.info("TikTok定时任务【拉取订单】 - 有效店铺数量 = {}", CollUtil.size(channelList));
        String nextPageToken = null;
        StopWatch allStopWatch = new StopWatch();
        allStopWatch.start();
        for (TenantSalesChannel tenantSalesChannel : channelList) {
            log.info("当前租户:{}",tenantId);
            log.info("当前店铺信息:{}",tenantSalesChannel);
            if(StrUtil.isNotEmpty(tenantId)){
                if(!tenantSalesChannel.getTenantId().equals(tenantId)){
                    continue;
                }
            }
            XxlJobSearchVO vo = JSONObject.parseObject(voJson, XxlJobSearchVO.class);
            vo.setTenantId(tenantSalesChannel.getTenantId());
            vo.setThirdChannelFlag(tenantSalesChannel.getThirdChannelFlag());
            vo.setShopId(String.valueOf(tenantSalesChannel.getId()));
            XxlConfKeyValue xxlConfKey = tenantSalesChannelService.getXxlConfKey(tenantSalesChannel);
            Map<String, String> xxlValueMap = xxlConfKey.getXxlValueMap();
            if(CollUtil.isEmpty(xxlValueMap)){
                continue;
            }
            String tiktokAppKey = xxlValueMap.get(XxlConfEnum.TikTokAppKey.name());
            String tiktokAppSecret = xxlValueMap.get(XxlConfEnum.TikTokAppSecret.name());
            if("crafted comforts-US".equals(tenantSalesChannel.getThirdChannelFlag())){
                // 当前时间-5天如果大于2024年10月10号8点

                LocalDateTime currentDateTime = LocalDateTime.now();

                // 减去5天
                LocalDateTime dateTimeFiveDaysAgo = currentDateTime.minusDays(5);
//                10/08/2024
                LocalDateTime localDateTime = LocalDateTime.of(2024, 10, 8, 18, 32,19);
                // dateTimeFiveDaysAgo=当前时间-5天 如果dateTimeFiveDaysAgo大于指定时间就用 dateTimeFiveDaysAgo ,否则就用于指定时间
                if(dateTimeFiveDaysAgo.isBefore(localDateTime)){
                    dateTimeFiveDaysAgo = localDateTime;
                }
                Instant nowDays = currentDateTime.atZone(ZoneId.systemDefault()).toInstant();

                // 转换为Instant对象（以UTC为基准）
                Instant instantFiveDaysAgo = dateTimeFiveDaysAgo.atZone(java.time.ZoneId.systemDefault()).toInstant();

                // 转换为秒时间戳
                long timestampInSeconds = Math.floorDiv(instantFiveDaysAgo.getEpochSecond(), 1);

                long nowTime = Math.floorDiv(nowDays.getEpochSecond(), 1);
                vo.setCreateTimeGe(timestampInSeconds);
                vo.setCreateTimeLt(nowTime);
            }

//            String updateStrategy = XxlJobHelper.getJobParam();
            // 全量更新,循环调用接口直到next_token 为null时,停止调用
            boolean flag = true;
            int i = 0;
            while (flag) {

                if (i != 0 && StringUtils.isNotBlank(nextPageToken)) {
                    vo.setPageToken(nextPageToken);
                }
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                Object allOrder = tikTokSupportV2.getAllOrderV2(vo, TikTokSyncOrderSearchResp.class,tiktokAppKey,tiktokAppSecret);
                stopWatch.stop();
                log.info("TikTok定时任务【拉取订单】 - 有效店铺数量 = {},用时信息:{}", CollUtil.size(channelList), stopWatch.prettyPrint());
                if (ObjectUtil.isNotEmpty(allOrder)) {
                    String json = JSONObject.toJSONString(allOrder);
                    // 拿到resp做数据拆解 组装 放入list
                    TikTokRespBaseEntity tikTokRespBase = JSON.parseObject(json, TikTokRespBaseEntity.class);
                    Object data = tikTokRespBase.getData();
                    String dataJson = JSONObject.toJSONString(data);
                    TikTokSyncOrderSearchResp resp = JSON.parseObject(dataJson, TikTokSyncOrderSearchResp.class);

                    if (StringUtils.isBlank(resp.getNextPageToken())) {
                        // 当前流程为最后一组
                        log.info("TikTok定时任务【批量拉取订单】 - nextPageToken为空,任务准备跳出");
                        flag =false;
                    }
                    // curd next_token 使用上一组的
                    nextPageToken = resp.getNextPageToken();
                    i++;
                } else {
                    break;
                }

            }
        }
        allStopWatch.stop();
        log.info("TikTok定时任务【批量拉取订单】 - 结束,用时信息:{}", allStopWatch.prettyPrint());
    }

    @Override
    public void pullProduct(String channel, String voJson) {
        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.TikTok);
        log.info("TikTok定时任务【拉取产品】 - 有效店铺数量 = {}", CollUtil.size(channelList));
        String nextPageToken = null;
        for (TenantSalesChannel tenantSalesChannel : channelList) {
            XxlJobSearchVO vo = JSONObject.parseObject(voJson, XxlJobSearchVO.class);
            vo.setTenantId(tenantSalesChannel.getTenantId());
            vo.setThirdChannelFlag(tenantSalesChannel.getThirdChannelFlag());
            vo.setShopId(String.valueOf(tenantSalesChannel.getId()));
            vo.setChannelId(String.valueOf(tenantSalesChannel.getId()));
            XxlConfKeyValue xxlConfKey = tenantSalesChannelService.getXxlConfKey(tenantSalesChannel);
            Map<String, String> xxlValueMap = xxlConfKey.getXxlValueMap();
            if(CollUtil.isEmpty(xxlValueMap)){
                continue;
            }
            String tiktokAppKey = xxlValueMap.get(XxlConfEnum.TikTokAppKey.name());
            String tiktokAppSecret = xxlValueMap.get(XxlConfEnum.TikTokAppSecret.name());
            // 全量和增量更新的区别只和有关任务的配置参数有关
            /**
             * 全量更新
             * 1.调用获取接口,size设置为100,直到返回值的next_token为null时停止调用
             * 2.根据数据量的多少,性能不足时可以放入队列消化
             * **/

            int i = 0;

            while (true) {

                if (i != 0 && StringUtils.isNotBlank(nextPageToken)) {
                    vo.setPageToken(nextPageToken);
                }

                TikTokRespForProduct resp = (TikTokRespForProduct) tikTokSupportV2.getAllProduct(vo, TikTokRespForProduct.class,tiktokAppKey,tiktokAppSecret);
                if (ObjectUtil.isEmpty(resp)) {
                    break;
                }
                if (resp.getCode() != 0) {
                    break;
                }
                if (ObjectUtil.isNotEmpty(resp)) {
                    SearchProductResp body = resp.getData();
                    // 拿到resp做数据拆解 组装 放入list

                    if (StringUtils.isBlank(body.getNextPageToken()) || "b2Zmc2V0PTAK".equals(nextPageToken)) {
                        // 当前流程为最后一组
                        break;
                    }
                    // curd next_token 使用上一组的
                    nextPageToken = body.getNextPageToken();
                    i++;
                } else {
                    break;
                }

            }

            vo.setPageToken(null);
            vo.setTenantId(null);
            vo.setThirdChannelFlag(null);
            vo.setShopId(null);

        }
        log.info("TikTok定时任务【拉取商品】 - 结束");
    }

    @Override
    public void pullProduct(String name, String voJson, List<String> tenantIds) {
        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.TikTok);
        log.info("TikTok定时任务【拉取产品】 - 有效店铺数量 = {}", CollUtil.size(channelList));
        String nextPageToken = null;
        for (TenantSalesChannel tenantSalesChannel : channelList) {
            if (CollUtil.isNotEmpty(tenantIds) && !tenantIds.contains(tenantSalesChannel.getTenantId())) {
                continue;
            }
            XxlJobSearchVO vo = JSONObject.parseObject(voJson, XxlJobSearchVO.class);
            vo.setTenantId(tenantSalesChannel.getTenantId());
            vo.setThirdChannelFlag(tenantSalesChannel.getThirdChannelFlag());
            vo.setShopId(String.valueOf(tenantSalesChannel.getId()));
            vo.setChannelId(String.valueOf(tenantSalesChannel.getId()));

            XxlConfKeyValue xxlConfKey = tenantSalesChannelService.getXxlConfKey(tenantSalesChannel);
            Map<String, String> xxlValueMap = xxlConfKey.getXxlValueMap();
            if(CollUtil.isEmpty(xxlValueMap)){
                continue;
            }
            String tiktokAppKey = xxlValueMap.get(XxlConfEnum.TikTokAppKey.name());
            String tiktokAppSecret = xxlValueMap.get(XxlConfEnum.TikTokAppSecret.name());
            // 全量和增量更新的区别只和有关任务的配置参数有关
            /**
             * 全量更新
             * 1.调用获取接口,size设置为100,直到返回值的next_token为null时停止调用
             * 2.根据数据量的多少,性能不足时可以放入队列消化
             * **/

            int i = 0;

            while (true) {

                if (i != 0 && StringUtils.isNotBlank(nextPageToken)) {
                    vo.setPageToken(nextPageToken);
                }

                TikTokRespForProduct resp = (TikTokRespForProduct) tikTokSupportV2.getAllProduct(vo, TikTokRespForProduct.class,tiktokAppKey,tiktokAppSecret);
                if (ObjectUtil.isEmpty(resp)) {
                    break;
                }
                if (resp.getCode() != 0) {
                    break;
                }
                if (ObjectUtil.isNotEmpty(resp)) {
                    SearchProductResp body = resp.getData();
                    // 拿到resp做数据拆解 组装 放入list

                    if (StringUtils.isBlank(body.getNextPageToken()) || "b2Zmc2V0PTAK".equals(nextPageToken)) {
                        // 当前流程为最后一组
                        break;
                    }
                    // curd next_token 使用上一组的
                    nextPageToken = body.getNextPageToken();
                    i++;
                } else {
                    break;
                }

            }

            vo.setPageToken(null);
            vo.setTenantId(null);
            vo.setThirdChannelFlag(null);
            vo.setShopId(null);

        }
        log.info("TikTok定时任务【拉取商品】 - 结束");
    }


    @Override
    public void pullOrderToErp(List<String> orderNos) {
        List<Orders> orders = iOrdersService.list(new LambdaQueryWrapper<Orders>().in(Orders::getOrderNo, orderNos));
        TenantHelper.ignore(() -> orderSupport.orderThirdWarehouseFollowUp(orders, true));
        log.info("erp推送任务结束");
    }

    @Override
    public void pullOrderToErpByOrders(List<Orders> orderNos) {
        TenantHelper.ignore(() -> orderSupport.orderThirdWarehouseFollowUp(orderNos, true));
        // 从redis里拿到推送结果打印并返回

    }

    @Override
    public void pullOrderForSpecify(String name, String voJson, List<String> tenantIdList) {

        // 指定店铺
        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.TikTok)
                                                                         .stream()
                                                                         .filter(tenantSalesChannel -> tenantIdList.contains(tenantSalesChannel.getTenantId()))
                                                                         .collect(Collectors.toList());


        log.info("TikTok定时任务【拉取订单】 - 有效店铺数量 = {}", CollUtil.size(channelList));
        String nextPageToken = null;
        for (TenantSalesChannel tenantSalesChannel : channelList) {

            XxlJobSearchVO vo = JSONObject.parseObject(voJson, XxlJobSearchVO.class);
            vo.setTenantId(tenantSalesChannel.getTenantId());
            vo.setThirdChannelFlag(tenantSalesChannel.getThirdChannelFlag());
            vo.setShopId(String.valueOf(tenantSalesChannel.getId()));
            XxlConfKeyValue xxlConfKey = tenantSalesChannelService.getXxlConfKey(tenantSalesChannel);
            Map<String, String> xxlValueMap = xxlConfKey.getXxlValueMap();
            if(CollUtil.isEmpty(xxlValueMap)){
                continue;
            }
            String tiktokAppKey = xxlValueMap.get(XxlConfEnum.TikTokAppKey.name());
            String tiktokAppSecret = xxlValueMap.get(XxlConfEnum.TikTokAppSecret.name());
//            String updateStrategy = XxlJobHelper.getJobParam();
            // 全量更新,循环调用接口直到next_token 为null时,停止调用

            int i = 0;
            while (true) {

                if (i != 0 && StringUtils.isNotBlank(nextPageToken)) {
                    vo.setPageToken(nextPageToken);
                }

                Object allOrder = tikTokSupportV2.getAllOrderForSpecify(vo, TikTokSyncOrderSearchResp.class,tiktokAppKey,tiktokAppSecret);

                if (ObjectUtil.isNotEmpty(allOrder)) {
                    String json = JSONObject.toJSONString(allOrder);
                    // 拿到resp做数据拆解 组装 放入list
                    TikTokRespBaseEntity tikTokRespBase = JSON.parseObject(json, TikTokRespBaseEntity.class);
                    Object data = tikTokRespBase.getData();
                    String dataJson = JSONObject.toJSONString(data);
                    TikTokSyncOrderSearchResp resp = JSON.parseObject(dataJson, TikTokSyncOrderSearchResp.class);

                    if (StringUtils.isBlank(resp.getNextPageToken()) || "6AsPQsUMvH3RkchNUPPh22NROHkE0D8pmq/N5M1kHYcZmtRyv9aVrNv65W7Q6tFA+7D1ud64MPNz5OaT".equals(resp.getNextPageToken())) {
                        // 当前流程为最后一组
                        break;
                    }
                    // curd next_token 使用上一组的
                    nextPageToken = resp.getNextPageToken();
                    i++;
                } else {
                    break;
                }

            }

        }
        log.info("TikTok定时任务【拉取任务】 - 结束");
    }
//    XxlJobSearchVO vo = JSONObject.parseObject(voJson, XxlJobSearchVO.class);
//            vo.setTenantId(tenantSalesChannel.getTenantId());
//            vo.setThirdChannelFlag(tenantSalesChannel.getThirdChannelFlag());
//            vo.setShopId(String.valueOf(tenantSalesChannel.getId()));
    @Override
    public void pullOrderForSpecifyOrderNo(String name, CompensationOrderDTO dtos) {
        String status = dtos.getStatus();
        List<String> orderNos = dtos.getOrderNo();
        String shopId = dtos.getShopId();
        for (String orderNo : orderNos) {
            List<TikTokOrder> orderDetail = tikTokShopApiUtil.getOrderDetail(Collections.singletonList(orderNo), shopId);
            TikTokOrder tikTokOrder = orderDetail.get(0);
            Long createTimeGe = tikTokOrder.getCreateTime();
            long createTimeLt = createTimeGe + 1L;
            XxlJobSearchVO xxlJobSearchVO = new XxlJobSearchVO();
            xxlJobSearchVO.setOrderStatus(status);
            xxlJobSearchVO.setPageSize("100");
            xxlJobSearchVO.setCreateTimeGe(createTimeGe);
            xxlJobSearchVO.setCreateTimeLt(createTimeLt);
            String jsonString = JSONObject.toJSONString(xxlJobSearchVO);
            log.info("拉取订单参数:{}", jsonString);
            if(OrderStatusEnum.AWAITING_SHIPMENT.name().equals(tikTokOrder.getStatus())){
                pullOrderForSpecify(ChannelTypeEnum.TikTok.name(), jsonString, Collections.singletonList(dtos.getTenantId()));
            }

        }
        log.info("TikTok定时任务【拉取任务】 - 结束");

    }

    @Override
    public void cancellationCompensationHandler(String type) {
        // 1.查询补偿表中未补偿的数据
        // 2.根据type进行补偿
        // 3.更新补偿表中的状态
        compensationService.cancellationCompensationPlan(type);

    }

    @Override
    public void pullOrderV2(String name, String jsonString) {

    }

    @Override
    public void pullOrderV2(String name, String voJson, List<String> tenantIdList) {
        // 指定店铺
        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.TikTok)
                                                                         .stream()
                                                                         .filter(tenantSalesChannel -> tenantIdList.contains(tenantSalesChannel.getTenantId()))
                                                                         .collect(Collectors.toList());
//        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.TikTok);
        log.info("TikTok定时任务【批量拉取订单】 - 有效店铺数量 = {}", CollUtil.size(channelList));
        String nextPageToken = null;
        StopWatch allStopWatch = new StopWatch();
        allStopWatch.start();
        for (TenantSalesChannel tenantSalesChannel : channelList) {

            XxlJobSearchVO vo = JSONObject.parseObject(voJson, XxlJobSearchVO.class);
            vo.setTenantId(tenantSalesChannel.getTenantId());
            vo.setThirdChannelFlag(tenantSalesChannel.getThirdChannelFlag());
            vo.setShopId(String.valueOf(tenantSalesChannel.getId()));
            XxlConfKeyValue xxlConfKey = tenantSalesChannelService.getXxlConfKey(tenantSalesChannel);
            Map<String, String> xxlValueMap = xxlConfKey.getXxlValueMap();
            if(CollUtil.isEmpty(xxlValueMap)){
                continue;
            }
            String tiktokAppKey = xxlValueMap.get(XxlConfEnum.TikTokAppKey.name());
            String tiktokAppSecret = xxlValueMap.get(XxlConfEnum.TikTokAppSecret.name());
//            String updateStrategy = XxlJobHelper.getJobParam();
            // 全量更新,循环调用接口直到next_token 为null时,停止调用

            int i = 0;
            boolean flag = true;
            while (flag) {

                if (i != 0 && StringUtils.isNotBlank(nextPageToken)) {
                    vo.setPageToken(nextPageToken);
                }
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                Object allOrder = tikTokSupportV2.getAllOrderV2(vo, TikTokSyncOrderSearchResp.class,tiktokAppKey,tiktokAppSecret);
                stopWatch.stop();
                log.info("TikTok定时任务【拉取订单】 - 有效店铺数量 = {},用时信息:{}", CollUtil.size(channelList), stopWatch.prettyPrint());
                if (ObjectUtil.isNotEmpty(allOrder)) {
                    String json = JSONObject.toJSONString(allOrder);
                    // 拿到resp做数据拆解 组装 放入list
                    TikTokRespBaseEntity tikTokRespBase = JSON.parseObject(json, TikTokRespBaseEntity.class);
                    Object data = tikTokRespBase.getData();
                    String dataJson = JSONObject.toJSONString(data);
                    TikTokSyncOrderSearchResp resp = JSON.parseObject(dataJson, TikTokSyncOrderSearchResp.class);

                    if (StringUtils.isBlank(resp.getNextPageToken())) {
                        // 当前流程为最后一组
                        break;
                    }
                    // curd next_token 使用上一组的
                    nextPageToken = resp.getNextPageToken();
                    i++;
                } else {
                    break;
                }

            }

        }
        allStopWatch.stop();
        log.info("TikTok定时任务【批量拉取订单】 - 结束,用时信息:{}", allStopWatch.prettyPrint());
    }

    @Override
    public void abnormalOrderCompensationHandler(Integer otherDay) {
        // 1.查询库内,Abnormal状态的订单,调用erp查询接口,如果已经存在,则更新状态,如果不存在,则补偿进行order推送;
        compensationMethod(otherDay);

    }

    @Override
    public void getOrderFromErp(String order) {

    }

    @Override
    public void compensationMethod(Integer otherDay) {
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime dateTimeFiveDaysAgo;
        LambdaQueryWrapper<Orders> lambdaQueryWrapper = new LambdaQueryWrapper<Orders>().eq(Orders::getFulfillmentProgress, LogisticsProgress.Abnormal);
        if(ObjectUtil.isNotNull(otherDay)){
            dateTimeFiveDaysAgo = currentDateTime.minusDays(otherDay);
            lambdaQueryWrapper.ge(Orders::getCreateTime, dateTimeFiveDaysAgo);
        }
        lambdaQueryWrapper.eq(Orders::getDelFlag, 0);
        String allowTenantId = sysConfigMapper.getSysConfigByKey("product.allow.examine");
        String[] split = allowTenantId.split("[，,;]");
        List<String> list = Arrays.asList(split);

        lambdaQueryWrapper.notIn(Orders::getTenantId, list);
        List<Orders> abnormalOrders = iOrdersService.list(lambdaQueryWrapper);
        if (CollUtil.isEmpty(abnormalOrders)){
            log.info("没有异常订单需要补偿");
            return;
        }
        List<String> abnormalOrderNos = abnormalOrders.stream().map(Orders::getOrderNo).collect(Collectors.toList());
        LambdaQueryWrapper<OrderItemProductSku> in = new LambdaQueryWrapper<OrderItemProductSku>()
            .eq(OrderItemProductSku::getDelFlag, 0)
            .in(OrderItemProductSku::getOrderNo, abnormalOrderNos);
        List<OrderItemProductSku> productSkus = iOrderItemProductSkuService.list(in);
        if(CollUtil.isEmpty(productSkus)){
            log.info("订单信息异常,未找到订单商品信息");
            return;
        }
        Map<String, String> orderNoToSupplierTenantIdMap = productSkus.stream()
                                                                      .collect(Collectors.toMap(OrderItemProductSku::getOrderNo, OrderItemProductSku::getSupplierTenantId));
        // productSkus转换为map,key是orderNo,value 是 supplierTenantId
        List<String> needCompensationOrderNos = new ArrayList<>();

        Set<String> uniqueOrderExtendIds = new HashSet<>();
        List<Orders> deduplicatedAbnormalOrders = abnormalOrders.stream()
                                                                .filter(order -> uniqueOrderExtendIds.add(order.getOrderExtendId()))
                                                                .collect(Collectors.toList());

        List<String> deduplicatedAbnormalOrderNos = deduplicatedAbnormalOrders.stream().map(Orders::getOrderExtendId).collect(Collectors.toList());
        for (Orders abnormalOrder : deduplicatedAbnormalOrders) {
            String orderNo = abnormalOrder.getOrderNo();
            String orderExtendId = abnormalOrder.getOrderExtendId();
            String supplierTenantId;
            try{
                supplierTenantId = orderNoToSupplierTenantIdMap.get(orderNo);
            }catch (Exception e){
                log.error("订单信息异常,未找到订单商品信息,异常订单:{}",orderNo);
                continue;
            }

            ThebizarkDelegate delegate = initDelegate(supplierTenantId);
            if (ObjectUtil.isNull(delegate)) {
                continue;
            }
            Result<OutOrder> one = null;
            try{
                one = delegate.orderApi().getOne(orderExtendId);
            }catch (Exception e){
                log.error("查询erp异常,异常单号:{}",orderExtendId);
                log.error("查询erp异常",e);
                continue;
            }
            OutOrder data = one.getData();
            // needCompensationOrderNos内的order是orderExtendId
            needCompensationOrderNos.add(data.getOrderNo());
        }
        //needCompensationOrderNos 内的走更新,不在内的走补偿
        // 找到abnormalOrderNos内存在的 needCompensationOrderNos内不存在的 元素生成一个List<String>
        List<String> needSendOrderNo = deduplicatedAbnormalOrderNos.stream()
                                                       .filter(orderExtendId -> !needCompensationOrderNos.contains(orderExtendId))
                                                       .collect(Collectors.toList());

        try{
            if(CollUtil.isNotEmpty(needSendOrderNo)){
                // 走重新推送 这里的是orderExtendId 不是 orderNo 所以需要根据orderExtendId查询orderNo
                List<Orders> orders = iOrdersService.list(new LambdaQueryWrapper<Orders>().in(Orders::getOrderExtendId, needSendOrderNo));
                List<String> orderNos = orders.stream().map(Orders::getOrderNo).collect(Collectors.toList());
                pullOrderToErp(orderNos);
            }
        }catch (Exception e){
            log.error("异常订单推送败",e);
        }
        try{
            if(CollUtil.isNotEmpty(needCompensationOrderNos)){
                // 走更新
                updateBatchNeedCompensationOrderNos(needCompensationOrderNos);
            }
        }catch (Exception e){
            log.error("异常订单更新失败",e);
        }
    }

    @Override
    public void cancelOrder(OpenApiCancelOrderDTO dto) {
        String orderNo = dto.getOrderNo();
        List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.getByOrderExtendId(orderNo));
        // ordersList 过滤掉cancelStatus状态不是1的订单

        List<Orders> orders = ordersList.stream().filter(order -> order.getCancelStatus() == 1)
                                         .collect(Collectors.toList());
        // 查询订单erp 是否已经取消
        if(CollUtil.isNotEmpty(ordersList)){
            for (Orders order : orders) {
                iOrderManger.processRefundsAuto(1,order.getOrderExtendId(),order.getTenantId(), false);
            }
        }
    }

    @Override
    public void specifyOrderToGoErpCancellation(List<OpenApiCancelOrderDTO> dtos) {
        List<String> orderExtendIds = dtos.stream().map(OpenApiCancelOrderDTO::getOrderNo).collect(Collectors.toList());
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Orders::getCancelStatus, 1);
        queryWrapper.eq(Orders::getDelFlag, 0);
        queryWrapper.in(Orders::getOrderExtendId, orderExtendIds);
        List<Orders> orders = TenantHelper.ignore(()->ordersMapper.selectList(queryWrapper));
        if (CollUtil.isEmpty(orders)) {
            log.info("没有需要取消的订单");
            return;
        }
        List<SysApi> sysApis = iSysApiService.list();
        List<String> autoTenantIds = sysApis.stream().map(SysApi::getTenantId).collect(Collectors.toList());
        List<Orders> needCancelOrders = orders.stream().filter(order -> autoTenantIds.contains(order.getTenantId())).collect(Collectors.toList());
        if(CollUtil.isEmpty(needCancelOrders)){
            log.info("该订单尚未开通自动取消权限");
        }
        List<String> orderNos = needCancelOrders.stream().map(Orders::getOrderNo).collect(Collectors.toList());
        List<com.zsmall.order.entity.domain.OrderItem> orderItems = iOrderItemService.getList(orderNos);

        Map<String, String> orderNoAndTenantIdMap = orderItems.stream()
                                                              .collect(Collectors.toMap(com.zsmall.order.entity.domain.OrderItem::getOrderNo, com.zsmall.order.entity.domain.OrderItem::getSupplierTenantId));

        ArrayList<String> orderNoList = new ArrayList<>();

        for (Orders needCancelOrder : orders) {
            String orderNo = needCancelOrder.getOrderNo();
            String tenantId = needCancelOrder.getTenantId();
            // LTL 需要合单
            if(orderNoList.contains(needCancelOrder.getOrderExtendId())){
                continue;
            }else {
                orderNoList.add(needCancelOrder.getOrderExtendId());
            }
            // 先查询erp有没有取消结果,有就取消成功或失败
            String supplierTenantId  = orderNoAndTenantIdMap.get(needCancelOrder.getOrderNo());
            ThebizarkDelegate delegate = initDelegate(supplierTenantId);
            CancelOutOrder outOrder = delegate.orderApi().queryCancelOrderV2(needCancelOrder.getOrderExtendId()).getData().get(0);
//            0拦截中 1拦截成功 2拦截失败 -1接口调用失败或异常 3部分拦截
            Integer deliverInterceptStatus = outOrder.getDeliverInterceptStatus();
            // 拦截成功
            if(ObjectUtil.isNotEmpty(deliverInterceptStatus)){
                // 走提交退款流程
                TenantHelper.ignore(()->iOrderManger.processRefundsAuto(deliverInterceptStatus,orderNo,tenantId, false));
            }

        }
    }

    private void compensationMethodForPage() {
        // 找一个已经发送过去的订单,然后查一下看能不能查到;
        LambdaQueryWrapper<Orders> eq = new LambdaQueryWrapper<Orders>().eq(Orders::getFulfillmentProgress, LogisticsProgress.Abnormal)
                                                                        .eq(Orders::getDelFlag, 0);
        List<Orders> abnormalOrders = iOrdersService.list(eq);
        if (CollUtil.isEmpty(abnormalOrders)) {
            log.info("没有异常订单需要补偿");
            return;
        }
        List<String> abnormalOrderNos = abnormalOrders.stream().map(Orders::getOrderNo).collect(Collectors.toList());
        LambdaQueryWrapper<OrderItemProductSku> in = new LambdaQueryWrapper<OrderItemProductSku>()
            .eq(OrderItemProductSku::getDelFlag, 0)
            .in(OrderItemProductSku::getOrderNo, abnormalOrderNos);
        List<OrderItemProductSku> productSkus = iOrderItemProductSkuService.list(in);
//        <供应商id,map<分销商id,分销商单号>>
        // productSkus转换为map,key是supplierTenantId,value是map2;map2的key是tenantId,value是String 以逗号分隔的orderNo拼接
        Map<String, Map<String, String>> toErpMap = productSkus.stream()
                                                               .collect(Collectors.groupingBy(
                                                                   OrderItemProductSku::getSupplierTenantId,
                                                                   Collectors.groupingBy(
                                                                       OrderItemProductSku::getTenantId,
                                                                       Collectors.mapping(OrderItemProductSku::getOrderNo, Collectors.joining(","))
                                                                   )
                                                               ));
        List<String> tenantIds = abnormalOrders.stream()
                                               .map(Orders::getTenantId)
                                               .distinct()
                                               .collect(Collectors.toList());
        //
        Map<String, Map<String, String>> flagMap = sysTenantService.queryByTenantIds(tenantIds);
        // 遍历toErpMap
        for (Map.Entry<String, Map<String, String>> entry : toErpMap.entrySet()) {
            // 不同供货商
            String supplierTenantId = entry.getKey();
            Map<String, String> tenantIdAndOrderNos = entry.getValue();
            // 遍历tenantIdAndOrderNos
            WarehouseBizArkConfig bizArkConfig = iWarehouseBizArkConfigService.queryBySupplierId(supplierTenantId);
            if (ObjectUtil.isEmpty(bizArkConfig)) {
                log.error("未找到供应商{}的配置信息", supplierTenantId);
                continue;
            }
            for (Map.Entry<String, String> tenantIdAndOrderNo : tenantIdAndOrderNos.entrySet()) {
                // 不同租户
                String tenantId = tenantIdAndOrderNo.getKey();

                String orderNos = tenantIdAndOrderNo.getValue();
                Map<String, String> map = flagMap.get(tenantId);
                String flag = map.get("channel_flag");
                ThebizarkDelegate delegate = initDelegate(supplierTenantId);
                if (ObjectUtil.isNull(delegate)) {
                    continue;
                }
                // 查询erp
                InOrderPage inOrderPage = new InOrderPage();
                inOrderPage.setPage(1);
                inOrderPage.setSidx("id");
                inOrderPage.setSort(SortType.desc);
                inOrderPage.setChannelAccount(flag);
                inOrderPage.setOrderNos(orderNos);
                try {
                    // 能通但是没数据 下周找数据
                    PageResult<OutOrder> orderByPage = delegate.orderApi().findOrderByPage(inOrderPage);
                } catch (Exception e) {
                    log.error("查询erp异常", e);
                }

                System.out.println(1);
            }
        }
    }


    @Override
    public void updateBatchNeedCompensationOrderNos(List<String> needCompensationOrderNos) {
        // 做事务更新
        compensationJobTransaction.updateBatchNeedCompensationOrderNos(needCompensationOrderNos);
    }

    @Override
    public R testOrderV2(String name, String voJson) {
        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.TikTok);
        log.info("TikTok定时任务【拉取订单】 - 有效店铺数量 = {}", CollUtil.size(channelList));
        String nextPageToken = null;
        String allOrderJson = null;
        for (TenantSalesChannel tenantSalesChannel : channelList) {
//            PetTrove
            if(!tenantSalesChannel.getTenantId().equals("DJSN9WE")){
               continue;
            }
            XxlConfKeyValue xxlConfKey = tenantSalesChannelService.getXxlConfKey(tenantSalesChannel);
            Map<String, String> xxlValueMap = xxlConfKey.getXxlValueMap();
            if(CollUtil.isEmpty(xxlValueMap)){
                continue;
            }
            String tiktokAppKey = xxlValueMap.get(XxlConfEnum.TikTokAppKey.name());
            String tiktokAppSecret = xxlValueMap.get(XxlConfEnum.TikTokAppSecret.name());
            XxlJobSearchVO vo = JSONObject.parseObject(voJson, XxlJobSearchVO.class);
            vo.setTenantId(tenantSalesChannel.getTenantId());
            vo.setThirdChannelFlag(tenantSalesChannel.getThirdChannelFlag());
            vo.setShopId(String.valueOf(tenantSalesChannel.getId()));

//            String updateStrategy = XxlJobHelper.getJobParam();
            // 全量更新,循环调用接口直到next_token 为null时,停止调用

            Object allOrder = tikTokSupportV2.getAllOrderV2(vo, TikTokSyncOrderSearchResp.class,tiktokAppKey,tiktokAppSecret);
            allOrderJson= JSONObject.toJSONString(allOrder);
            log.info("测试订单拉取数据json:{}",JSONObject.toJSONString(allOrder));
        }
        log.info("TikTok定时任务【拉取订单】 - 结束");
        return R.ok(allOrderJson);
    }


    @Override
    public void testOrderForSpecifiedData(String name, String msg, String tenantId, String channelFlag) {
        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.TikTok);
        log.info("TikTok定时任务【拉取订单】 - 有效店铺数量 = {}", CollUtil.size(channelList));
        String nextPageToken = null;
        for (TenantSalesChannel tenantSalesChannel : channelList) {
//            PetTrove
            if(!tenantSalesChannel.getTenantId().equals(tenantId)){
                if(!tenantSalesChannel.getThirdChannelFlag().equals(channelFlag)){
                    continue;
                }
                continue;
            }
            XxlConfKeyValue xxlConfKey = tenantSalesChannelService.getXxlConfKey(tenantSalesChannel);
            Map<String, String> xxlValueMap = xxlConfKey.getXxlValueMap();
            if(CollUtil.isEmpty(xxlValueMap)){
                continue;
            }
            String tiktokAppKey = xxlValueMap.get(XxlConfEnum.TikTokAppKey.name());
            String tiktokAppSecret = xxlValueMap.get(XxlConfEnum.TikTokAppSecret.name());
            XxlJobSearchVO vo = new XxlJobSearchVO();
            vo.setTenantId(tenantId);
            vo.setThirdChannelFlag(tenantSalesChannel.getThirdChannelFlag());
            vo.setShopId(String.valueOf(tenantSalesChannel.getId()));

//            String updateStrategy = XxlJobHelper.getJobParam();
            // 全量更新,循环调用接口直到next_token 为null时,停止调用

            Object allOrder = tikTokSupportV2.getAllOrderV2ForTest(vo, TikTokSyncOrderSearchResp.class,msg,tiktokAppKey,tiktokAppSecret);

        }
        log.info("TikTok定时任务【拉取订单】 - 结束");
    }

    @Override
    public R updateBatchCompensateForAppoint(List<String> orderNos) {
        LambdaQueryWrapper<Orders> eq = new LambdaQueryWrapper<Orders>()
            .in(Orders::getExceptionCode, OrderExceptionEnum.out_of_stock_exception.getValue(),
                OrderExceptionEnum.final_delivery_fee_exception.getValue(),
                OrderExceptionEnum.measurement_anomaly.getValue())
            .eq(Orders::getLogisticsType, LogisticsTypeEnum.DropShipping)
            .eq(Orders::getDelFlag, 0);
        if(CollUtil.isNotEmpty(orderNos)){
            eq.in(Orders::getOrderNo, orderNos);
        }
        List<Orders> needCompensateOrders = TenantHelper.ignore(()->iOrdersService.list(eq));
        List<String> successOrders = TenantHelper.ignore(()->ordersService.updateBatchCompensate(needCompensateOrders));

        List<String> needCompensateOrderNos = needCompensateOrders.stream().map(Orders::getOrderNo).collect(Collectors.toList());

        List<String> failedOrderNos = needCompensateOrderNos.stream().filter(orderNo -> !successOrders.contains(orderNo)).collect(Collectors.toList());
        // 存在与needCompensateOrders但不存在于successOrders,过滤出来,以orderId为过滤标准

        log.info("批量补偿成功的订单号为：{}", JSONObject.toJSONString(successOrders));
        log.info("批量补偿失败的订单号为：{}", JSONObject.toJSONString(failedOrderNos));
        return R.ok("批量修复操作成功","批量补偿成功的订单号为："+ JSONObject.toJSONString(successOrders)
            + "----" +
            "批量补偿失败的订单号为："+ JSONObject.toJSONString(failedOrderNos));

    }
}
