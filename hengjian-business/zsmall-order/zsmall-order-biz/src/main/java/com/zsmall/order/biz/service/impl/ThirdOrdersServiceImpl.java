package com.zsmall.order.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.openapi.domain.entity.response.OpenApiTrackingResp;
import com.hengjian.openapi.service.ISysApiService;
import com.hengjian.system.domain.vo.SysOssVo;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.annotaion.Manager;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.domain.dto.*;
import com.zsmall.common.domain.resp.OpenApiOrderCancelResp;
import com.zsmall.common.enums.OpenApiEnum;
import com.zsmall.common.enums.erp.ErpExceptionEnums;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.extend.wms.kit.ThebizarkDelegate;
import com.zsmall.extend.wms.kit.ThebizarkKit;
import com.zsmall.extend.wms.model.ThebizarkBean;
import com.zsmall.extend.wms.model.order.InAttachInfo;
import com.zsmall.extend.wms.model.order.InUploadAttachment;
import com.zsmall.order.biz.anno.annotaion.OpenApiParamCheck;
import com.zsmall.order.biz.factory.ThirdOrderOperationFactory;
import com.zsmall.order.biz.service.DistributorOrderService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.ThirdOrdersApiService;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.order.CancelOrderMsg;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.order.entity.manger.IOrderManger;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.util.Base64Converter;
import com.zsmall.warehouse.entity.domain.WarehouseBizArkConfig;
import com.zsmall.warehouse.entity.iservice.IWarehouseBizArkConfigService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/11 11:52
 */
@Slf4j
@Manager
public class ThirdOrdersServiceImpl implements ThirdOrdersApiService {
    @Resource
    private OrdersServiceImpl ordersServiceImpl;
    @Resource
    OrdersService ordersService;
    @Resource
    private ISysApiService iSysApiService;
    @Resource
    private IOrderRefundService iOrderRefundService;

    @Resource
    private IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private IOrderItemService iOrderItemService;
    @Resource
    private IOrderManger iOrderManger;
    @Resource
    private IOrderItemShippingRecordService iOrderItemShippingRecordService;

    @Resource
    private DistributorOrderService distributorOrderService;

    @Resource
    private IProductSkuService iProductSkuService;
    @Resource
    private IOrderLogisticsInfoService iOrderLogisticsInfoService;

    @Resource
    private ISysTenantService iSysTenantService;
    @Resource
    private ThirdOrderOperationFactory factory;
    @Resource
    private IOrdersService iOrdersService;


    @Resource
    private IOrderAttachmentService iOrderAttachmentService;

    @Resource
    private IWarehouseBizArkConfigService iWarehouseBizArkConfigService;
    private ThebizarkDelegate initDelegate(String supplierId) {
        WarehouseBizArkConfig bizArkConfig = iWarehouseBizArkConfigService.queryBySupplierId(supplierId);
        return ThebizarkKit.create(new ThebizarkBean(bizArkConfig.getBizArkApiUrl(), bizArkConfig.getBizArkSecretKey(), bizArkConfig.getBizArkChannelAccount()));
    }
    @Override
    @OpenApiParamCheck(value = OpenApiEnum.CREATE_ORDER)
    public R tripartiteBatchEnterForOpenApi(OrderReceiveFromThirdDTO dto,String tenantId) {

        SysTenantVo sysTenantVo = TenantHelper.ignore(()->iSysTenantService.queryByTenantId(tenantId));
        R r = R.ok();
        StringBuilder errorOrderNo = new StringBuilder();
        AtomicReference<Boolean> result = new AtomicReference<>(false);

        dto.setThirdChannelFlag(sysTenantVo.getThirdChannelFlag());
        dto.setTenantId(tenantId);
        ArrayList<String> arrayList=null;
        List<String> nos = null;
        StringBuilder errorTips = new StringBuilder();
        try {
            // 目前直接在这里进行多线程操作
            Orders orders = new Orders();
            orders.setChannelType(dto.getChannelType());
            arrayList = factory.getInvokeStrategy("openOperationHandler")
                                         .formalTripartiteEntryForOpenTemplate(JSON.toJSONString(dto), orders);

        } catch (Exception e) {
            String message = e.getMessage();
            Integer code = ErpExceptionEnums.ALREADY_EXIST.getCode();
            errorTips = new StringBuilder(message);
            if (!result.get() && !String.valueOf(code).equals(message)) {
                errorOrderNo.append(dto.getOrderNo());
            }
            e.printStackTrace();
        } finally {
            result.set(false);

        }

        if(ObjectUtil.isNotNull(arrayList) ){
            nos = arrayList.stream().distinct().collect(Collectors.toList());
            String join = String.join(",", nos);
            r.setSubCode("200");
            r.setData(join);
        }
        // 判断订单录入成功后是否存在错误信息
        if(CollUtil.isNotEmpty(nos)){
            // 非 DF9FEOG 租户 不给错误提示
            if(tenantId.equals("DF9FEOG")){
                if (StringUtils.isNotEmpty(errorOrderNo)) {
                    r.setSubCode("409");
                    r.setMsg(Objects.requireNonNull(errorTips).toString());
//            r.setData(errorOrderNo);
                }
                for (String orderNo : nos) {

                    List<Orders> orders= new ArrayList<>();
                    orders = iOrdersService.getByOrderExtendId(orderNo);
                    for (Orders orders1 : orders) {
                        errorTopsMsg(orders1, errorTips);
                    }
                }
                if (StringUtils.isNotEmpty(errorTips)){
                    r.setSubCode("409");
                    r.setMsg(errorTips.toString());
                }
            }else {
                r.setSubCode("200");
            }
        }else {
            // 录入失败的提示
            if (StringUtils.isNotEmpty(errorTips)){
                r.setCode(500);
                r.setSubCode("409");
                r.setMsg(errorTips.toString());
            }
        }
        return r;
    }
    /**
     * 功能描述：取消订单流,仅支持单条
     * 1.订单取消中 2.进入事务 2.1 调用erp接口,取消订单 2.2-success 成功后,进入退款流程 2.2-false 失败,变更状态为取消失败,输出失败原因 需要注意LTL合单情况,取消需要都取消
     * @param nos       订单号集合
     * @param dTenantId d租户id
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/14
     */
    @Override
//    @OrderRefundLimit(timeUnit = TimeUnit.MINUTES,interval=2,value = CancelOrderApiEnum.OPEN_API_CANCEL_ORDER_FLOW)
    public R cancelOrderFlow(List<String> nos, String dTenantId) {
        if(CollUtil.isEmpty(nos)||ObjectUtil.isEmpty(dTenantId)){
            throw new RuntimeException("请检查订单号和租户id");
        }
        String orderExtendId = nos.get(0);
        List<Orders> orders = iOrdersService.getByOrderExtendId(orderExtendId);
        if(CollUtil.isEmpty(orders)){
            throw new RuntimeException("请检查订单号");
        }
        List<String> orderNos = orders.stream().map(Orders::getOrderNo).collect(Collectors.toList());
        List<OrderRefund> orderRefunds = iOrderRefundService.listByOrderNoNoTenant(orderNos);
        List<String> refundedOrderNos = null;
        if(CollUtil.isNotEmpty(orderRefunds)){
            refundedOrderNos = orderRefunds.stream().map(OrderRefund::getOrderNo).collect(Collectors.toList());
        }
        List<String> reasons = new ArrayList<>();
        for (Orders order : orders) {
            LogisticsProgress fulfillmentProgress = order.getFulfillmentProgress();
            Integer cancelStatus = order.getCancelStatus();
            if (!LogisticsProgress.UnDispatched.equals(fulfillmentProgress)&&!LogisticsProgress.Abnormal.equals(fulfillmentProgress)) {
                reasons.add("物流状态为[" + fulfillmentProgress.name() + "]，不支持取消");
            }

            if (!OrderCancelStateEnum.None.getValue().equals(cancelStatus)
                && !OrderCancelStateEnum.Abnormal.getValue().equals(cancelStatus)) {
                String statusDesc = OrderCancelStateEnum.getDisplayName(cancelStatus);
                reasons.add("订单当前取消状态（" + statusDesc + "）,不支持取消");
            }

            if(CollUtil.isNotEmpty(refundedOrderNos)){
                boolean contains = refundedOrderNos.contains(order.getOrderNo());
                if (contains){
                    reasons.add("订单已有取消记录,不支持再次取消");
                }
            }
//            if (!OrderStateType.Paid.equals(order.getOrderState())) {
//                reasons.add("订单（当前支付状态：" + order.getOrderState() + "）不支持取消");
//            }
        }
        if (!reasons.isEmpty()) {
            String errorMsg = "订单无法取消：\n▶ " + String.join("\n▶ ", reasons);
            throw new RuntimeException(errorMsg);
        }
        // orderExtendId 根据orderExtendId ,校验是否已支付,已支付的走已支付退款逻辑,未支付的走未支付退款逻辑
        Boolean isPaid = iOrdersService.isAllPaid(orderExtendId);
        if(isPaid){
            String supplierTenantId = TenantHelper.ignore(()->iOrdersService.getSupplierIdByOrderNo(orderExtendId));
            iOrderManger.cancelOrderFlow(nos,dTenantId,supplierTenantId);
        }else {
            iOrderManger.cancelOrderForNotPay(orderExtendId);
        }

        return R.ok("订单取消中");
    }
    @Override
    @OpenApiParamCheck(value = OpenApiEnum.RECEIVE_ORDER_ATTACHMENT)
    @Transactional(rollbackFor = Exception.class)
    public R receiveOrderAttachment(List<OrderReceiveAttachmentDTO> dtos, String tenantId) {
        OrderReceiveAttachmentDTO orderReceiveAttachmentDTO = dtos.get(0);
        String orderNo = orderReceiveAttachmentDTO.getOrderNo();
        log.info("附件更新单号:{}",orderNo);
        for (OrderReceiveAttachmentDTO dto : dtos) {
            // 这个orderNo实际上是orderExtendId
            String orderExtendId = dto.getOrderNo();
//            String finalOrderNo = orderNo;
            List<OrderAttachmentDTO> attachments = dto.getAttachments();
            List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.getByOrderExtendId(orderExtendId));
            // 如果是LTL的单子,要拿orderExtendId

            List<Long> orderIds = ordersList.stream().map(Orders::getId).collect(Collectors.toList());
            LambdaQueryWrapper<OrderItem> orderItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderItemLambdaQueryWrapper.in(OrderItem::getOrderId, orderIds);
            List<OrderItem> orderItems = TenantHelper.ignore(() -> iOrderItemService.list(orderItemLambdaQueryWrapper));
            Orders order = ordersList.get(0);
            for (Orders orders : ordersList) {
                if (LogisticsProgress.Dispatched.equals(orders.getFulfillmentProgress())) {
                    throw new RuntimeException("订单已发货,不允许上传附件,订单号:"+orders.getOrderExtendId());

                }
            }


            distributorOrderService.updateOrderAttachmentIncludeAll(orderExtendId,attachments);
            try{
                String productSkuCode = orderItems.get(0).getProductSkuCode();
                LambdaQueryWrapper<ProductSku> skuLambdaQueryWrapper = new LambdaQueryWrapper<>();
                skuLambdaQueryWrapper.eq(ProductSku::getProductSkuCode, productSkuCode);
                ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getOne(skuLambdaQueryWrapper));
                String supplierId = productSku.getTenantId();
                // 推送附件
                InUploadAttachment inUploadAttachment = new InUploadAttachment();
                inUploadAttachment.setOrderNo(orderExtendId);

                ArrayList<InAttachInfo>  inAttachInfos= new ArrayList<>();
                ThebizarkDelegate delegate = initDelegate(supplierId);
                for (OrderAttachmentDTO attachment : attachments) {
                    InAttachInfo inAttachInfo = new InAttachInfo();
                    inAttachInfo.setName(attachment.getName());
                    inAttachInfo.setUrl(attachment.getUrl());
                    inAttachInfo.setRemark(attachment.getRemark());
                    Integer fileType = attachment.getFileType();

                    if(OrderAttachmentTypeEnum.ShippingLabel.getCode().equals(fileType)){
                        inAttachInfo.setType(0);
                    }
                    if(OrderAttachmentTypeEnum.BOL.getCode().equals(fileType)){
                        inAttachInfo.setType(2);
                    }
                    if(OrderAttachmentTypeEnum.CartonLabel.getCode().equals(fileType)){
                        inAttachInfo.setType(4);
                    }
                    if(OrderAttachmentTypeEnum.PalletLabel.getCode().equals(fileType)){
                        inAttachInfo.setType(5);
                    }
                    if(OrderAttachmentTypeEnum.ItemLabel.getCode().equals(fileType)){
                        inAttachInfo.setType(6);
                    }
                    inAttachInfos.add(inAttachInfo);
                }
                // 历史已经有的附件要带上

                LambdaQueryWrapper<OrderAttachment> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(OrderAttachment::getOrderNo,order.getOrderNo());
                wrapper.ne(OrderAttachment::getAttachmentType,OrderAttachmentTypeEnum.BOL);
                wrapper.eq(OrderAttachment::getDelFlag, 0);
                List<OrderAttachment> orderAttachments = TenantHelper.ignore(() -> iOrderAttachmentService.list(wrapper));
                for (OrderAttachment attachment : orderAttachments) {
                    InAttachInfo inAttachInfo = new InAttachInfo();
                    inAttachInfo.setName(attachment.getAttachmentName());
                    inAttachInfo.setUrl(attachment.getAttachmentShowUrl());

                    OrderAttachmentTypeEnum fileType = attachment.getAttachmentType();

                    if(OrderAttachmentTypeEnum.ShippingLabel.equals(fileType)){
                        inAttachInfo.setType(0);
                    }
                    if(OrderAttachmentTypeEnum.BOL.equals(fileType)){
                        inAttachInfo.setType(2);
                    }
                    if(OrderAttachmentTypeEnum.CartonLabel.equals(fileType)){
                        inAttachInfo.setType(4);
                    }
                    if(OrderAttachmentTypeEnum.PalletLabel.equals(fileType)){
                        inAttachInfo.setType(5);
                    }
                    if(OrderAttachmentTypeEnum.ItemLabel.equals(fileType)){
                        inAttachInfo.setType(6);
                    }
                    inAttachInfos.add(inAttachInfo);
                }
                if(ObjectUtil.isNotEmpty(delegate)){
                    inUploadAttachment.setAttachInfoItemRequestList(inAttachInfos);
                    delegate.orderApi().createAttachment(inUploadAttachment);
                }
            }catch (Exception e){
                // 推送erp异常
                log.error("推送erp异常",e);
                return  R.fail("附件更新erp失败");
            }

        }
        return R.ok();
    }

    @Override
    public R getTrackingRecord(List<String> channelNos, String tenantId) {
        List<Orders>orders = iOrdersService.getListByChannelOrderNoAndTenantId(channelNos,tenantId);

        if(CollUtil.isEmpty(orders)){
            return R.fail("The order does not exist");
        }else {
            List<String> list = orders.stream().map(Orders::getChannelOrderNo).collect(Collectors.toList());
            // list是否包含所有的channelNos,不包含则筛出不包含的channelNo
            List<String> collect = channelNos.stream().filter(channelNo -> !list.contains(channelNo)).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(collect)){
                return R.fail("The order does not exist"+collect);
            }
        }
//        OpenApiTrackingResp
        HashMap<String,List<OpenApiTrackingResp>> channelAndTrackingMap = new HashMap();
        StringBuilder msg = new StringBuilder();
//        orders 转换为map,key为channelOrderNo,value为List<Orders>
        Map<String, List<Orders>> ordersMap = orders.stream()
                                                    .filter(order -> order.getChannelOrderNo() != null)
                                                    .collect(Collectors.groupingBy(Orders::getChannelOrderNo));
        for (Map.Entry<String,  List<Orders>> entry : ordersMap.entrySet()){
            String channelOrderNo = entry.getKey();
            List<Orders> ordersList = entry.getValue();
            List<String> nos = ordersList.stream().map(Orders::getOrderNo).collect(Collectors.toList());
            List<OrderItemTrackingRecord> trackingRecords = iOrderItemTrackingRecordService.getListByOrdersHasCompleted(nos);
            List<OpenApiTrackingResp> respList = new ArrayList<>();
            trackingRecords.forEach(trackingRecord -> {
                if(CollUtil.isEmpty(trackingRecords)||trackingRecords.size() < nos.size()){
                    // 返回key-value结构吧还是
                }else if(CollUtil.isNotEmpty(trackingRecords)&&trackingRecords.size() == nos.size()){
                    OpenApiTrackingResp openApiTrackingResp = new OpenApiTrackingResp();
                    openApiTrackingResp.setTracking(trackingRecord.getLogisticsTrackingNo());
                    openApiTrackingResp.setCarrier(trackingRecord.getLogisticsCarrier());
                    openApiTrackingResp.setNum(trackingRecord.getQuantity());
                    openApiTrackingResp.setSku(trackingRecord.getSku());
                    openApiTrackingResp.setSkuId(trackingRecord.getProductSkuCode());
                    openApiTrackingResp.setWarehouseCode(trackingRecord.getWarehouseCode());
                    openApiTrackingResp.setDeliveryTime(trackingRecord.getDispatchedTime());
                    openApiTrackingResp.setChannelOrderNo(channelOrderNo);
                    respList.add(openApiTrackingResp);
                }
            });
            channelAndTrackingMap.put(channelOrderNo,respList);
        }
        if (CollUtil.isNotEmpty(channelAndTrackingMap)){
            msg.append(JSON.toJSON(channelAndTrackingMap));
            return R.ok(msg);
        }else {
            return R.fail("The tracking information has not been filled in yet, please wait patiently;");
        }
    }

    @Override
    @OpenApiParamCheck(value = OpenApiEnum.RECEIVE_ORDER_ATTACHMENT)
    @Transactional(rollbackFor = Exception.class)
    public R receiveAllOrderAttachment(List<OrderReceiveAttachmentDTO> dtos, String tenantId) {
        for (OrderReceiveAttachmentDTO dto : dtos) {
            // 这个orderNo实际上是orderExtendId
            String orderExtendId = dto.getOrderNo();
//            String finalOrderNo = orderNo;
            List<OrderAttachmentDTO> attachments = dto.getAttachments();
            List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.getByOrderExtendId(orderExtendId));
            // 如果是LTL的单子,要拿orderExtendId
            List<Long> orderIds = ordersList.stream().map(Orders::getId).collect(Collectors.toList());
            LambdaQueryWrapper<OrderItem> orderItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderItemLambdaQueryWrapper.in(OrderItem::getOrderId, orderIds);
            List<OrderItem> orderItems = TenantHelper.ignore(() -> iOrderItemService.list(orderItemLambdaQueryWrapper));
            Orders order = ordersList.get(0);
            for (Orders orders : ordersList) {
                if (LogisticsProgress.Dispatched.equals(orders.getFulfillmentProgress())) {
                    throw new RuntimeException("订单已发货,不允许上传附件,订单号:"+orders.getOrderExtendId());

                }
            }
            Set<Long> attachmentIds = distributorOrderService.updateOrderAttachmentIncludeAll(orderExtendId, attachments);
            // 分别调用附件创建接口 不允许多个供应商
            String productSkuCode = orderItems.get(0).getProductSkuCode();
            LambdaQueryWrapper<ProductSku> skuLambdaQueryWrapper = new LambdaQueryWrapper<>();
            skuLambdaQueryWrapper.eq(ProductSku::getProductSkuCode, productSkuCode);
            ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getOne(skuLambdaQueryWrapper));
            String supplierId = productSku.getTenantId();
            // 推送附件
            InUploadAttachment inUploadAttachment = new InUploadAttachment();
            inUploadAttachment.setOrderNo(orderExtendId);

            ArrayList<InAttachInfo>  inAttachInfos= new ArrayList<>();
            ThebizarkDelegate delegate = initDelegate(supplierId);
            for (OrderAttachmentDTO attachment : attachments) {
                InAttachInfo inAttachInfo = new InAttachInfo();
                inAttachInfo.setName(attachment.getName());
                inAttachInfo.setUrl(attachment.getUrl());
                inAttachInfo.setRemark(attachment.getRemark());
                Integer fileType = attachment.getFileType();
                if(OrderAttachmentTypeEnum.ShippingLabel.getCode().equals(fileType)){
                    inAttachInfo.setType(0);
                }
                if(OrderAttachmentTypeEnum.BOL.getCode().equals(fileType)){
                    inAttachInfo.setType(2);
                }
                if(OrderAttachmentTypeEnum.CartonLabel.getCode().equals(fileType)){
                    inAttachInfo.setType(4);
                }
                if(OrderAttachmentTypeEnum.PalletLabel.getCode().equals(fileType)){
                    inAttachInfo.setType(5);
                }
                if(OrderAttachmentTypeEnum.ItemLabel.getCode().equals(fileType)){
                    inAttachInfo.setType(6);
                }
                if(OrderAttachmentTypeEnum.Other.getCode().equals(fileType)){
                    inAttachInfo.setType(3);
                }
                inAttachInfos.add(inAttachInfo);
            }
            // 历史已经有的附件要带上

            LambdaQueryWrapper<OrderAttachment> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderAttachment::getOrderNo,order.getOrderNo());
            wrapper.ne(OrderAttachment::getAttachmentType,OrderAttachmentTypeEnum.BOL);
            // 要去掉新增的附件,上面已经加过了
            wrapper.notIn(!attachmentIds.isEmpty(),OrderAttachment::getId,attachmentIds);
            wrapper.eq(OrderAttachment::getDelFlag, 0);
            List<OrderAttachment> orderAttachments = TenantHelper.ignore(() -> iOrderAttachmentService.list(wrapper));
            for (OrderAttachment attachment : orderAttachments) {
                InAttachInfo inAttachInfo = new InAttachInfo();
                inAttachInfo.setName(attachment.getAttachmentName());
                inAttachInfo.setUrl(attachment.getAttachmentShowUrl());
                OrderAttachmentTypeEnum fileType = attachment.getAttachmentType();

                if(OrderAttachmentTypeEnum.ShippingLabel.equals(fileType)){
                    inAttachInfo.setType(0);
                }
                if(OrderAttachmentTypeEnum.BOL.equals(fileType)){
                    inAttachInfo.setType(2);
                }
                if(OrderAttachmentTypeEnum.CartonLabel.equals(fileType)){
                    inAttachInfo.setType(4);
                }
                if(OrderAttachmentTypeEnum.PalletLabel.equals(fileType)){
                    inAttachInfo.setType(5);
                }
                if(OrderAttachmentTypeEnum.ItemLabel.equals(fileType)){
                    inAttachInfo.setType(6);
                }
                if(OrderAttachmentTypeEnum.Other.equals(fileType)){
                    inAttachInfo.setType(3);
                }
                inAttachInfos.add(inAttachInfo);
            }
            inUploadAttachment.setAttachInfoItemRequestList(inAttachInfos);
            delegate.orderApi().createAttachment(inUploadAttachment);
        }
        return R.ok();
    }
    @Override
    public R getCancelOrderStatus(OpenApiOrderCancelDTO thirdDTO, String tenantId) {
        String orderNo = thirdDTO.getOrderNo();

        List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.getByOrderExtendId(orderNo));
        OpenApiOrderCancelResp openApiOrderCancelResp = new OpenApiOrderCancelResp();


        if(CollUtil.isEmpty(ordersList)){
            throw new RuntimeException("Abnormal order number");
        }
        Orders order = ordersList.get(0);
        String orderExtendId = order.getOrderExtendId();
        String channelOrderNo = order.getChannelOrderNo();

        openApiOrderCancelResp.setOrderNo(orderExtendId);
        openApiOrderCancelResp.setChannelOrderNo(channelOrderNo);
        StringBuilder sb = new StringBuilder();
        //        cancelStatus : 1 取消中 2 取消成功 3 取消失败 4 存在拆单情况,订单退款状态不一致,请手动核实订单状况 所以需要有一个set帮我记录子订单状况,然后判断后再放入openApiOrderCancelResp.setCancelStatus();内

        Set<Integer> statusSet = new HashSet<>();
        Integer statusCode = order.getCancelStatus();
        for (Orders orders : ordersList) {
            Integer code = orders.getCancelStatus();
            statusSet.add(code);
        }
        // 状态不一致则为取消中
        if(statusSet.isEmpty()){
            throw new RuntimeException("订单取消状态异常,请联系管理员");
        }
        if(statusSet.size()>1){
            sb.append("订单取消中;");
        }else {
            OrderCancelStateDescEnum state = OrderCancelStateDescEnum.fromCode(statusCode);
            sb.append("订单").append(state.getDesc());
        }
//       todo 后续优化  cancelStatus : 1 取消中 2 取消成功 3 取消失败 4 存在拆单情况,订单退款状态不一致,请手动核实订单状况
        openApiOrderCancelResp.setCancelStatus(statusCode);
        openApiOrderCancelResp.setCancelReason(sb.toString());
        return R.ok(openApiOrderCancelResp);
    }
    private static boolean isValidOrder(Orders order) {
        return order != null
            && (order.getFulfillmentProgress() == LogisticsProgress.UnDispatched ||order.getFulfillmentProgress() == LogisticsProgress.Abnormal)
            && order.getCancelStatus() == 1
            && order.getOrderState() != OrderStateType.Failed
            && order.getOrderState() != OrderStateType.UnPaid;
    }
    @Override
    public R orderReviewFlowForSupplier(OpenApiOrderReviewDTO openApiOrderReviewDTO, String tenantId) throws Exception {
        SysTenantVo sysTenantVo = iSysTenantService.queryByTenantId(tenantId);
        String tenantType = sysTenantVo.getTenantType();

        String orderExtendId = openApiOrderReviewDTO.getOrderNo();
        Integer orderReviewOpinion = openApiOrderReviewDTO.getOrderReviewOpinion();
        if(StrUtil.isBlank(orderExtendId)||ObjectUtil.isEmpty(orderReviewOpinion)){
            throw new RuntimeException("请检查你的参数");
        }

        if(!TenantType.Supplier.name().equals(tenantType)){
            return R.fail("该功能仅支持供应商使用");
        }


        List<Orders> orders = iOrdersService.getByOrderExtendId(orderExtendId);
        List<String> orderNos = orders.stream().map(Orders::getOrderNo).collect(Collectors.toList());
        // 需要查看order_item,验证是否是其分销商订单
        // 遍历itemList 如果其中元素的supplierTenantId 不为指定tenantId则报错
        List<OrderItem> itemList = iOrderItemService.getList(orderNos);

        List<OrderItem> invalidItems = itemList.stream()
                                               .filter(item -> !tenantId.equals(item.getSupplierTenantId()))
                                               .collect(Collectors.toList());

        if (!invalidItems.isEmpty()) {
            throw new IllegalArgumentException("存在非法供应商的订单项！数量: " + invalidItems.size() +
                " 目标租户ID: " + tenantId +
                " 违规列表: " + invalidItems);
        }
        // orders内的元素的fulfillmentProgress 必须是LogisticsProgress、UnDispatched,cancelOrderStatus 必须是 1,否则报错
        List<OrderRefund> orderRefunds = ordersServiceImpl.getOrderRefundsByOrderExtendId(orderExtendId);
        // orderRefunds.size()<orders.size() =订单尚未发起退款, orderRefunds.size()>orders.size() =订单退款异常
        if(orders.size()<orderRefunds.size()){
            throw new RuntimeException("订单尚未发起取消,请核实");
        }
        if(orders.size()>orderRefunds.size()){
            throw new RuntimeException("子订单业务状态不一致,无法取消");
        }
        List<String> orderRefundNos = orderRefunds.stream().map(OrderRefund::getOrderRefundNo)
                                                  .collect(Collectors.toList());
        List<Orders> invalidOrders = orders.stream()
                                           .filter(order -> !isValidOrder(order))
                                           .limit(1)
                                           .collect(Collectors.toList());
        //todo 校验退款状态
        if (!invalidOrders.isEmpty()) {
            Orders invalid = invalidOrders.get(0);
            throw new IllegalStateException(String.format(
                "订单 [ID: %s] 状态非法: fulfillmentProgress=%s, cancelOrderStatus=%s，orderState=%s",
                invalid.getOrderNo(),
                invalid.getFulfillmentProgress(),
                OrderCancelStateDescEnum.fromCode(invalid.getCancelStatus()).getDesc(),
                OrderStateType.getByName(invalid.getOrderState().name()).getValue()
            ));
        }
        Orders orders1 = orders.get(0);
        LogisticsProgress fulfillmentProgress = orders1.getFulfillmentProgress();
        Boolean isAbnormal = LogisticsProgress.Abnormal.equals(fulfillmentProgress);
        //取消成功
        if (orderReviewOpinion == 0) {
            ordersServiceImpl.agreeSuccess(orderRefundNos,orderExtendId, true,isAbnormal);
            return R.ok(ZSMallStatusCodeEnum.OPEN_ORDER_REVIEW_CONSENT_SUCCESS);
        }
        //取消失败,审批成失败
        if (orderReviewOpinion == 1) {
            if (CollUtil.isNotEmpty(orderRefundNos)) {
                ordersServiceImpl.refuseCancel(orderRefundNos,orderExtendId, true);
            }
            return R.ok(ZSMallStatusCodeEnum.OPEN_ORDER_REVIEW_REFUSE_SUCCESS);
        }
        return R.fail();
    }

    @Override
    public void abnormalOrderCancelAutoReviewSuccess(CancelOrderMsg cancelOrderMsg) {
        String orderNo = cancelOrderMsg.getOrderNo();
        List<Orders> orders = iOrdersService.listByOrderNo(orderNo);
        List<OrderItem> itemList = iOrderItemService.getList(Collections.singletonList(orderNo));
        OrderItem item = itemList.get(0);
        String tenantId = item.getTenantId();
        String supplierTenantId = item.getSupplierTenantId();
        if(CollUtil.isNotEmpty(orders)){
            Orders order = orders.get(0);
            String orderExtendId = order.getOrderExtendId();
            if (ObjectUtil.isEmpty(orderNo) ) {
                throw new RuntimeException("请检查订单号和租户id");
            }
            RedissonClient client = RedisUtils.getClient();
            String lockKey = RedisConstants.CANCEL_ORDER_LOCK + ":" + orderExtendId;
            RLock rLock = client.getLock(lockKey);
            try {
                // 尝试立即获取锁，锁持有时间5分钟（根据业务调整）
                boolean isLockAcquired = rLock.tryLock(0, 10, TimeUnit.MINUTES);
                if (!isLockAcquired) {
                    throw new RuntimeException("订单正在处理中，请勿重复操作");
                }

                log.info("ab取消业务,取消订单:{}",orderExtendId);
                // 订单取消
                ordersService.cancelOrder(orderExtendId);
                // 判断是否是自动化,不是自动化的,需要手动供应商审批 ,todo 如果发货异常,那么erp都没有接收到这个单子,实际上是肯定不应该走自动取消的流程的,应该走默认审批取消的流程
                boolean isNotExist = TenantHelper.ignore(()->iSysApiService.isNotExist(supplierTenantId));
                if (isNotExist) {
//                    走供应商自动退款流程
                    OpenApiOrderReviewDTO openApiOrderReviewDTO = new OpenApiOrderReviewDTO();
                    openApiOrderReviewDTO.setTenantId(supplierTenantId);
                    openApiOrderReviewDTO.setOrderReviewOpinion(0);
                    openApiOrderReviewDTO.setOrderNo(orderExtendId);
                    orderReviewFlowForSupplier(openApiOrderReviewDTO,openApiOrderReviewDTO.getTenantId());
                }else {
                    // 如果是自动化的 卡在取消失败,则进入重试的job流程
                }


            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                // 确保当前线程持有锁时才释放（避免误释放其他线程的锁）
                if (rLock.isHeldByCurrentThread() && rLock.isLocked()) {
                    rLock.unlock();
                }
            }
        }
    }

    @Override
    public R getAttachmentUrlByBase64(OpenApiAttachmentConvertDTO thirdDTO, String tenantId) {
        String base64Code = thirdDTO.getBase64Code();
        String fileName = thirdDTO.getFileName();
        // base64Code转换成multipartFile
        MultipartFile multipartFile = Base64Converter.toMultipartFileSafe(base64Code, fileName);
        OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
        SpringUtils.context().publishEvent(ossUploadEvent);
        SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
        String url = sysOssVo.getUrl();
        return R.ok("上传成功",url);
    }

    private static void errorTopsMsg(Orders order, StringBuilder errorTips) {
        if (ObjectUtil.isNotEmpty(order.getPayErrorMessage())){
            JSONObject payErrorMessage = order.getPayErrorMessage();
            String o = (String) payErrorMessage.get("zh_CN");
            errorTips.append(o);
        }
    }
}
