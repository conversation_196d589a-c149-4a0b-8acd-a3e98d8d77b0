package com.zsmall.order.biz.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.thoughtworks.xstream.XStream;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.AmazonScOrderDTO;
import com.zsmall.common.domain.dto.AmazonScOrderItemDTO;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.common.handler.AbstractOrderOperationHandler;
import com.zsmall.common.util.RegexUtil;
import com.zsmall.common.util.UnitConverter;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.order.biz.service.OrderItemPriceService;
import com.zsmall.order.biz.service.OrderItemService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.impl.OrderItemProductSkuThirdServiceImpl;
import com.zsmall.order.biz.service.impl.OrderItemThirdServiceImpl;
import com.zsmall.order.biz.support.OrderActivitySupport;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.order.OrderPayBo;
import com.zsmall.order.entity.domain.dto.AmazonVCOrderLogisticsAttachmentDTO;
import com.zsmall.order.entity.domain.dto.AmazonVCOrderLogisticsAttachmentItemDTO;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.order.entity.mapper.ThirdOrderInfoItemMapper;
import com.zsmall.order.entity.mapper.ThirdOrderInfoMapper;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAdjustStockVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年8月2日  11:52
 * @description: amazon sc 订单处理
 */
@Slf4j
@Lazy
@Component("amazonScOrderOperationHandler")
public class AmazonScOrderOperationHandler extends AbstractOrderOperationHandler<String, AmazonScOrderDTO, Map<String, Object>, Orders, Object> {

    @Resource
    ThirdOrderInfoMapper thirdOrderInfoMapper;

    @Resource
    ThirdOrderInfoItemMapper thirdOrderInfoItemMapper;

    @Resource
    private IProductMappingService iProductMappingService;
    @Value("${distribution.specify.warehouse.id.bizArk}")
    public String warehouseSystemCode;
    @Resource
    private OrderItemService orderItemService;
    @Resource
    private OrderSupport orderSupport;
    @Resource
    private IProductSkuService iProductSkuService;
    @Resource
    private IProductService iProductService;
    @Resource
    private IOrdersService iOrdersService;
    @Resource
    private OrdersService ordersService;
    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private IProductChannelControlService iProductChannelControlService;
    @Autowired
    ApplicationContext applicationContext;
    @Resource
    private OrderCodeGenerator orderCodeGenerator;
    @Resource
    private IOrderItemService iOrderItemService;
    @Resource
    private OrderItemThirdServiceImpl iOrderItemThirdService;
    @Resource
    private IOrderItemProductSkuService iOrderItemProductSkuService;
    @Resource
    private IOrderLogisticsInfoService iOrderLogisticsInfoService;
    @Resource
    private IOrderAddressInfoService iOrderAddressInfoService;
    @Resource
    private OrderItemProductSkuThirdServiceImpl orderItemProductSkuThirdService;
    @Resource
    private PriceSupportV2 priceSupportV2;
    @Resource
    private OrderItemPriceService orderItemPriceService;
    @Resource
    private IProductSkuPriceService iProductSkuPriceService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private IProductSkuDetailService iProductSkuDetailService;
    @Resource
    private ISiteCountryCurrencyService iSiteCountryCurrencyService;
    @Resource
    private OrderActivitySupport orderActivitySupport;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void initialMessageSave(AmazonScOrderDTO amazonScOrderDTO) {
        List<ThirdOrderInfo> thirdOrderInfoList = new ArrayList<>();
        List<ThirdOrderInfoItem> thirdOrderInfoItemList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(amazonScOrderDTO)){
            ThirdOrderInfo thirdOrderInfo = new ThirdOrderInfo();
            thirdOrderInfo.setAccountId(amazonScOrderDTO.getAccount_id()).setOrderNum(amazonScOrderDTO.getOrdernum()).setAmount(amazonScOrderDTO.getAmount()).setCurrencyCode(amazonScOrderDTO.getCurrency_code())
                          .setStatus(amazonScOrderDTO.getStatus()).setShippingStatus(amazonScOrderDTO.getShipping_status()).setBuyName(amazonScOrderDTO.getBuy_name()).setBuyEmail(amazonScOrderDTO.getBuy_email())
                          .setDisplaySeller(amazonScOrderDTO.getDisplay_seller()).setFulfillmentChannel(amazonScOrderDTO.getFulfillment_channel()).setShipServiceLevel(amazonScOrderDTO.getShip_service_level())
                          .setShippingName(amazonScOrderDTO.getShipping_name()).setAddressline1(amazonScOrderDTO.getAddressline1()).setAddressline2(amazonScOrderDTO.getAddressline2())
                          .setCity(amazonScOrderDTO.getCity()).setStateOrRegion(amazonScOrderDTO.getState_or_region()).setCountry(amazonScOrderDTO.getCountry()).setCountryCode(amazonScOrderDTO.getCountry_code())
                          .setPostalCode(amazonScOrderDTO.getPostal_code()).setPhone(amazonScOrderDTO.getPhone()).setCreated(amazonScOrderDTO.getCreated()).setPaymentDate(amazonScOrderDTO.getPayment_date())
                          .setUpdated(amazonScOrderDTO.getUpdated()).setLatestShipDate(amazonScOrderDTO.getLatest_ship_date()).setEarliestShipDate(amazonScOrderDTO.getEarliest_ship_date())
                          .setIsBusinessOrder(amazonScOrderDTO.getIs_business_order()).setIsPrime(amazonScOrderDTO.getIs_prime())
                          .setDisplay_ordernum(amazonScOrderDTO.getDisplay_ordernum()).setCreateTime(LocalDateTime.now());
            thirdOrderInfo.setTenantId(amazonScOrderDTO.getTenantId());
            thirdOrderInfoList.add(thirdOrderInfo);
            if(CollUtil.isNotEmpty(amazonScOrderDTO.getItems())){
                List<AmazonScOrderItemDTO> items = amazonScOrderDTO.getItems();
                for(AmazonScOrderItemDTO amazonScOrderItemDTO : items){
                    ThirdOrderInfoItem thirdOrderInfoItem = new ThirdOrderInfoItem();
                    thirdOrderInfoItem.setAccountId(amazonScOrderDTO.getAccount_id()).setOrderNum(amazonScOrderDTO.getOrdernum()).setCreateTime(LocalDateTime.now());
                    thirdOrderInfoItem.setSku(amazonScOrderItemDTO.getSku()).setAsin(amazonScOrderItemDTO.getAsin()).setTitle(amazonScOrderItemDTO.getTitle()).setQuantity(amazonScOrderItemDTO.getQuantity())
                        .setSubtotal(amazonScOrderItemDTO.getSubtotal()).setCurrencyCode(amazonScOrderItemDTO.getCurrency_code()).setShipedQuantity(amazonScOrderItemDTO.getShiped_quantity()).setShippingPrice(amazonScOrderItemDTO.getShipping_price())
                        .setShippingTaxPrice(amazonScOrderItemDTO.getShipping_tax_price()).setShippingDiscountPrice(amazonScOrderItemDTO.getShipping_discount_price()).setTaxPrice(amazonScOrderItemDTO.getTax_price()).setPromotionDiscountPrice(amazonScOrderItemDTO.getPromotion_discount_price())
                        .setPromotion_ids(amazonScOrderItemDTO.getPromotion_ids()).setGiftWrapPrice(amazonScOrderItemDTO.getGift_wrap_price()).setGiftWrapTaxPrice(amazonScOrderItemDTO.getGift_wrap_tax_price()).setChannelOrderItemId(amazonScOrderItemDTO.getChannel_order_item_id());
                    thirdOrderInfoItem.setTenantId(amazonScOrderDTO.getTenantId());
                    thirdOrderInfoItemList.add(thirdOrderInfoItem);
                }
            }
            if(CollUtil.isNotEmpty(thirdOrderInfoList)){
                thirdOrderInfoMapper.insertBatch(thirdOrderInfoList);
            }
            if (CollUtil.isNotEmpty(thirdOrderInfoItemList)){
                thirdOrderInfoItemMapper.insertBatch(thirdOrderInfoItemList);
            }
        }
    }

    @Override
    public AmazonScOrderDTO parseThirdData(String json) {
        XStream xStream = new XStream();
        xStream.registerConverter(new CustomBigDecimalConverter());
        xStream.alias("OrderHeader", AmazonScOrderDTO.class);
        xStream.alias("OrderItem", AmazonScOrderItemDTO.class);
        AmazonScOrderDTO amazonScOrderDTO = (AmazonScOrderDTO) xStream.fromXML(json);
        if (ObjectUtil.isEmpty(amazonScOrderDTO)) {
            throw new AppRuntimeException("订单数据不能为null");
        }
        // 保存报文信息
        try {
            initialMessageSave(amazonScOrderDTO);
        }catch (Exception e){
            log.error("持久化amazonSC订单信息报错:{}",e.getMessage());
        }
        if(StringUtils.isNotEmpty(amazonScOrderDTO.getStatus()) && !amazonScOrderDTO.getStatus().equals("unshipped")){
            return null;
        }
        TenantSalesChannel tenantSalesChannelByChannelName = iTenantSalesChannelService.getTenantSalesChannelByChannelName(amazonScOrderDTO.getAccount_id(),ChannelTypeEnum.Amazon_SC.name());
        if(ObjectUtil.isEmpty(tenantSalesChannelByChannelName)){
            log.error("亚马逊SC订单：{} ,没有渠道信息",json);
            throw new AppRuntimeException("没有对应的渠道信息");
        }
        if(StringUtils.isEmpty(tenantSalesChannelByChannelName.getTenantId())){
            log.error("亚马逊SC订单：{} ,查询到渠道信息没有租户id",json);
            throw new AppRuntimeException("渠道:"+tenantSalesChannelByChannelName.getTenantId()+" 没有租户信息");
        }
        amazonScOrderDTO.setTenantId(tenantSalesChannelByChannelName.getTenantId());
        amazonScOrderDTO.setChannelType(ChannelTypeEnum.valueOf(tenantSalesChannelByChannelName.getChannelType()));
        amazonScOrderDTO.setLogisticsType(tenantSalesChannelByChannelName.getLogisticsType());
        amazonScOrderDTO.setChannelId(tenantSalesChannelByChannelName.getId());
        amazonScOrderDTO.setOrderSource(OrderSourceEnum.INTERFACE_ORDER.getValue());
        if(StringUtils.isEmpty(amazonScOrderDTO.getAddressline1())){
            log.error("亚马逊SC订单：{} ,没有地址信息",json);
            return null;
        }
        if(CollUtil.isNotEmpty(amazonScOrderDTO.getItems()) && amazonScOrderDTO.getItems().size() > 1){
            amazonScOrderDTO.setIsMultiple(Boolean.TRUE);
        }else {
            amazonScOrderDTO.setIsMultiple(Boolean.FALSE);
        }
        return amazonScOrderDTO;
    }

    @Override
    public Boolean msgVerify(AmazonScOrderDTO amazonScOrderDTO) {
        List<AmazonScOrderItemDTO> items = amazonScOrderDTO.getItems();
        // 订单物流信息
        LogisticsTypeEnum orderLogisticsType = amazonScOrderDTO.getLogisticsType();
        for (AmazonScOrderItemDTO amazonScOrderItemDTO : items) {
            ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(amazonScOrderDTO.getTenantId(), amazonScOrderDTO.getChannelId(), amazonScOrderItemDTO.getSku(), SyncStateEnum.Mapped,amazonScOrderDTO.getCountry_code());
            if(null != productMapping && StringUtils.isNotEmpty(productMapping.getProductSkuCode())){
                ProductSku productSku = iProductSkuService.queryByProductSkuCode(productMapping.getProductSkuCode());
                if (productSku == null) {
                    continue;
                } else {
                    // 如果商品被管控，则报商品不存在
                    String tenantId = "1";
                    boolean checkUserAllow = iProductChannelControlService.checkUserAllow(tenantId, productMapping.getProductSkuCode(), ChannelTypeEnum.Amazon_SC.name());

                    if (!checkUserAllow) {
                        // 日志记录商品不存在
                        return Boolean.FALSE;
                    }
                }
                Product product = iProductService.queryByProductSkuCode(productMapping.getProductSkuCode());
                // 产品的物流信息
                SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();
                // 物流类型校验
                if(SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)){
                    if(orderLogisticsType.equals(LogisticsTypeEnum.DropShipping)){
                        log.error("订单物流类型: {} 和产品支持的物流类型: {} 不匹配,无法生成订单",supportedLogistics,LogisticsTypeEnum.DropShipping);
                        amazonScOrderDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                    }
                }
                if(SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogistics)){
                    if(orderLogisticsType.equals(LogisticsTypeEnum.PickUp)){
                        log.error("订单物流类型: {} 和产品支持的物流类型: {} 不匹配,无法生成订单",supportedLogistics,LogisticsTypeEnum.PickUp);
                        amazonScOrderDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                    }
                }
            }
            // 规则校验
            if (!RegexUtil.matchQuantity(amazonScOrderItemDTO.getQuantity().toString())) {
                // 数量异常-正则表达式
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Orders formalOrderAndItemEntry(Map<String, List> map, Orders orders) {
        List<OrderItem> orderItems = (List<OrderItem>) map.get("orderItems");
        List<OrderItemProductSku> skus = (List<OrderItemProductSku>) map.get("orderItemProductsSku");
        iOrdersService.save(orders);
        orderItems.forEach(item -> item.setOrderId(orders.getId()));
        iOrderItemService.saveBatch(orderItems);
        if(CollUtil.isNotEmpty(skus)){
            for (OrderItem item : orderItems) {
                for (OrderItemProductSku sku : skus) {
                    if (item.getOrderItemNo().equals(sku.getOrderItemNo())) {
                        sku.setOrderItemId(item.getId());
                        sku.setOrderNo(item.getOrderNo());
                    }
                }
            }
            iOrderItemProductSkuService.saveBatch(skus);
        }
        log.info("订单操作完成,进行保存操作");
        return orders;
    }

    @Override
    public void formalOrderAboutEntry(Map<String, Object> map) {
        List<OrderLogisticsInfo> logisticsInfo = (List<OrderLogisticsInfo>) map.get("logisticsInfo");
        List<OrderAddressInfo> address = (List<OrderAddressInfo>) map.get("addressInfo");
//        List<OrderItemPrice> itemPrices = (List<OrderItemPrice>) map.get("orderItemPrice");
        Orders orders = (Orders) map.get("orders");
        String tenantId = orders.getTenantId();
        iOrderLogisticsInfoService.saveBatch(logisticsInfo);
        iOrderAddressInfoService.saveBatch(address);
//        if(CollUtil.isNotEmpty(itemPrices)){
//            iOrderItemPriceService.saveBatch(itemPrices);
//        }
        String logisticsCompanyName = null;
        if(CollUtil.isNotEmpty(logisticsInfo)){
            OrderLogisticsInfo orderLogisticsInfo = logisticsInfo.get(0);
            if (ObjectUtil.isNotEmpty(orderLogisticsInfo)){
                logisticsCompanyName = orderLogisticsInfo.getLogisticsCarrierCode();
            }
        }
        //      address转换为map,key为orderNo,value为zipCode
        Map<String, String> addressMap = address.stream()
                                                .collect(Collectors.toMap(OrderAddressInfo::getOrderNo, OrderAddressInfo::getZipCode));

        if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
            // 重新计算订单价格信息
            List<OrderItemPrice> itemPrices = new ArrayList<>();
            List<OrderItem> orderItemUpdateList = new ArrayList<>();
            List<OrderItem> listByOrderId = iOrderItemService.getListByOrderId(orders.getId());
            HashMap<String,List<String> >stashMap = orderSupport.getStashList(listByOrderId);
            String warehouseSystemCode = null;
            String warehouseCode = null;
            String logisticsCarrierCode = null;
            String logisticsCode = null;
            if(CollUtil.isNotEmpty(listByOrderId)){
                for(OrderItem orderItem : listByOrderId){
                    OrderPriceCalculateDTO orderPriceCalculateDTO = new OrderPriceCalculateDTO();
                    orderPriceCalculateDTO.setOrderItem(orderItem);
                    orderPriceCalculateDTO.setActivityCode(orderItem.getActivityCode());
                    orderPriceCalculateDTO.setLogisticsType(orders.getLogisticsType());
                    List<String> stashList = new ArrayList<>();
                    if(CollUtil.isNotEmpty(stashMap)){
                        stashList = stashMap.get(orderItem.getOrderItemNo());
                    }else {
                        stashList = null;
                    }
                    LocaleMessage calculationOrderItemPriceExceptionMessage = priceSupportV2.calculationOrderItemPrice(orderPriceCalculateDTO,tenantId,addressMap.get(orderItem.getOrderNo()),stashList, orders, OrderFlowEnum.THIRD_CREATE_ORDER, logisticsCompanyName);
                    warehouseSystemCode = orderPriceCalculateDTO.getWarehouseSystemCode();
                    warehouseCode = orderPriceCalculateDTO.getWarehouseSystemCode();
                    logisticsCarrierCode = orderPriceCalculateDTO.getLogisticsCarrierCode();
                    logisticsCode = orderPriceCalculateDTO.getLogisticsCode();
                    if(ObjectUtil.isNotEmpty(calculationOrderItemPriceExceptionMessage) && StringUtils.isNotEmpty(calculationOrderItemPriceExceptionMessage.getEn_US())){
                        orders.setPayErrorMessage(calculationOrderItemPriceExceptionMessage.toJSON());
                    }
                    OrderItemPrice orderItemPrice1 = orderPriceCalculateDTO.getOrderItemPrice();
                    OrderItemPrice orderItemPrice = new OrderItemPrice();
                    BeanUtils.copyProperties(orderItemPrice1, orderItemPrice);
                    orderItemPrice.setTotalQuantity(orderItem.getTotalQuantity()).setOrderItemId(orderItem.getId()).setOrderItemNo(orderItem.getOrderItemNo()).setProductSkuCode(orderItem.getProductSkuCode()).setLogisticsType(orderItem.getLogisticsType());
                    itemPrices.add(orderItemPrice);
                    OrderItem orderItemUpdate = new OrderItem();
                    BeanUtils.copyProperties(orderItem, orderItemUpdate);
                    orderItemUpdateList.add(orderItemUpdate);
                }
                // 重新计算主订单数据
                priceSupportV2.recalculateOrderAmount(orders,itemPrices);
            }
            // 更新订单明细信息
            orderItemPriceService.saveOrSetNUll(itemPrices,orders.getExceptionCode());

            orderItemService.updateOrSetNUll(orderItemUpdateList,orders.getExceptionCode());

            ordersService.updateOrSetNull(orders);
            // 一件代发订单物流信息更新
            if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                iOrderItemProductSkuService.updateOrSetNull(orders.getOrderNo(),warehouseSystemCode, warehouseSystemCode);
            }
            // 一件代发订单物流信息更新
            if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                iOrderLogisticsInfoService.updateOrSetNull(orders.getOrderNo(),logisticsCarrierCode,logisticsCode);
            }

        }
    }

    @Override
    public Map<String, Object> msgForLogistics(AmazonScOrderDTO amazonScOrderDTO, Orders orders,
                                               Map<String, List> itemMap) {
        HashMap<String, Object> map = new HashMap<>();
        List<OrderItem> orderItems = (List<OrderItem>) itemMap.get("orderItems");
        OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
        ArrayList<OrderAddressInfo> addressInfos = new ArrayList<>();
        ArrayList<OrderLogisticsInfo> orderLogisticsInfos = new ArrayList<>();
        // 订单物流信息
        orderLogisticsInfo.setOrderId(orders.getId());
        orderLogisticsInfo.setOrderNo(orders.getOrderNo());
        orderLogisticsInfo.setShippingLabelExist(true);
        orderLogisticsInfo.setLogisticsZipCode(amazonScOrderDTO.getPostal_code());
        orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orders.getLogisticsType()
                                                                                               .name()));
//        orderLogisticsInfo.setLogisticsCompanyName(details.getCarrier());
//        orderLogisticsInfo.setLogisticsServiceName(details.getCarrier());
        orderLogisticsInfo.setZipCode(amazonScOrderDTO.getPostal_code());
        orderLogisticsInfo.setLogisticsCountryCode(amazonScOrderDTO.getCountry_code());
        orderLogisticsInfos.add(orderLogisticsInfo);

        // 订单地址信息
        OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
        orderAddressInfo.setOrderId(orders.getId());
        orderAddressInfo.setOrderNo(orders.getOrderNo());
        // 拿默认模版里面
        orderAddressInfo.setRecipient(amazonScOrderDTO.getBuy_name());

        orderAddressInfo.setPhoneNumber(amazonScOrderDTO.getPhone());
        // 这三个信息需要调用包裹接口拿到详细的包裹信息
        orderAddressInfo.setCountry(amazonScOrderDTO.getCountry());
        orderAddressInfo.setCountryCode(amazonScOrderDTO.getCountry_code());
        String zipCode = amazonScOrderDTO.getPostal_code();
        String state = amazonScOrderDTO.getState_or_region();
        orderAddressInfo.setState(state);
        orderAddressInfo.setStateCode(state);

        orderAddressInfo.setCity(amazonScOrderDTO.getCity());
        orderAddressInfo.setAddress1(amazonScOrderDTO.getAddressline1());
        orderAddressInfo.setAddress2(amazonScOrderDTO.getAddressline2());

        orderAddressInfo.setZipCode(zipCode);
//        orderAddressInfo.setEmail(address.getBuyerEmail());
        orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);

        addressInfos.add(orderAddressInfo);

        List<OrderItemPrice> itemPrices = new ArrayList<>();
        if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
            OrderPriceCalculateDTO paramDTO = new OrderPriceCalculateDTO();
            // 订单明细价格
            for (OrderItem orderItem : orderItems) {
                paramDTO.setOrderItem(orderItem);
                paramDTO.setLogisticsType(amazonScOrderDTO.getLogisticsType());
                paramDTO.setCountry(amazonScOrderDTO.getCountry_code());
                OrderPriceCalculateDTO calculateDTO = orderSupport.calculationOrderItemPriceForTemu(paramDTO);
                OrderItemPrice itemPrice = calculateDTO.getOrderItemPrice();
                itemPrice.setOrderItemId(orderItem.getId());
                itemPrices.add(itemPrice);
            }
        }
        map.put("logisticsInfo", orderLogisticsInfos);
        map.put("addressInfo", addressInfos);
        map.put("orderItemPrice", itemPrices);
        map.put("orders", orders);
        log.info("amazonSc订单物流信息:{}", JSONUtil.toJsonStr(orderLogisticsInfo));
        return map;
    }

    @Override
    public Map<String, List> msgForItems(AmazonScOrderDTO amazonScOrderDTO, Orders orders) {
        List<AmazonScOrderItemDTO> items = amazonScOrderDTO.getItems();
        List<OrderItem> orderItems = new ArrayList();
        List<OrderItemProductSku> orderItemProductSkus = new ArrayList<>();
        HashMap<String, List> hashMap = new HashMap<>();

        for (AmazonScOrderItemDTO amazonScOrderItemDTO : items) {
            OrderItem orderItem = new OrderItem();
            orderItem.setChannelType(orders.getChannelType());
            orderItem.setChannelSku(amazonScOrderItemDTO.getSku());
            orderItem.setLogisticsType(amazonScOrderDTO.getLogisticsType());
            orderItemService.setOrderBusinessFieldForAmazonSc(orderItem, amazonScOrderDTO, orders, amazonScOrderItemDTO);
            orderItemService.setChannelTagForAmazonSc(orderItem, amazonScOrderDTO, orders, amazonScOrderItemDTO);
            iOrderItemThirdService.setOrderTagSystemForAmazonSc(orderItem, amazonScOrderDTO, orders, amazonScOrderItemDTO);
            // 设置币种
            SiteCountryCurrencyVo siteCountryCurrencyVo = iSiteCountryCurrencyService.queryByCountryCode(amazonScOrderDTO.getCountry_code());
            if(null != siteCountryCurrencyVo){
                orderItem.setCurrency(siteCountryCurrencyVo.getCurrencyCode());
                orderItem.setCurrencySymbol(siteCountryCurrencyVo.getCurrencySymbol());
                orderItem.setCountryCode(siteCountryCurrencyVo.getCountryCode());
                orderItem.setSiteId(siteCountryCurrencyVo.getId());
            }
            orderItems.add(orderItem);
            // 获取商品相关的活动信息
            DistributorProductActivity distributorProductActivity = orderActivitySupport.getDistributorActivityByStock(amazonScOrderDTO.getTenantId(), amazonScOrderItemDTO.getProductSkuCode(), amazonScOrderDTO.getCountry(),orderActivitySupport.logisticsTypeConvert(orders.getLogisticsType()));
            // 产品没有映射不生成 orderItemProductsSku 数据
            if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
                OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
                // 通过 自订单编号进行关联,
                orderItemProductSkuThirdService.setBusinessFieldForAmazonSc(orderItemProductSku, orderItem, amazonScOrderDTO, orders, amazonScOrderItemDTO);
                // 活动订单设置为活动仓库
                if (null != distributorProductActivity) {
                    DistributorProductActivityStock activityWarehouseInfo = orderActivitySupport.getActivityWarehouseInfo(distributorProductActivity);
                    if(null != activityWarehouseInfo && null != activityWarehouseInfo.getWarehouseSystemCode()){
                        orderItemProductSku.setSpecifyWarehouse(activityWarehouseInfo.getWarehouseSystemCode());
                        orderItemProductSku.setWarehouseSystemCode(activityWarehouseInfo.getWarehouseSystemCode());
                    }
                }
                orderItemProductSkus.add(orderItemProductSku);
            }
            // 判断活动订单
            if (null != distributorProductActivity) {
                orderItem.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                orderItem.setActivityType(distributorProductActivity.getActivityType());
                orders.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                orders.setActivityType(distributorProductActivity.getActivityType());
            }
        }
        hashMap.put("orderItems", orderItems);
        hashMap.put("orderItemProductsSku", orderItemProductSkus);
        log.info("订单项信息:{}", JSONUtil.toJsonStr(orderItems));
        return hashMap;
    }

    @Override
    public Boolean isNeedPay() {
        return Boolean.FALSE;
    }

    @Override
    public String attachmentsFlow(AmazonScOrderDTO amazonScOrderDTO,Orders orders) {
//        LogisticsTypeEnum logisticsType = orders.getLogisticsType();
//// 目前全是代发
//        if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
//            SaleOrderDetailDTO saleOrderDetails = orderReceiveFromThirdDTO.getSaleOrderDetails();
//            List<AttachInfo> attachInfoItems = saleOrderDetails.getAttachInfoItems();
//            for (AttachInfo attachInfoItem : attachInfoItems) {
//                SysOssVo sysOssVo = sysOssService.downloadPdfNotAsync(attachInfoItem.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());
//                OrderUpdateBo bo = new OrderUpdateBo(orders.getOrderNo(), null, true, sysOssVo);
//                // 实际上传完了以后已经有了文件了 不需要再走上传的逻辑了
//                distributorOrderService.uploadShippingLabel(bo);
//            }
//        }
        return orders.getOrderNo();
    }

    @Override
    public Boolean isNeedPay(AmazonScOrderDTO amazonScOrderDTO) {
        Boolean isPay;
//        isPay = ZSMallSystemEventUtils.checkWalletAutoPaymentEvent(amazonScOrderDTO.getTenantId(),amazonScOrderDTO.getCurrency_code());
        isPay = ZSMallSystemEventUtils.checkAutoPaymentEvent(amazonScOrderDTO.getTenantId());
        if(isPay){
            // 异常订单不进行支付
            if(null != amazonScOrderDTO.getExceptionCode() && !amazonScOrderDTO.getExceptionCode().equals(OrderExceptionEnum.normal.getValue())){
                isPay = false;
            }
        }
        return isPay;
    }

    @Override
    public Boolean payOrder(AmazonScOrderDTO amazonScOrderDTO) throws Exception {
        // 是否开启自动支付
        if (true) {
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, amazonScOrderDTO.getOrdernum());

            List<String> orderNos = TenantHelper.ignore(() -> iOrdersService.list(lqw)).stream().map(Orders::getOrderNo)
                                                .collect(Collectors.toList());
            OrderPayBo bo = new OrderPayBo();
            bo.addOrderNoList(orderNos);
            bo.setPaymentPassword(null);
            if(CollUtil.isEmpty(orderNos)){
                return false;
            }
            try {
                TenantHelper.ignore(() -> {
                    try {
                        return ordersService.payOrderForErp(bo, amazonScOrderDTO.getTenantId(), true, true);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        throw new RuntimeException(e.getMessage());
                    }
                });
            } catch (Exception e) {
                log.error("支付失败:{}", e.getMessage());
            }
        }
        return true;
    }
    @Override
    public Boolean payOrderForAsync(AmazonScOrderDTO amazonScOrderDTO, Boolean isAsync) throws Exception {
        // 是否开启自动支付
        if (true) {
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, amazonScOrderDTO.getOrdernum());

            List<String> orderNos = TenantHelper.ignore(() -> iOrdersService.list(lqw)).stream().map(Orders::getOrderNo)
                                                .collect(Collectors.toList());
            OrderPayBo bo = new OrderPayBo();
            bo.addOrderNoList(orderNos);
            bo.setPaymentPassword(null);
            if(CollUtil.isEmpty(orderNos)){
                return false;
            }
            try {
                TenantHelper.ignore(() -> {
                    try {
//                        SaleOrderDetailDTO saleOrderDetails = orderReceiveFromThirdDTO.getSaleOrderDetails();
//                        Integer isNeedDelivery = saleOrderDetails.getIsNeedDelivery();
                        return ordersService.payOrderForErp(bo, amazonScOrderDTO.getTenantId(), true, true);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        throw new RuntimeException(e.getMessage());
                    }
                });
            } catch (Exception e) {
                log.error("支付失败:{}", e.getMessage());
            }
        }
        return true;
    }

    @Override
    public Orders thirdToDistribution(AmazonScOrderDTO amazonScOrderDTO, Orders orders) throws ParseException {
        Orders orders1 = new Orders();
        String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
        orders1.setOrderExtendId(orderNo);
        orders1.setOrderNo(orderNo);
        orders1.setOrderExtendId(orderNo);
        if(null != amazonScOrderDTO.getExceptionCode()){
            orders1.setExceptionCode(amazonScOrderDTO.getExceptionCode());
        }
        if(null != amazonScOrderDTO.getPayErrorMessage()){
            orders1.setPayErrorMessage(amazonScOrderDTO.getPayErrorMessage());
        }
        // 业务属性
        orders1 = ordersService.setOrderBusinessFieldForAmazonSc(amazonScOrderDTO, orders1);
        orders1 = ordersService.setOrderTagSystemByAmazonSc(amazonScOrderDTO, orders1);
        orders1 = ordersService.setChannelTagForAmazonSc(amazonScOrderDTO, orders1);
        // 币种
        SiteCountryCurrencyVo siteCountryCurrencyVo = iSiteCountryCurrencyService.queryByCountryCode(amazonScOrderDTO.getCountry_code());
        if(null != siteCountryCurrencyVo){
            orders1.setCurrency(siteCountryCurrencyVo.getCurrencyCode());
            orders1.setCurrencySymbol(siteCountryCurrencyVo.getCurrencySymbol());
            orders1.setCountryCode(siteCountryCurrencyVo.getCountryCode());
            orders1.setSiteId(siteCountryCurrencyVo.getId());
        }
        return orders1;
    }

    @Override
    public List<AmazonScOrderDTO> ordersDisassemble(AmazonScOrderDTO amazonScOrderDTO) {
        List<AmazonScOrderItemDTO> amazonScOrderItemDTOList = amazonScOrderDTO.getItems();
        List<AmazonScOrderDTO> amazonScOrderDTOList = new ArrayList<>();
        // 把订单项拆开
        for (AmazonScOrderItemDTO amazonScOrderItemDTO : amazonScOrderItemDTOList) {
            if (amazonScOrderItemDTO.getQuantity() == 1) {
                // 需要通过itemOrderId进行搜索
                LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, amazonScOrderDTO.getOrdernum())
                                                                                 .eq(Orders::getLineOrderItemId, amazonScOrderItemDTO.getChannel_order_item_id())
                                                                                 .eq(Orders::getDelFlag, 0)
                                                                                 .last("limit 1");
                Orders order = iOrdersService.getOne(lqw);
                if (ObjectUtil.isNotEmpty(order)) {
                    continue;
                }
                List<AmazonScOrderItemDTO> amazonScOrderItemDTOList1 = new ArrayList<>();
                AmazonScOrderDTO amazonScOrderDTO1 = new AmazonScOrderDTO();
                ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(amazonScOrderDTO.getTenantId(), amazonScOrderDTO.getChannelId(), amazonScOrderItemDTO.getSku(), SyncStateEnum.Mapped,amazonScOrderDTO.getCountry_code());
                TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getTenantSalesChannelByChannelName(amazonScOrderDTO.getAccount_id(), ChannelTypeEnum.Amazon_SC.name());
                if (ObjectUtil.isEmpty(tenantSalesChannel)) {
                    throw new AppRuntimeException("没有对应的渠道信息");
                }
                if (null != productMapping && StringUtils.isNotEmpty(productMapping.getProductSkuCode())) {
                    amazonScOrderItemDTO.setProductSkuCode(productMapping.getProductSkuCode());
                    // 判断渠道和产品的发货方式是否一致
                    Product product = iProductService.queryByProductSkuCode(productMapping.getProductSkuCode());
                    //校验渠道店铺发货方式和商品发货方式是否不一致
                    if (ObjectUtil.isNotNull(product) && ObjectUtil.isNotNull(tenantSalesChannel)) {
                        boolean isSupport = product.getSupportedLogistics()
                                                   .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
                        if (!isSupport) {
                            amazonScOrderDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                        }
                    }
                    // 判断库存是否足够
                    ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productMapping.getProductSkuCode());
                    Integer stockTotal = adjustStockVo.getStockTotal();
                    if (NumberUtil.compare(amazonScOrderItemDTO.getQuantity(), stockTotal) > 0) {
                        amazonScOrderDTO.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                    }
                }
                amazonScOrderItemDTOList1.add(amazonScOrderItemDTO);
                BeanUtils.copyProperties(amazonScOrderDTO, amazonScOrderDTO1);
                // 判断产品是否映射
                if (ObjectUtil.isEmpty(productMapping)) {
                    // 判断商品价格是否存在
                    List<ProductMapping> productMappingList = iProductMappingService.getProductMappingByChannelSkuAndSyncState(amazonScOrderDTO.getTenantId(), amazonScOrderDTO.getChannelId(), amazonScOrderItemDTO.getSku(), SyncStateEnum.Mapped);
                    String productSkuCode = null;
                    if(CollUtil.isNotEmpty(productMappingList)){
                        productSkuCode = productMappingList.get(0).getProductSkuCode();
                    }
                    ProductSkuPrice productSkuPrice;
                    if(StringUtils.isNotEmpty(productSkuCode)){
                        productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSkuCode, amazonScOrderDTO.getCountry());
                        if(null == productSkuPrice){
                            LocaleMessage localeMessage = LocaleMessage.byStatusCode(OrderStatusCodeEnum.SKU_REGION_PRICE_NOT_MAINTAINED.args(productSkuCode));
                            amazonScOrderDTO1.setPayErrorMessage(localeMessage.toJSON());
                            amazonScOrderDTO.setPayErrorMessage(localeMessage.toJSON());
                        }
                    }
                    amazonScOrderDTO1.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                    amazonScOrderDTO.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                }
                amazonScOrderDTO1.pushAmazonScOrderItemDTOList(amazonScOrderItemDTOList1);
                amazonScOrderDTO1.setChannel_order_item_id(amazonScOrderItemDTO.getChannel_order_item_id());
                amazonScOrderDTOList.add(amazonScOrderDTO1);
            }
            if (amazonScOrderItemDTO.getQuantity() > 1) {
                // 需要通过itemOrderId进行搜索
                LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, amazonScOrderDTO.getOrdernum())
                                                                                 .eq(Orders::getLineOrderItemId, amazonScOrderItemDTO.getChannel_order_item_id())
                                                                                 .eq(Orders::getDelFlag, 0)
                                                                                 .last("limit 1");
                Orders order = iOrdersService.getOne(lqw);
                if (ObjectUtil.isNotEmpty(order)) {
                    continue;
                }
                List<AmazonScOrderItemDTO> amazonScOrderItemDTOList1 = new ArrayList<>();
                AmazonScOrderDTO amazonScOrderDTO1 = new AmazonScOrderDTO();
                ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(amazonScOrderDTO.getTenantId(), amazonScOrderDTO.getChannelId(), amazonScOrderItemDTO.getSku(), SyncStateEnum.Mapped,amazonScOrderDTO.getCountry_code());
                TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getTenantSalesChannelByChannelName(amazonScOrderDTO.getAccount_id(), ChannelTypeEnum.Amazon_SC.name());
                if (ObjectUtil.isEmpty(tenantSalesChannel)) {
                    throw new AppRuntimeException("没有对应的渠道信息");
                }
                if (null != productMapping && StringUtils.isNotEmpty(productMapping.getProductSkuCode())) {
                    amazonScOrderItemDTO.setProductSkuCode(productMapping.getProductSkuCode());
                    // 判断渠道和产品的发货方式是否一致
                    Product product = iProductService.queryByProductSkuCode(productMapping.getProductSkuCode());
                    //校验渠道店铺发货方式和商品发货方式是否不一致
                    if (ObjectUtil.isNotNull(product) && ObjectUtil.isNotNull(tenantSalesChannel)) {
                        boolean isSupport = product.getSupportedLogistics()
                                                   .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
                        if (!isSupport) {
                            amazonScOrderDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                        }
                    }
                    // 判断库存是否足够
                    ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productMapping.getProductSkuCode());
                    Integer stockTotal = adjustStockVo.getStockTotal();
                    if (NumberUtil.compare(amazonScOrderItemDTO.getQuantity(), stockTotal) > 0) {
                        amazonScOrderDTO.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                    }
                }
                amazonScOrderItemDTOList1.add(amazonScOrderItemDTO);
                BeanUtils.copyProperties(amazonScOrderDTO, amazonScOrderDTO1);
                // 判断产品是否映射
                if (ObjectUtil.isEmpty(productMapping)) {
                    // 判断商品价格是否存在
                    List<ProductMapping> productMappingList = iProductMappingService.getProductMappingByChannelSkuAndSyncState(amazonScOrderDTO.getTenantId(), amazonScOrderDTO.getChannelId(), amazonScOrderItemDTO.getSku(), SyncStateEnum.Mapped);
                    String productSkuCode = null;
                    if(CollUtil.isNotEmpty(productMappingList)){
                        productSkuCode = productMappingList.get(0).getProductSkuCode();
                    }
                    ProductSkuPrice productSkuPrice;
                    if(StringUtils.isNotEmpty(productSkuCode)){
                        productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSkuCode, amazonScOrderDTO.getCountry());
                        if(null == productSkuPrice){
                            LocaleMessage localeMessage = LocaleMessage.byStatusCode(OrderStatusCodeEnum.SKU_REGION_PRICE_NOT_MAINTAINED.args(productSkuCode));
                            amazonScOrderDTO1.setPayErrorMessage(localeMessage.toJSON());
                            amazonScOrderDTO.setPayErrorMessage(localeMessage.toJSON());
                        }
                    }
                    amazonScOrderDTO1.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                    amazonScOrderDTO.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                }
                amazonScOrderDTO1.pushAmazonScOrderItemDTOList(amazonScOrderItemDTOList1);
                amazonScOrderDTO1.setChannel_order_item_id(amazonScOrderItemDTO.getChannel_order_item_id());
                // 拆分数量
                Integer forI = amazonScOrderItemDTO.getQuantity();
                amazonScOrderItemDTO.setQuantity(1);
                amazonScOrderItemDTO.setSubtotal(amazonScOrderItemDTO.getSubtotal().divide(new BigDecimal(forI)));
                for (int i = 0; i < forI; i++){
                    amazonScOrderDTOList.add(amazonScOrderDTO1);
                }
            }
        }
        return amazonScOrderDTOList;
    }

    @Override
    public R tripartiteUpdate(Object o) {
        return null;
    }

    @Override
    public Boolean tripartiteDeliverGoods(Object o) {
        return null;
    }

    @Override
    public Boolean tripartiteReceiptGoods(Object o) {
        return null;
    }

    @Override
    public R<Void> test() {
        return null;
    }

    @Override
    public void orderOperationHandler(AmazonScOrderDTO i, ConcurrentHashMap<String, List<Object>> businessMap,
                                      ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                      ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void priceOperation(ConcurrentHashMap<String, List<Object>> businessMap,
                               ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                               ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void orderOperationHandlerSave(ChannelTypeEnum channelTypeEnum,
                                          ConcurrentHashMap<String, List<Object>> businessMap) {

    }

    @Override
    public List<AmazonScOrderDTO> ordersDisassembleForList(List<AmazonScOrderDTO> amazonScOrderDTOList,
                                                       BusinessTypeMappingEnum mappingEnum) {
        return null;
    }

    @Override
    public List<AmazonScOrderDTO> parseThirdDataForList(String s) {
        return null;
    }

    @Override
    public void getShippingLabels(List<Map<String, List>> orderData) {
        try{
            AmazonVCOrderLogisticsAttachmentDTO amazonVCOrderLogisticsAttachmentDTO = new AmazonVCOrderLogisticsAttachmentDTO();
            List<AmazonVCOrderLogisticsAttachmentItemDTO> itemDTOList = new ArrayList<>();
            for (Map<String, List> orderDatum : orderData) {

                Orders orders = (Orders) orderDatum.get("orders").get(0);
                //发货类型为自提才获取面单
                if(!LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
                    log.info("发货类型为非自提，不获取面单");
                    return;
                }
                //List<OrderItem> orderItems = (List<OrderItem>) orderDatum.get("orderItems");
                List<OrderItemProductSku> skus = (List<OrderItemProductSku>) orderDatum.get("orderItemProductsSku");
                if(null == orders.getExceptionCode() || orders.getExceptionCode().equals(OrderExceptionEnum.normal.getValue())
                    || orders.getExceptionCode().equals(OrderExceptionEnum.out_of_stock_exception.getValue())){
                    log.info("发送订单信息到物流附件队列");
                    itemDTOList.addAll(createShippingLabels(orders, skus));
                }
            }
            Orders orderHead = (Orders)orderData.get(0).get("orders").get(0);
            if (CollectionUtil.isEmpty(itemDTOList)) {
                log.info("未获取到订单明细数据，单号：{}",orderHead.getChannelOrderNo());
                return;
            }
            amazonVCOrderLogisticsAttachmentDTO.setOrderNo(orderHead.getChannelOrderNo()).setChannel(orderHead.getChannelAlias().replaceAll("^[^:]*:", "")).setContainerType("Carton");
            amazonVCOrderLogisticsAttachmentDTO.setItems(itemDTOList);
            String str = JSON.toJSONString(amazonVCOrderLogisticsAttachmentDTO);
            String messageId = IdUtil.fastSimpleUUID();
            Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
            log.info("[发送订单信息到物流附件队列]成功，发送参数：{}",JSON.toJSON(amazonVCOrderLogisticsAttachmentDTO));
            rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_ROUTING_KEY,message);

        }catch (Exception e){
            log.error("发送订单信息到物流附件队列报错:{}",e.getMessage());
        }
    }

    private List<AmazonVCOrderLogisticsAttachmentItemDTO> createShippingLabels(Orders orders, List<OrderItemProductSku> skus) {
        List<AmazonVCOrderLogisticsAttachmentItemDTO> itemDTOList = new ArrayList<>();
        for(OrderItemProductSku orderItemProductSku : skus){
            AmazonVCOrderLogisticsAttachmentItemDTO itemDTO = new AmazonVCOrderLogisticsAttachmentItemDTO();
            itemDTO.setDeliverOrderNo(orders.getOrderNo()).setDeliverOrderId(orders.getId()).setChannelOrderItemId(orders.getLineOrderItemId()).setQuantity(1);
            ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuCode(orderItemProductSku.getProductSkuCode());
            if(null != productSkuDetail){
                // 设置长宽高
                switch (productSkuDetail.getPackLengthUnit())      {
                    case foot:
                        itemDTO.setLength(productSkuDetail.getPackLength());
                        itemDTO.setWidth(productSkuDetail.getPackWidth());
                        itemDTO.setHeight(productSkuDetail.getPackHeight());
                        break;
                    case inch:
                        itemDTO.setLength(UnitConverter.inchesToFeet(productSkuDetail.getPackLength()));
                        itemDTO.setWidth(UnitConverter.inchesToFeet(productSkuDetail.getPackWidth()));
                        itemDTO.setHeight(UnitConverter.inchesToFeet(productSkuDetail.getPackHeight()));
                        break;
                    case m:
                        itemDTO.setLength(UnitConverter.metersToFeet(productSkuDetail.getPackLength()));
                        itemDTO.setWidth(UnitConverter.metersToFeet(productSkuDetail.getPackWidth()));
                        itemDTO.setHeight(UnitConverter.metersToFeet(productSkuDetail.getPackHeight()));
                        break;
                    case cm:
                        itemDTO.setLength(UnitConverter.metersToFeet(UnitConverter.centimetersToMeters(productSkuDetail.getPackLength())));
                        itemDTO.setWidth(UnitConverter.metersToFeet(UnitConverter.centimetersToMeters(productSkuDetail.getPackWidth())));
                        itemDTO.setHeight(UnitConverter.metersToFeet(UnitConverter.centimetersToMeters(productSkuDetail.getPackHeight())));
                        break;
                }
                // 设置重量
                switch (productSkuDetail.getPackWeightUnit()){
                    case lb:
                        itemDTO.setWeight(productSkuDetail.getPackWeight());
                        break;
                    case kg:
                        itemDTO.setWeight(UnitConverter.kilogramsToPounds(productSkuDetail.getPackWeight()));
                        break;
                    case g:
                        itemDTO.setWeight(UnitConverter.gramsToPounds(productSkuDetail.getPackWeight()));
                        break;
                    case t:
                        itemDTO.setWeight(UnitConverter.kilogramsToPounds(UnitConverter.tonsToKilograms(productSkuDetail.getPackWeight())));
                        break;
                }
            }
            itemDTOList.add(itemDTO);
        }
        return itemDTOList;
    }
}
