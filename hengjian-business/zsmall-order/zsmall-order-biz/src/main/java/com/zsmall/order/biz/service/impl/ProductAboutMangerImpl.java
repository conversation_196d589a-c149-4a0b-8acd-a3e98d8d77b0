package com.zsmall.order.biz.service.impl;

import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.common.annotaion.Manager;
import com.zsmall.order.biz.service.IProductAboutManger;
import com.zsmall.product.biz.service.ProductSkuPriceService;
import com.zsmall.product.biz.service.ProductSkuService;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.iservice.IProductMappingService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/11 10:18
 */
@Slf4j
@RequiredArgsConstructor
@Manager
public class ProductAboutMangerImpl implements IProductAboutManger {

    private final ProductSkuService productSkuService;
    private final ProductSkuPriceService productSkuPriceService;
    private final IProductMappingService iMappingService;
    private final IProductSkuService iProductSkuService;


    @Override
    @InMethodLog("通过Sku查找商品价格")
    public ProductSkuPrice getProductPriceBySellerSku(String sellerSku, Long siteId, String tenantId) {
        ProductSku sku = productSkuService.getProductSkuBySellerSku(sellerSku, tenantId);
        ProductSkuPrice productSkuPrice = productSkuPriceService.getProductSkuPriceByCode(sku.getProductSkuCode(),siteId);
        return productSkuPrice;
    }

    @Override
    public ProductSkuPrice getProductPriceByProductSkuCode(String productSkuCode, Long siteId) {
        return productSkuPriceService.getProductSkuPriceByCode(productSkuCode, siteId);
    }


    @Override
    public ProductSkuPrice getProductPriceByProductSkuCodeAndCountryCode(String productSkuCode,String countryCode) {
        return productSkuPriceService.getProductSkuPriceByCodeAndCountryCode(productSkuCode,countryCode);
    }

    @Override
    public ProductSkuPrice getProductPriceByItemNo(String itemNo, Long siteId) {
        return productSkuPriceService.getProductSkuPriceByCode(itemNo,siteId);
    }


}
