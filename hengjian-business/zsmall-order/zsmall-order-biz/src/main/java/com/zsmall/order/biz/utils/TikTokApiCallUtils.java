package com.zsmall.order.biz.utils;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 10:43
 */

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zsmall.common.domain.tiktok.auth.TikTokAuth;
import com.zsmall.common.domain.tiktok.auth.TikTokAuthPro;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/26 11:05
 */
@Component
@Slf4j
public class TikTokApiCallUtils {
    public static final String ACCESS_TOKEN = "accessToken";
    public static final String REFRESH_TOKEN = "refreshToken";
    public static final String OPEN_ID = "openId";
    public static final String SHOP_CIPHER = "shopCipher";

    //    @XxlConf(value = "bizark-multichannel.tiktok.shopId")
    private static String TIKTOK_SHOP_ID;

    //    @XxlConf(value = "bizark-erp.tiktok.switch",defaultValue = "false")
    public static Boolean TIKTOK_SWITCH;

    //        @XxlConf(value = "bizark-multichannel.tiktok.appkey",defaultValue = "690c3k6msiuq81")
//    @Value("${distribution.tiktok.appKey}")
//    public String TIKTOK_APP_KEY;
    //    @XxlConf(value = "bizark-multichannel.tiktok.app.secret",defaultValue= "bed1c73e397473a77500987a35ad8d6263edc76e")
//    @Value("${distribution.tiktok.appSecret}")
//    public String TIKTOK_APP_SECRET;
    /**
     * 抖音应用秘密 本地测试使用
     */
    public static final String testTiktokAppSecret = "8566fadfc63f8233cc084981b3c1798ce32aad73";
    public static final String testTiktokAppKey = "69qe5l6kd3vog";

    public static final String SHOP_ID = "shopId";


    @Autowired
    private ITenantSalesChannelService iTenantSalesChannelService;


//    @XxlConf(value = "bizark-multichannel.tiktok.appkey")
//    public static String TIKTOK_APP_KEY;
//    @XxlConf(value = "bizark-multichannel.tiktok.app.secret")
//    public static String TIKTOK_APP_SECRET;


    /**
     * @param
     * @param appKey    appkey
     * @param appSecret
     * @param timestamp 请求时间戳
     * @param path      路径
     * @param param     看接口需求,部分接口必传shop_id
     * @param body
     * @description: 获取签名
     * @author: Moore
     * @date: 2023/9/19 17:42
     * @return: java.lang.String
     **/
    public String getSignForPost(String appKey, String appSecret, Long timestamp, String path, Map<String, Object> param,
                                 String body) {

        String sign = "";
        String unSign = appSecret + path + "app_key" + appKey;
        if (null != param) {
            for (Map.Entry<String, Object> entry : param.entrySet()) {
                unSign = unSign + entry.getKey() + entry.getValue();
            }
        }
        unSign = unSign + "timestamp" + timestamp;

        if (StringUtils.isNotEmpty(body)) {
            unSign = unSign + body;
        }
        unSign = unSign + appSecret;
        log.info("tiktok V2版本接口待签名字符串#{}", unSign);
        sign = encrypt(unSign, appSecret);
        log.info("tiktok V2版本接口签名字符串#{}", sign);
        return sign;
    }
    public String getSignForGet(String appKey, String appSecret, Long timestamp, String path, Map<String, Object> param
                          ) {

        String sign = "";
        String unSign = appSecret + path + "app_key" + appKey;
        if (null != param) {
            for (Map.Entry<String, Object> entry : param.entrySet()) {
                unSign = unSign + entry.getKey() + entry.getValue();
            }
        }
        unSign = unSign + "timestamp" + timestamp;

        unSign = unSign + appSecret;
        log.info("tiktok V2版本接口待签名字符串#{}", unSign);
        sign = encrypt(unSign, appSecret);
        log.info("tiktok V2版本接口签名字符串#{}", sign);
        return sign;
    }

//    /**
//     * 发送帖子专业版
//     *
//     * @param dataMap   数据地图
//     * @param queryMap  查询地图
//     * @param bodyParam 正文参数
//     * @param shopId
//     * @return {@link String}
//     */
//    public String sendPutPro(Map dataMap, Map queryMap, String bodyParam, String shopId) {
//        String integrityUrl;
//        if (TIKTOK_SWITCH) {
//            integrityUrl = getIntegrityUrlV1ForPost(dataMap, queryMap, TIKTOK_APP_KEY, TIKTOK_APP_SECRET, shopId);
//        } else {
//            integrityUrl = getIntegrityUrlV1ForPost(dataMap, queryMap, tiktokAppKey, tiktokAppSecret, shopId);
//        }
//        try {
//            HttpRequest httpRequest = HttpUtil.createRequest(Method.PUT, integrityUrl);
//            httpRequest.header("Content-Type", "application/json");
//            httpRequest.body(bodyParam);
//            HttpResponse httpResponse = httpRequest.execute();
//            if (httpResponse.isOk()) {
//                log.error("tiktok call error : {}", httpResponse.isOk());
//                return httpResponse.body();
//            }
//        } catch (Exception e) {
//            log.error("tikok库存修改失败，参数：{}", bodyParam);
//            return null;
//        }
//        return null;
//
//    }
    /**
     * 发送帖子专业版
     *
     * @param dataMap   url+path 基础路径拼接
     * @param queryMap  query条件
     * @param bodyParam 正文参数
     * @param shopId
     * @return {@link String}
     */
    public String sendPostPro(Map dataMap, Map queryMap, String bodyParam, String shopId,String tiktokAppKey,String tiktokAppSecret) {
        OkHttpClient client = new OkHttpClient();
        StringBuilder result = new StringBuilder();
        MediaType mediaType = MediaType.parse("application/json");
        BufferedReader in = null;
        String integrityUrl;
        if (Boolean.TRUE) {
            log.info("暂时使用:app{},secret{}", tiktokAppKey, tiktokAppSecret);
            integrityUrl = getIntegrityUrlV1ForPost(dataMap, queryMap, tiktokAppKey, tiktokAppSecret, shopId);
        } else {
            // 测试使用
            integrityUrl = getIntegrityUrlV1ForPost(dataMap, queryMap, testTiktokAppKey, testTiktokAppSecret, shopId);
        }

        String jsonString = JSON.toJSONString(queryMap);

        log.info("body入参:{}", jsonString);

        TikTokAuth auth = getTikTokAuth(Long.parseLong(shopId));
//        RequestBody body = RequestBody.create(mediaType, substring1);
        RequestBody body = RequestBody.create(mediaType, jsonString);
        try {
            Request request = new Request.Builder().url(integrityUrl)
                                                   .addHeader("Content-Type", "application/json")
                                                   .addHeader("x-tts-access-token", auth.getAccess_token())
                                                   .post(body)
                                                   .build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("参数:{}, 调用tiktok接口失败response code: {} and message: {}", jsonString,
                    response.code(), response.message());
            }
            ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
            log.info(" tiktok response body result:{}", result);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result.toString();
    }

    /**
     * 发送获取专业版
     *
     * @param dataMap         数据地图
     * @param queryMap        查询地图
     * @param bodyParam       正文参数
     * @param shopId
     * @param tiktokAppKey
     * @param tiktokAppSecret
     * @return {@link String}
     */
    public String sendGetPro(Map dataMap, Map queryMap, String bodyParam, String shopId, String tiktokAppKey,
                             String tiktokAppSecret) {
        OkHttpClient client = new OkHttpClient();
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;

        String integrityUrl;
        if (Boolean.TRUE) {
            integrityUrl = getIntegrityUrlV1ForGet(dataMap, queryMap, tiktokAppKey, tiktokAppSecret, shopId);
        } else {
            integrityUrl = getIntegrityUrlV1ForGet(dataMap, queryMap, testTiktokAppKey, testTiktokAppSecret, shopId);
        }
        TikTokAuth auth = getTikTokAuth(Long.parseLong(shopId));
        try {
            Request request = new Request.Builder().url(integrityUrl)
                                                   .addHeader("Content-Type", "application/json")
                                                   .addHeader("x-tts-access-token", auth.getAccess_token())
                                                   .get()
                                                   .build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.info("参数:{}, 调用tiktok接口失败response code: {} and message: {}", bodyParam,
                    response.code(), response.message());
                // 需要抛出一个异常来
                return null;
            }
            ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
            log.info(" tiktok response body result:{}", result);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result.toString();
    }




    /**
     * 获取完整性网址
     *
     * @param map             地图
     * @param queryMap        参数
     * @param tiktokAppKey    抖音应用密钥
     * @param tiktokAppSecret 抖音应用秘密
     * @param shopId
     * @return {@link String}
     */
    private String getIntegrityUrlV1ForPost(Map<String, Object> map, Map<String, Object> queryMap, String tiktokAppKey,
                                            String tiktokAppSecret, String shopId) {
        TikTokAuth auth = getTikTokAuth(Long.parseLong(shopId));
        Date date = new Date();
        long now = date.getTime() / 1000;

        Map<String, Object> stringStringHashMap = new TreeMap<>(Comparator.naturalOrder());
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();

            if (!"url".equals(key) && !"path".equals(key) && !"shop_id".equals(key)) {

                stringStringHashMap.put(entry.getKey(), entry.getValue());

            }
        }
        stringStringHashMap.put("shop_cipher",auth.getShop_cipher());


//        stringStringHashMap.put("access_token",auth.getAccess_token());
        // -> {Boolean@30991} false
        String body = JSON.toJSONString(queryMap);

        String sign = getSignForPost(tiktokAppKey, tiktokAppSecret, now, String.valueOf(map.get("path")), stringStringHashMap, body);


        StringBuilder integrityUrl = new StringBuilder();
        // 按照顺序拼接 url+path
        integrityUrl = appendPrefixInOrder(appendPrefixInOrder(integrityUrl, map, "url"), map, "path");
        // 其余属性拼接

        log.info("完整url路径:{}", integrityUrl);
//        Long encryptedShopId = Long.parseLong(queryMap.get("shop_id"));

        integrityUrl.
            append("app_key=").
            append(tiktokAppKey).
            append("&sign=").
            append(sign).
            append("&timestamp=").
            append(now).
            append("&access_token=").
            append(auth.getAccess_token());

        integrityUrl = appendQueryListInOrder(integrityUrl, stringStringHashMap);


        log.info("tiktok调用完整路径:{}", integrityUrl);
        return integrityUrl.toString();
    }

    /**
     * 功能描述：获取 GET 完整性 URL v1
     *
     * @param map             地图
     * @param queryMap        查询地图
     * @param tiktokAppKey    TikTok 应用密钥
     * @param tiktokAppSecret TikTok 应用秘密
     * @param shopId          店铺 ID
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/02/20
     */
    private String getIntegrityUrlV1ForGet(Map<String, Object> map, Map<String, Object> queryMap, String tiktokAppKey,
                                            String tiktokAppSecret, String shopId) {
        TikTokAuth auth = getTikTokAuth(Long.parseLong(shopId));
        Date date = new Date();
        long now = date.getTime() / 1000;

        Map<String, Object> stringStringHashMap = new TreeMap<>(Comparator.naturalOrder());
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();

            if (!"url".equals(key) && !"path".equals(key)) {

                stringStringHashMap.put(entry.getKey(), entry.getValue());

            }
        }
        stringStringHashMap.put("shop_cipher",auth.getShop_cipher());

        String sign = getSignForGet(tiktokAppKey, tiktokAppSecret, now, String.valueOf(map.get("path")), stringStringHashMap);


        StringBuilder integrityUrl = new StringBuilder();
        // 按照顺序拼接 url+path
        integrityUrl = appendPrefixInOrder(appendPrefixInOrder(integrityUrl, map, "url"), map, "path");
        // 其余属性拼接

        log.info("完整url路径:{}", integrityUrl);
//        Long encryptedShopId = Long.parseLong(queryMap.get("shop_id"));

        integrityUrl.
            append("app_key=").
            append(tiktokAppKey).
            append("&sign=").
            append(sign).
            append("&timestamp=").
            append(now).
            append("&access_token=").
            append(auth.getAccess_token());

        integrityUrl = appendQueryListInOrder(integrityUrl, stringStringHashMap);


        log.info("tiktok调用完整路径:{}", integrityUrl);
        return integrityUrl.toString();
    }

    /**
     * 按顺序追加
     *
     * @param integrityUrl 完整性网址
     * @param map          地图
     * @param param        参数
     * @return {@link StringBuilder}
     */
    public StringBuilder appendPrefixInOrder(StringBuilder integrityUrl, Map<String, Object> map, String param) {
        map.entrySet().removeIf(key -> {
            if (param.equals(key.getKey()) && ObjectUtil.isNotNull(key.getValue())) {
                integrityUrl.append(key.getValue());
                if ("path".equals(param)) {
                    integrityUrl.append("?");
                }
                return true;
            }
            return false;
        });
        return integrityUrl;
    }

    /**
     * 按顺序追加
     *
     * @param integrityUrl 完整性网址
     * @param map          地图
     * @return {@link StringBuilder}
     */
    public StringBuilder appendQueryListInOrder(StringBuilder integrityUrl, Map<String, Object> map) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (ObjectUtil.isNotNull(entry.getValue())) {
                integrityUrl
                    .append("&").append(entry.getKey()).append("=").append(entry.getValue());
            }
        }
        return integrityUrl;
    }

    public static String encrypt(String message, String secretKey) {
        try {
            // 创建一个HMAC-SHA256算法实例，并指定密钥
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            hmacSha256.init(secretKeySpec);

            // 计算消息的摘要值
            byte[] hash = hmacSha256.doFinal(message.getBytes(StandardCharsets.UTF_8));

            return byte2Hex(hash);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 将byte转为16进制
     *
     * @param bytes
     * @return
     */
    public static String byte2Hex(byte[] bytes) {
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    /**
     * 获取抖音身份验证
     *
     * @param shopId 店铺编号
     * @return {@link }
     */
    public TikTokAuthPro getTikTokAuth(Long shopId) {
        TenantSalesChannel shopMsgByRealShopId = iTenantSalesChannelService.getShopMsgByRealShopId(String.valueOf(shopId));

        TenantSalesChannel account = iTenantSalesChannelService.getById(shopMsgByRealShopId.getId());

        if (Objects.isNull(account)) {
            log.error("tiktok auth error [{}] : account not found ", shopId);
            return null;
        }
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("tiktok auth error [{}] : connectStr is empty ", shopId);
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            log.error("tiktok auth error [{}] : accessToken is empty ", shopId);
            return null;
        }
        return TikTokAuthPro.builder().open_id(jsonObject.getString(OPEN_ID)).connectStr(connectStr)
                            .access_token(jsonObject.getString(ACCESS_TOKEN))
                            .refresh_token(jsonObject.getString(REFRESH_TOKEN))
                            .shop_cipher(jsonObject.getString(SHOP_CIPHER))
                            .build();

    }

    /**
     * @param shopId 店铺ID
     * @description: 获取TikTok鉴权信息
     * @author: Moore
     * @date: 2023/9/19 18:54
     * @return: com.bizark.erp.api.entity.erp.conf.TikTokAuth
     **/
    public TikTokAuth getTikTokAuth(Integer shopId) {
        TenantSalesChannel account = iTenantSalesChannelService.getById(shopId);
        if (Objects.isNull(account)) {
            log.error("tiktok auth error [{}] : account not found ", shopId);
            return null;
        }
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("tiktok auth error [{}] : connectStr is empty ", shopId);
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            log.error("tiktok auth error [{}] : accessToken is empty ", shopId);
            return null;
        }
        return new TikTokAuth(
            jsonObject.getString(ACCESS_TOKEN),
            jsonObject.getString(REFRESH_TOKEN),
            jsonObject.getString(OPEN_ID),
            jsonObject.getString(SHOP_CIPHER)
        );
    }


    /**
     * 功能描述：拆分订单
     *
     * @param tikTokParam                   仅支持
     * @param channelId                     通道id
     * @param orderNo                       订单号
     * @param itemBuyLabelList              商品购买标签列表
     * @param amazonVendorDirectLabelReturn 亚马逊供应商直接标签退货
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/01
     */
//    public String splitOrders(TikTokAuth tikTokParam, String channelId, String orderNo, List<ItemBuyLabel> itemBuyLabelList, AmazonVendorDirectLabelReturn amazonVendorDirectLabelReturn) {
//        String shopCipher = tikTokParam.getShop_cipher();
//        String appKey = TIKTOK_APP_KEY;
//        String appSecret = TIKTOK_APP_SECRET;
//
//        JSONObject bodyParam = new JSONObject();
//        List<JSONObject> splittableGroups = new ArrayList<>();
//        itemBuyLabelList.forEach(item -> {
//            JSONObject splittable = new JSONObject();
//            splittable.put("id", item.getDeliverOrderId());
//            String[] orderItems = new String[1];
//            orderItems[0] = item.getChannelOrderItemId();
//            splittable.put("order_line_item_ids", orderItems);
//            splittableGroups.add(splittable);
//        });
//        bodyParam.put("splittable_groups", splittableGroups);
//        String path = TikTokApiEnums.SPLIT_ORDERS.formatPath(orderNo);
//        Map<String, Object> queryParam = new LinkedHashMap<>();
//        queryParam.put("shop_cipher", shopCipher);
//        Long timestamp = System.currentTimeMillis() / 1000;
//        String sign = getSignForPost(appKey, appSecret, timestamp, path, queryParam, bodyParam.toJSONString());
//
//
//
//        String url = path + "?timestamp=" + timestamp + "&sign=" + sign + "&app_key=" + appKey + "&access_token=" + tikTokParam.getAccess_token() + "&shop_cipher=" + shopCipher;
//
//        log.info("tiktok channel:{} getLabel orderNo {}, splitOrders url:{}, bodyParam:{}", channelId, orderNo, url, bodyParam.toJSONString());
//        try {
//            String response = HttpUtil.sendTkV2Post(channelId, tikTokParam.getAccess_token(), url, bodyParam.toJSONString(), "splitOrders");
//            log.info("tiktok channel:{} getLabel orderNo {}, splitOrders response:{}", channelId, orderNo, response);
//            if (StringUtils.isNotEmpty(response)) {
//                TikTokResponse tikTokResponse = JSONObject.parseObject(response, TikTokResponse.class);
//                if (0 == tikTokResponse.getCode()) {
//                    String data = tikTokResponse.getData();
//                    return data;
//                } else {
//                    amazonVendorDirectLabelReturn.setErrorMessage(tikTokResponse.getMessage());
//                    log.error("tiktok channel:{} getLabel orderNo {}, splitOrders fail:{}", channelId, orderNo, tikTokResponse.getMessage());
//                }
//            } else {
//                amazonVendorDirectLabelReturn.setErrorMessage("拆单失败：Internal Server Error");
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("tiktok channel:{} getLabel orderNo {}, splitOrders exception:{}", channelId, orderNo, e.getMessage());
//        }
//        return null;
//    }

}
