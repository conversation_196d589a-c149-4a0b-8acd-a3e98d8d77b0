package com.zsmall.order.entity.domain.bo.pay;

import cn.hutool.core.lang.UUID;
import com.hengjian.common.core.utils.StringUtils;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.*;

/**
 * <AUTHOR>
 * @date 2025年8月12日  17:29
 * @description: payoneer支付参数
 */
@Data
public class PayoneerPayParam implements Serializable {

    private static final long serialVersionUID = 1L;


    private Style style;

    private Callback callback;

    // 消费者信息
    private PayoneerRequestCustomer payoneerRequestCustomer;

    // 支付信息
    private PayoneerRequestPayment payoneerRequestPayment;

    private List<Product> productList;

    private List<String> storageFeeIdList;


    /**
     * 仓储费参数初始化
     *
     * @param returnUrl
     * @param cancelUrl
     * @param amount
     * @param currency
     * @param customerEmail
     * @param unitPrice
     */
    public void storageFeeInit(String returnUrl,String cancelUrl,BigDecimal amount,String currency,String customerEmail,BigDecimal unitPrice,List<String> storageFeeIdList){
        Style style = new Style();
        style.setLanguage("cn").setHostedVersion("v4").setDisplayName("Sweet furniture").setPrimaryColor("#2196F3").setLogoUrl("https://hengjian-distribution.oss-cn-hangzhou.aliyuncs.com/2024/04/11/11.png").setBackgroundType("BACKGROUND_IMAGE");
        Callback callback = new Callback();
        callback.setReturnUrl(returnUrl);
        callback.setCancelUrl(cancelUrl);
        callback.setNotificationUrl("https://distribution.ehengjian.com/prod-api/distributor/salesChannel/payoneer/notifications");
        // 支付信息
        PayoneerRequestPayment payoneerRequestPayment = new PayoneerRequestPayment();
        // 产品信息
        List<Product> productList = new ArrayList<>();
        // 消费者信息
        PayoneerRequestCustomer payoneerRequestCustomer = new PayoneerRequestCustomer();
        // 地址信息
        Billing shipping = new Billing();

        payoneerRequestPayment.setReference("storage").setAmount(amount).setCurrency(currency);

        payoneerRequestCustomer.setEmail(customerEmail);
        Addresses addresses = new Addresses();
        shipping.setStreet("7401 E. Vista Drive").setZip("85250").setCity("Scottsdale").setState("AZ").setCountry("US");
        addresses.setShipping(shipping).setBilling(shipping);
        payoneerRequestCustomer.setAddresses(addresses);
        com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.Product product = new com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.Product();
        product.setType("PHYSICAL").setName("storage fee card").setQuantity(1L);
        product.setCurrency(currency);
        product.setAmount(unitPrice);
        productList.add(product);

//        PayoneerRequest payoneerRequest = new PayoneerRequest();
//        String transactionId = UUID.fastUUID().toString();
//        payoneerRequest.setIntegration("HOSTED").setTransactionId(transactionId).setCountry("CN").setChannel("WEB_ORDER")
//                       .setCallback(callback).setCustomer(payoneerRequestCustomer).setStyle(style).setPayment(payoneerRequestPayment).setProducts(productList);
        this.callback = callback;
        this.style = style;
        this.payoneerRequestPayment = payoneerRequestPayment;
        this.payoneerRequestCustomer = payoneerRequestCustomer;
        this.productList = productList;
        this.storageFeeIdList = storageFeeIdList;
    }
}
