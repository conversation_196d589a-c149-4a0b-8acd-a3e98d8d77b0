package com.zsmall.order.entity.domain.bo;

import com.zsmall.common.domain.bo.AttachmentBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 提交退款申请
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SubmitRefundApplyBo {

    /**
     * 主订单编号(主单提交用)
     */
    private String orderNo;

    /**
     * 子单编号
     */
    private String orderItemNo;

    /**
     * 退款规则编号
     */
    private String refundRuleNo;

    /**
     * 售后描述
     */
    private String description;

    /**
     * 申请金额
     */
    private BigDecimal amount;

    /**
     *图片URL集合
     */
    List<AttachmentBo> attachments;
    private String tenantId;
    /**
     * 主订单编号(主单提交用)
     */
    private Boolean isAbnormal;
    private List<String> orderRefundNos= new ArrayList<>();

    public List<String> addOrderRefundNos(String orderRefundNo){
        orderRefundNos.add(orderRefundNo);
        return this.orderRefundNos;
    }
}
