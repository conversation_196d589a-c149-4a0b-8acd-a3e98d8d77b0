package com.zsmall.order.utils;

import com.hengjian.common.core.utils.SpringUtils;
import com.zsmall.common.domain.dto.OpenApiOrderReviewDTO;
import com.zsmall.order.event.RefundApplyHandleEvent;
import com.zsmall.order.event.SupplierRefundHandleEvent;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/14 13:51
 */
public class ZSMallOrderRefundEventUtils {
    public static void automaticRefund(List<String> orderRefundNos, Boolean isAuto, Boolean supplierCall,
                                       Boolean isAbnormal) {
        RefundApplyHandleEvent refundApplyHandleEvent = new RefundApplyHandleEvent();
        refundApplyHandleEvent.setOrderRefundNoList(orderRefundNos);
        refundApplyHandleEvent.setIsAuto(isAuto);
        refundApplyHandleEvent.setSupplierCall(supplierCall);
        refundApplyHandleEvent.setAbnormal(isAbnormal);
        SpringUtils.context().publishEvent(refundApplyHandleEvent);
    }

    public static void supplierAutomaticRefund(OpenApiOrderReviewDTO openApiOrderReviewDTO,String tenantId) {
        SupplierRefundHandleEvent refundApplyHandleEvent = new SupplierRefundHandleEvent();
        refundApplyHandleEvent.setTenantId(tenantId);
        refundApplyHandleEvent.setOrderReviewOpinion(openApiOrderReviewDTO.getOrderReviewOpinion());
        refundApplyHandleEvent.setOrderNo(openApiOrderReviewDTO.getOrderNo());


        SpringUtils.context().publishEvent(refundApplyHandleEvent);
    }
}
