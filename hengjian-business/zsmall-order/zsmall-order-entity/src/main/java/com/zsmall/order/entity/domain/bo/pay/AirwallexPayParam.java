package com.zsmall.order.entity.domain.bo.pay;

import cn.hutool.core.lang.UUID;
import com.zsmall.common.domain.airwallex.payment.intents.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年8月11日  11:36
 * @description: 空中云汇支付参数
 */
@Data
public class AirwallexPayParam implements Serializable {

    private static final long serialVersionUID = 1L;

    private BigDecimal amount;

    private String currency;

    private List<Product> productList;

    private Shipping shipping;

    private Order order;

    private Customer customer;

    /**
     * 关联信息用来存储支付的相关信息
     * 仓储费存储的是仓储费ID
     */
    private List<String> relatesInfo;

    /**
     * 仓储费支付参数初始化
     *
     * @param customerEmail
     * @param customerPhoneNumber
     */
    public void storageFeeInit(String customerEmail,String customerPhoneNumber,BigDecimal unitPrice){
        Order order = new Order();
        Shipping shipping = new Shipping();
        Customer customer = new Customer();
        List<Product> productList = new ArrayList<>();
        Product product = new Product();
        product.setCode("");
        product.setDesc("");
        product.setName("Storage Fee Card");
        product.setQuantity(1L);
        product.setSku("SF8315");
        product.setType("physical_good");
        product.setUnit_price(unitPrice);
        productList.add(product);
        order.setProducts(productList);
        ShippingAddress shippingAddress = new ShippingAddress();
        shippingAddress.setCity("Davenport");
        shippingAddress.setCountry_code("US");
        shippingAddress.setPostcode("52804");
        shippingAddress.setState("IA");
        shippingAddress.setStreet("5 Birchwood Ct");
        shipping.setAddress(shippingAddress);
        order.setShipping(shipping);
        CustomerAdditionalInfo customerAdditionalInfo = new CustomerAdditionalInfo();
        customerAdditionalInfo.setRegistration_date(LocalDate.now().toString());
        customer.setAdditional_info(customerAdditionalInfo);
        customer.setFirst_name("Autumn Rose");
        customer.setLast_name("Sims");
        customer.setMerchant_customer_id(UUID.fastUUID().toString());
        customer.setEmail(customerEmail);
        customer.setPhone_number(customerPhoneNumber);
        // 给类中的order赋值
        this.order = order;
        this.shipping = shipping;
        this.customer = customer;
    }
}
