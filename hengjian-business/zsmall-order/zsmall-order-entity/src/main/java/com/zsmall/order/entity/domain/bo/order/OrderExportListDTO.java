package com.zsmall.order.entity.domain.bo.order;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2024/05/21 14:44
 */
@Data
@ExcelIgnoreUnannotated
public class OrderExportListDTO {
    @ExcelIgnore
    private Long id;
    @ExcelIgnore
    private Long orderItemId;
    @ExcelProperty("订单号")
    private String orderNo;
    @ExcelProperty("渠道订单号")
    private String channelOrderNo;
    @ExcelProperty("订单状态")
    private String orderState;
    @ExcelProperty("发运状态")
    private String fulfillmentProgress;
    @ExcelProperty("币种")
    private String currencyCode;
    @ExcelProperty("销售额金额")
    private String totalAmount;
    @ExcelProperty("应付金额")
    private String productAmount;
    @ExcelProperty("实付金额")
    private String total;
    @ExcelProperty("商品单价")
    private String productSkuUnitPrice;
    @ExcelProperty("操作费")
    private String operationFee;
    @ExcelProperty("尾程派送费")
    private String finalDeliveryFee;
    @ExcelProperty("商品小计")
    private String commoditySubtotal;
//    @ExcelProperty("定金")
//    private String totalDeposit;
    @ExcelProperty("SKU ID")
    private String productSkuCode;
    @ExcelProperty("ERP SKU")
    private String erpSku;
    @ExcelProperty("数量")
    private String quantity;
//    @ExcelProperty("单价")
//    private String price;
    @ExcelProperty("渠道类型")
    private String channelType;
    @ExcelProperty("渠道SKU")
    private String channelSku;
    @ExcelProperty("渠道店铺名")
    private String channelName;
    @ExcelProperty("发货方式")
    private String logisticsType;
    @ExcelProperty("发货仓库")
    private String logisticsWarehouse;
    @ExcelProperty("创建时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private String createTime;
    @ExcelProperty("下单时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private String channelOrderTime;
    @ExcelProperty("支付时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private String payTime;


    @ExcelProperty("最晚发货时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private String latestDeliveryTime;

    @ExcelProperty("发货时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private String dispatchedTime	;
    @ExcelProperty("分销商ID")
    private String tenantId;
    @ExcelProperty("供应商ID")
    private String supplierTenantId;
    @ExcelProperty("产品名称")
    private String name;
    @ExcelProperty("客户名称")
    private String recipient;
    @ExcelProperty("收货地址")
    private String address;
    @ExcelProperty("城市")
    private String city;
    @ExcelProperty("州/省")
    private String state;
    @ExcelProperty("国家")
    private String country;
    @ExcelProperty("邮编")
    private String zipCode;
    @ExcelProperty("手机号")
    private String phoneNumber;
    @ExcelProperty("承运服务")
    private String logisticsCarrier;
    @ExcelProperty("物流单号")
    private String logisticsTrackingNo;
    @ExcelProperty(value ="发货异常")
    private String shipmentException;


    /**
     * 原始应付单价（供货商）
     */
    @ExcelIgnore
    private String originalPayableUnitPrice;
    /**
     * 平台应付单价（平台、分销商）
     */
    @ExcelIgnore
    private String platformPayableUnitPrice;

    /**
     * 原始操作费总金额（供货商）
     */
    @ExcelIgnore
    private String originalTotalOperationFee;
    /**
     * 原始尾程派送费总金额（供货商）
     */
    @ExcelIgnore
    private String originalTotalFinalDeliveryFee;

    /**
     * 原始应付总金额（供货商）
     */
    @ExcelIgnore
    private String originalPayableTotalAmount;
    /**
     *原始实际支付总金额（供应商）
     */
    @ExcelIgnore
    private String originalActualTotalAmount;
    /**
     * 原始销售额总金额（供货商）
     */
    @ExcelIgnore
    private String originalTotalProductAmount;
    /**
     * 原始定金
     */
    @ExcelIgnore
    private String originalPrepaidTotalAmount;

    /**
     * 平台尾程派送费总额
     */
    @ExcelIgnore
    private String platformTotalFinalDeliveryFee;
    /**
     * 平台操作费总金额（平台、分销商）
     */
    @ExcelIgnore
    private String platformTotalOperationFee;
    /**
     *平台应付总金额（平台、分销商）
     */
    @ExcelIgnore
    private String platformPayableTotalAmount;
    /**
     * 平台实际支付总金额（平台、分销商）
     */
    @ExcelIgnore
    private String platformActualTotalAmount;
    /**
     * 平台销售额总金额（平台、分销商）
     */
    @ExcelIgnore
    private String platformTotalProductAmount;
    /**
     * 平台定金
     */
    @ExcelIgnore
    private String platformPrepaidTotalAmount;

    @ExcelIgnore
    private BigDecimal originalTotalPickUpPrice;
    @ExcelIgnore
    private BigDecimal originalTotalDropShippingPrice;
    @ExcelIgnore
    private Long channelID;
    @ExcelIgnore
    private String channelAlias;
    @ExcelIgnore
    private Integer exceptionCode;
    /**
     * 平台产品单价（平台、分销商）
     */
    @ExcelIgnore
    private BigDecimal platformUnitPrice;

    /**
     * 原始产品单价（供货商）
     */
    @ExcelIgnore
    private BigDecimal originalUnitPrice;
    @ExcelIgnore
    private String sku;


}
