package com.zsmall.order.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.order.entity.domain.OrderItemShippingRecord;
import com.zsmall.order.entity.domain.vo.OrderItemShippingRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 子订单出货单Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrderItemShippingRecordMapper extends BaseMapperPlus<OrderItemShippingRecord, OrderItemShippingRecordVo> {

    @InterceptorIgnore(tenantLine = "true")
    boolean existsShippingNo(@Param("shippingNo") String shippingNo);

    List<OrderItemShippingRecord> getListByOrderNoAndType(@Param("orderNo") String orderNo, @Param("stockManager") String stockManager);

    @InterceptorIgnore(tenantLine = "true")
    List<OrderItemShippingRecord> getListBySystemManaged(
        @Param("warehouseType") String warehouseType,
        @Param("shippingStateList") List<ShippingStateEnum> shippingStateList,
        @Param("systemManaged") Boolean systemManaged);

    List<OrderItemShippingRecord> getAllOrderItemShippingRecord(@Param("orderNo")  List<String> itemNoList);

    List<OrderItemShippingRecord> queryByOrderExtendId(String orderExtendId);
    List<OrderItemShippingRecord> queryListByOrderExtendId(@Param("orderExtendIds")List<String> orderExtendIds);
}
