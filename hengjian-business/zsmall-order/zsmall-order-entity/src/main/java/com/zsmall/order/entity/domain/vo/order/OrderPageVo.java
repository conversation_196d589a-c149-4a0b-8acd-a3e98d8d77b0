package com.zsmall.order.entity.domain.vo.order;

import cn.hutool.json.JSONObject;
import com.hengjian.common.translation.annotation.Translation;
import com.hengjian.common.translation.constant.TransConstant;
import com.zsmall.common.domain.vo.IntactAddressInfoVo;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 通用参数-订单信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-订单信息")
public class OrderPageVo {
    @ApiModelProperty(value = "订单编号")
    @Schema(title = "订单编号")
    private String orderId;
    @ApiModelProperty(value = "币种")
    private String currency;
    @ApiModelProperty(value = "订单类型：Normal-普通订单，Wholesale-国外现货")
    @Schema(title = "订单类型：Normal-普通订单，Wholesale-国外现货")
    private String orderType = "Normal";

    @ApiModelProperty(value = "渠道订单编号")
    @Schema(title = "渠道订单编号")
    private String channelOrderId;
    @ApiModelProperty(value = "渠道订单时间")
    @Schema(title = "渠道订单时间")
    private Date channelOrderTime;
    @ApiModelProperty(value = "渠道店铺名")
    @Schema(title = "渠道店铺名")
    private String channelAlias;

    @ApiModelProperty(value = "渠道类型")
    @Schema(title = "渠道类型")
    private String channelType;

    @ApiModelProperty(value = "渠道订单时间")
    @Schema(title = "渠道订单时间")
    private String orderChannelTime;

    @ApiModelProperty(value = "支付时间")
    @Schema(title = "支付时间")
    private String payDate;

    @ApiModelProperty(value = "金额总计")
    @Schema(title = "金额总计")
    private String totalAmount;

    @ApiModelProperty(value = "商品金额")
    @Schema(title = "商品金额")
    private String productAmount;

    @ApiModelProperty(value = "操作费")
    @Schema(title = "操作费")
    private String operationFee;

    @ApiModelProperty(value = "尾程派送费")
    @Schema(title = "尾程派送费")
    private String finalDeliveryFee;

    @ApiModelProperty(value = "运费（批发订单）")
    @Schema(title = "运费（批发订单）")
    private String shippingCost;

    @ApiModelProperty(value = "总价")
    @Schema(title = "总价")
    private String total;

    @ApiModelProperty(value = "总价（BigDecimal类型）")
    @Schema(title = "总价（BigDecimal类型）")
    private BigDecimal totalNumber;

    @ApiModelProperty(value = "总订金")
    @Schema(title = "总订金")
    private String totalDeposit;

    @ApiModelProperty(value = "利润")
    @Schema(title = "利润")
    private String margin;

    @ApiModelProperty(value = "顾客")
    @Schema(title = "顾客")
    private String customer;

    @ApiModelProperty(value = "子订单数量")
    @Schema(title = "子订单数量")
    private Integer item;

    @ApiModelProperty(value = "商品总数量")
    @Schema(title = "商品总数量")
    private Integer totalQuantity;

    @ApiModelProperty(value = "销售通道")
    @Schema(title = "销售通道")
    private String salesChannel;

    @ApiModelProperty(value = "创建日期")
    @Schema(title = "创建日期")
    private String startDate;

    @ApiModelProperty(value = "创建时间")
    @Schema(title = "创建时间")
    private String startTime;

    @ApiModelProperty(value = "买家订单状态")
    @Schema(title = "买家订单状态")
    private String buyerOrderStatus;

    @ApiModelProperty(value = "订单状态")
    @Schema(title = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "支付失败信息")
    @Schema(title = "支付失败信息")
    private JSONObject payFailedMessage;

    @ApiModelProperty(value = "用户名")
    @Schema(title = "用户名")
    private String customerName;

    @ApiModelProperty(value = "订单备注")
    @Schema(title = "订单备注")
    private String notes;

    @ApiModelProperty(value = "联系方式")
    @Schema(title = "联系方式")
    private String contactInformation;

    @ApiModelProperty(value = "帐单地址")
    @Schema(title = "帐单地址")
    private String billingAddress;

    @ApiModelProperty(value = "履约状态")
    @Schema(title = "履约状态")
    private String fulfillment;

    @ApiModelProperty(value = "履约错误信息（中文）")
    @Schema(title = "履约错误信息（中文）")
    private String fulfillmentFailMessage_zh_CN;

    @ApiModelProperty(value = "履约错误信息（英文）")
    @Schema(title = "履约错误信息（英文）")
    private String fulfillmentFailMessage_en_US;

    @ApiModelProperty(value = "运送地址信息")
    @Schema(title = "运送地址信息")
    private IntactAddressInfoVo shipTo;

    @ApiModelProperty(value = "账单地址信息")
    @Schema(title = "账单地址信息")
    private IntactAddressInfoVo billTo;

    @ApiModelProperty(value = "电话")
    @Schema(title = "电话")
    private String phoneNumber;

    @ApiModelProperty(value = "分销商id")
    @Schema(title = "分销商id")
    private String distributorId;

    @ApiModelProperty(value = "分销商店名")
    @Translation(type = TransConstant.NOT_TENANT_USER_ID_TO_NAME, mapper = "createBy", other = "Sensitive")
    @Schema(title = "分销商店名")
    private String distributor;

    @ApiModelProperty(value = "创建人")
    @Schema(title = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "供应商id")
    @Schema(title = "供应商id")
    private String supplierIds;

    @ApiModelProperty(value = "子订单列表")
    @Schema(title = "子订单列表")
    private List<OrderItemVo> orderItems;

    @ApiModelProperty(value = "发货方式：PickUp-自提，DropShipping-代发")
    @Schema(title = "发货方式：PickUp-自提，DropShipping-代发")
    private String logisticsType;

    @ApiModelProperty(value = "下单时间和当前时间的时差")
    @Schema(title = "下单时间和当前时间的时差")
    private Long timeDifference;

    @ApiModelProperty(value = "物流账号")
    @Schema(title = "物流账号")
    private String logisticsAccount;

    @ApiModelProperty(value = "物流账号邮编")
    @Schema(title = "物流账号邮编")
    private String logisticsAccountZipCode;

    @ApiModelProperty(value = "第三方编码")
    @Schema(title = "第三方编码")
    private String zipCode;

    @ApiModelProperty(value = "物流服务")
    @Schema(title = "物流服务")
    private String logisticsServiceName;

    @ApiModelProperty(value = "是否可以确认收货")
    @Schema(title = "是否可以确认收货")
    private Boolean canConfirmReceipt = false;

//    @ApiModelProperty(value = "订单附件展示Url")
//    @Schema(title = "订单附件展示Url")
//    private String attachmentShowUrl;
//
//    @ApiModelProperty(value = "订单附件名")
//    @Schema(title = "订单附件名")
//    private String attachmentName;

    @ApiModelProperty(value = "退款金额")
    @Schema(title = "退款金额")
    private String refundAmount;

    @ApiModelProperty(value = "主订单当前退款状态：NotRefund-从未退款，Refunding-退款中，Refunded-退款成功，Reject-退款被拒")
    @Schema(title = "主订单当前退款状态：NotRefund-从未退款，Refunding-退款中，Refunded-退款成功，Reject-退款被拒")
    private String orderRefundStatus ;

    @ApiModelProperty(value = "主订单可申请退款金额")
    @Schema(title = "主订单可申请退款金额")
    private BigDecimal refundExecutableAmount;

    @ApiModelProperty(value = "主订单售后信息")
    @Schema(title = "主订单售后信息")
    private OrderRefundItemBody orderRefundInfo;

    @ApiModelProperty(value = "现货商品图片")
    @Schema(title = "现货商品图片")
    private String imageShowUrl;

    @ApiModelProperty(value = "现货商品名")
    @Schema(title = "现货商品名")
    private String productName;

    /**
     * 最迟交货时间
     */
    private String latestDeliveryTime;

    /**
     * tracking上传标识 1：失败 其他正常
     */
    private Integer trackingFlag;

    /**
     * 异常code 0:无异常 1:商品映射异常 2:订单支付异常 3:库存不足异常 4:发货方式异常
     */
    private  Integer exceptionCode;
    /**
     *  订单来源 1: 接口接入 2: excel导入 3: 商城下单 4: openApi
     */
    private  Integer orderSource;
    /**
     * 2：为展示订单,其他非展示订单
     */
    private  Integer isShow;
    /**
     * 渠道ID
     */
    private Long channelId;
    /**
     * 最晚发货时间
     */
    private Date latestShipDate;

    /**
     * 仓库预计发货时间
     */
    private Date  warehouseExpectedShipDate;

    /**
     * 订单关联仓库信息
     */
    private String  warehouseInfo;
    /**
     * 站点
     */
    private String countryCode;
//    /**
//     * 币种
//     */
//    private String currency;
    /**
     * 币种符号
     */
    private String currencySymbol;
    /**
     * 订单扩展id(非LTL业务与orderNo一致)
     */
    private String orderExtendId;
    /**
     * 取消状态 0:无，1取消中，2取消成功，3取消失败
     */
    private Integer cancelStatus;
    /**
     * 主订单履约进度（未发货、已发货、已履约等）
     */
    private String fulfillmentProgress;

    /**
     * erp 发货异常
     */
    private String shipmentException;
    /**
     * 参与活动类型（为空代表未参与活动）
     */
    private String activityType;

    /**
     * 参与的子活动编号（为空代表未参与）
     */
    private String activityCode;
}
