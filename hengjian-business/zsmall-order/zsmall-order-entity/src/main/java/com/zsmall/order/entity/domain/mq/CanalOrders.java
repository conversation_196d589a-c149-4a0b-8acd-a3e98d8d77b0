package com.zsmall.order.entity.domain.mq;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CanalOrders {
    /**
     * 订单ID
     */
    private Long id;

    /**
     * 主订单类型
     */
    private String orderType;

    /**
     * 物流类型：PickUp-自提，DropShipping-代发
     */
    private String logisticsType;

    /**
     * 主订单编号
     */
    private String orderNo;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 渠道业务确认状态
     */
    private String channelReceiptStatus;

    /**
     * 渠道店铺主键
     */
    private Long channelId;

    /**
     * 渠道别名
     */
    private String channelAlias;

//    @IndexField(fieldType = FieldType.KEYWORD)
//    private String channelName;

    /**
     * 销售渠道订单编号（如果有则记录）
     */
    private String channelOrderNo;

    /**
     * 币种
     */
    private String currency;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 销售渠道订单号（如果有则记录，展示用）
     */
    private String channelOrderName;

    /**
     * 渠道订单时间
     */
    private String channelOrderTime;

    /**
     * 主订单履约进度（未发货、已发货、已履约等）
     */
    private String fulfillmentProgress;

    /**
     * 三方子订单id
     */
    private String lineOrderItemId;

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 站点(待补充)
     */
    private String countryCode;

    /**
     * 是否已分割
     */
    private Integer isSplit;

    /**
     * 主订单状态：待支付，已支付等
     */
    private String orderState;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 支付错误信息
     */
    private String payErrorMessage;

    /**
     * 订单商品总数量
     */
    private Integer totalQuantity;

    /**
     * 原始商品单价总额（供应商）
     */
    private BigDecimal originalTotalProductAmount;

    /**
     * 原始操作费总金额（供货商）
     */
    private BigDecimal originalTotalOperationFee;

    /**
     * 原始尾程派送费总额（供货商）
     */
    private BigDecimal originalTotalFinalDeliveryFee;

    /**
     * 原始自提价总额（供货商）
     */
    private BigDecimal originalTotalPickUpPrice;

    /**
     * 原始代发价总额（供货商）
     */
    private BigDecimal originalTotalDropShippingPrice;

    /**
     * 原始应付总金额（供货商）
     */
    private BigDecimal originalPayableTotalAmount;

    /**
     * 原始已预付总金额（供货商）
     */
    private BigDecimal originalPrepaidTotalAmount;

    /**
     * 原始实际支付总金额（供应商）(应付金额-销售额金额)
     */
    private BigDecimal originalActualTotalAmount;

    /**
     * 原始售后可执行总金额（供货商）
     */
    private BigDecimal originalRefundExecutableAmount;

    /**
     * 平台商品单价总额（平台、分销商）
     */
    private BigDecimal platformTotalProductAmount;

    /**
     * 平台操作费总金额（平台、分销商）
     */
    private BigDecimal platformTotalOperationFee;

    /**
     * 平台尾程派送费总额
     */
    private BigDecimal platformTotalFinalDeliveryFee;

    /**
     * 平台自提价总额（平台、分销商）
     */
    private BigDecimal platformTotalPickUpPrice;

    /**
     * 平台代发价总额（平台、分销商）
     */
    private BigDecimal platformTotalDropShippingPrice;

    /**
     * 平台应付总金额（平台、分销商）
     */
    private BigDecimal platformPayableTotalAmount;

    /**
     * 平台已预付总金额（平台、分销商）
     */
    private BigDecimal platformPrepaidTotalAmount;

    /**
     * 平台实际支付总金额（平台、分销商）(应付金额-销售额金额)
     */
    private BigDecimal platformActualTotalAmount;

    /**
     * 平台售后可执行总金额（平台、分销商）
     */
    private BigDecimal platformRefundExecutableAmount;

    /**
     * 归属订单导入记录主键
     */
    private Long importRecordId;

    /**
     * 订单备注
     */
    private String orderNote;

    /**
     * tracking上传标识 1：失败 其他正常
     */
    private Integer trackingFlag;

    /**
     * 异常code 0:无异常 1:商品映射异常 2:订单支付异常 3:库存不足异常 4:发货方式异常 5:尾程异常 6:测算异常
     */
    private Integer exceptionCode;

    /**
     * 1：非展示订单 2：为展示订单
     */
    private Integer isShow;

    /**
     * 自定义 isShow 的 setter 方法
     * @param isShow 传入的 isShow 值
     */
    public void setIsShow(Integer isShow) {
        this.isShow = (isShow == null) ? 1 : isShow;
    }

    /**
     * 订单来源 1: 接口接入 2: excel导入 3: 商城下单 4: openApi
     */
    private Integer orderSource;

    /**
     * 渠道仓库code
     */
    private String channelWarehouseCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private Integer delFlag;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 仓库预计发货时间
     */
    private String warehouseExpectedShipDate;

    /**
     * 最晚发货时间
     */
    private String latestShipDate;

    /**
     * 最迟交货时间
     */
    private String latestDeliveryTime;


    /**
     * 支付方式
     */
    private String payType;

    /**
     * 扩展id
     */
    private String orderExtendId;

    /**
     * 0:无，1取消中，2取消成功，3取消失败
     */
    private Integer cancelStatus;
    /**
     * erp 发货异常
     */
    private String shipmentException;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 活动编号
     */
    private String activityCode;
}
