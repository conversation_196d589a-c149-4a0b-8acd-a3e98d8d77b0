package com.zsmall.order.entity.domain.mq;


import cn.hutool.core.util.NumberUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldStrategy;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Settings(shardsNum = 3,replicasNum=2)
@EqualsAndHashCode
@IndexName(value = "distribution_es_orders")
public class EsOrdersDTO implements Serializable {

    /**
     * 订单ID
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private Long id;

    /**
     * 主订单类型
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String orderType;

    /**
     * 物流类型：PickUp-自提，DropShipping-代发
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String logisticsType;

    /**
     * 主订单编号
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String orderNo;
    /**
     * 是否需要贴标签 0:是 1:否
     */
    private Integer isNeedLabeling;
    /**
     * 渠道类型
     */
    @IndexField(fieldType = FieldType.KEYWORD,strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String channelType;

    /**
     * 渠道业务确认状态
     */
    private String channelReceiptStatus;

    /**
     * 渠道店铺主键
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private Long channelId;

    /**
     * 渠道别名
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String channelAlias;

//    @IndexField(fieldType = FieldType.KEYWORD)
//    private String channelName;

    /**
     * 销售渠道订单编号（如果有则记录）
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String channelOrderNo;

    /**
     * 币种
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String currency;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 销售渠道订单号（如果有则记录，展示用）
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String channelOrderName;

    /**
     * 渠道订单时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String channelOrderTime;

    /**
     * 主订单履约进度（未发货、已发货、已履约等）
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String fulfillmentProgress;

    /**
     * 三方子订单id
     */
    private String lineOrderItemId;

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 站点(待补充)
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String countryCode;

    /**
     * 是否已分割
     */
    private Integer isSplit;

    /**
     * 主订单状态：待支付，已支付等
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String orderState;

    /**
     * 支付时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private String payTime;

    /**
     * 支付错误信息
     */
    private String payErrorMessage;

    /**
     * 订单商品总数量
     */
    private Integer totalQuantity;

    /**
     * 原始商品单价总额（供应商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal originalTotalProductAmount;

    /**
     * 原始操作费总金额（供货商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal originalTotalOperationFee;

    /**
     * 原始尾程派送费总额（供货商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal originalTotalFinalDeliveryFee;

    /**
     * 原始自提价总额（供货商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal originalTotalPickUpPrice;

    /**
     * 原始代发价总额（供货商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal originalTotalDropShippingPrice;

    /**
     * 原始应付总金额（供货商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal originalPayableTotalAmount;

    /**
     * 原始已预付总金额（供货商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal originalPrepaidTotalAmount;

    /**
     * 原始实际支付总金额（供应商）(应付金额-销售额金额)
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT,strategy = FieldStrategy.IGNORED)
    private BigDecimal originalActualTotalAmount;

    /**
     * 原始售后可执行总金额（供货商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal originalRefundExecutableAmount;

    /**
     * 平台商品单价总额（平台、分销商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal platformTotalProductAmount;

    /**
     * 平台操作费总金额（平台、分销商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal platformTotalOperationFee;

    /**
     * 平台尾程派送费总额
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal platformTotalFinalDeliveryFee;

    /**
     * 平台自提价总额（平台、分销商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal platformTotalPickUpPrice;

    /**
     * 平台代发价总额（平台、分销商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal platformTotalDropShippingPrice;

    /**
     * 平台应付总金额（平台、分销商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal platformPayableTotalAmount;

    /**
     * 平台已预付总金额（平台、分销商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal platformPrepaidTotalAmount;

    /**
     * 平台实际支付总金额（平台、分销商）(应付金额-销售额金额)
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal platformActualTotalAmount;

    /**
     * 平台售后可执行总金额（平台、分销商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal platformRefundExecutableAmount;

    /**
     * 归属订单导入记录主键
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private Long importRecordId;

    /**
     * 订单备注
     */
    private String orderNote;

    /**
     * tracking上传标识 1：失败 其他正常
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer trackingFlag;

    /**
     * 异常code 0:无异常 1:商品映射异常 2:订单支付异常 3:库存不足异常 4:发货方式异常 5:尾程异常 6:测算异常
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer exceptionCode;

    /**
     * 1：非展示订单 2：为展示订单
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer isShow;

    /**
     * 自定义 isShow 的 setter 方法
     * @param isShow 传入的 isShow 值
     */
    public void setIsShow(Integer isShow) {
        this.isShow = (isShow == null) ? 1 : isShow;
    }

    /**
     * 订单来源 1: 接口接入 2: excel导入 3: 商城下单 4: openApi
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer orderSource;

    /**
     * 渠道仓库code
     */
    private String channelWarehouseCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @IndexField(fieldType = FieldType.INTEGER,strategy = FieldStrategy.IGNORED)
    private Integer delFlag;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private String createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private String updateTime;

    /**
     * 仓库预计发货时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private String warehouseExpectedShipDate;

    /**
     * 最晚发货时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private String latestShipDate;

    /**
     * 最迟交货开始时间,订单维度
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private String latestDeliveryTime;

    /**
     * 支付方式
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String payType;

    /**
     * 扩展id
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String orderExtendId;

    /**
     * 0:无，1取消中，2取消成功，3取消失败
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer cancelStatus;
    /**
     * erp 发货异常
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String shipmentException;

    /*---------------- order_item--------------------------*/

    /**
     * 子订单编号
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String orderItemNo;

    /**
     * 租户编号（分销商）
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String tenantId;
    /**
     * 供货商租户编号
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String supplierTenantId;

    /**
     * Sku唯一编号（ItemNo.）
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String productSkuCode;

    /**
     * 渠道sku
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String channelSku;
    /**
     * 发货时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private String dispatchedTime;

    /*order_item_product_sku*/


    /**
     * 最小库存单位（Sku）
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String sku;

    /**
     * 商品统一代码（UPC）
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String upc;

    /**
     * 第三方系统库存单位
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String erpSku;

    /**
     * 分销商映射Sku（第三方渠道订单才有）
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String mappingSku;

    /**
     * 商品名称
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_SMART, searchAnalyzer = Analyzer.IK_SMART)
    private String productName;

    /**
     * 商品图片展示地址
     */
    private String imageShowUrl;

    /**
     * 供货商仓库唯一系统编号（最终选定仓库）
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String warehouseSystemCode;



    /*order_item_tracking_record*/

    @IndexField(fieldType = FieldType.KEYWORD)
    private List<String>  logisticsTrackingNos;

    /**
     * 物流跟踪单号
     */
    @IndexField(exist = false)
    private String logisticsTrackingNo;
    /*---------------- order_item_price--------------------------*/

    /**
     * 原始产品单价（供货商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT,strategy = FieldStrategy.IGNORED)
    private BigDecimal originalUnitPrice;

    /**
     * 原始操作费（供货商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal originalOperationFee;

    /**
     * 原始尾程派送费（供货商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal originalFinalDeliveryFee;



    /**
     * 平台产品单价（平台、分销商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal platformUnitPrice;

    /**
     * 平台操作费（平台、分销商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal platformOperationFee;

    /**
     * 平台尾程派送费（平台、分销商）
     */
    @IndexField(fieldType = FieldType.SCALED_FLOAT)
    private BigDecimal platformFinalDeliveryFee;

    /**
     * 活动类型
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String activityType;

    /**
     * 活动编号
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String activityCode;
    /*---------------- warehouse------------------------------*/
//    /**
//     * 仓库名称
//     */
//    private String warehouseName;
    /*---------------- order_logistics_info--------------------------*/

    /**
     * 物流账户
     */
    private String logisticsAccount;

    /**
     * 物流账户邮政编码
     */
    private String logisticsAccountZipCode;




    /*order_address_info*/
//    /**
//     * 接收人姓名
//     */
//    private String recipient;
//
//    /**
//     * 县
//     */
//    private String county;
//
//    /**
//     * 国家
//     */
//    private String country;
//
//    /**
//     * 省/州
//     */
//    private String state;
//
//    /**
//     * 城市
//     */
//    private String city;
//
//    /**
//     * 详细地址1
//     */
//    private String address1;
//
//    /**
//     * 详细地址2
//     */
//    private String address2;
//
//    /**
//     * 邮箱
//     */
//    private String email;
//
//    /**
//     * 电话号码
//     */
//    private String phoneNumber;
//
//    /**
//     * 邮政编码
//     */
//    private String zipCode;

    /**
     * 获取原始商品单价总额（供应商）
     */
    public BigDecimal getOriginalTotalProductAmount() {
        return originalTotalProductAmount != null ? NumberUtil.round(originalTotalProductAmount, 2) : null;
    }

    /**
     * 设置原始商品单价总额（供应商）
     */
    public void setOriginalTotalProductAmount(BigDecimal originalTotalProductAmount) {
        this.originalTotalProductAmount = originalTotalProductAmount != null ? NumberUtil.round(originalTotalProductAmount, 2) : null;
    }

    /**
     * 获取原始操作费总金额（供货商）
     */
    public BigDecimal getOriginalTotalOperationFee() {
        return originalTotalOperationFee != null ? NumberUtil.round(originalTotalOperationFee, 2) : null;
    }

    /**
     * 设置原始操作费总金额（供货商）
     */
    public void setOriginalTotalOperationFee(BigDecimal originalTotalOperationFee) {
        this.originalTotalOperationFee = originalTotalOperationFee != null ? NumberUtil.round(originalTotalOperationFee, 2) : null;
    }

    /**
     * 获取原始尾程派送费总额（供货商）
     */
    public BigDecimal getOriginalTotalFinalDeliveryFee() {
        return originalTotalFinalDeliveryFee != null ? NumberUtil.round(originalTotalFinalDeliveryFee, 2) : null;
    }

    /**
     * 设置原始尾程派送费总额（供货商）
     */
    public void setOriginalTotalFinalDeliveryFee(BigDecimal originalTotalFinalDeliveryFee) {
        this.originalTotalFinalDeliveryFee = originalTotalFinalDeliveryFee != null ? NumberUtil.round(originalTotalFinalDeliveryFee, 2) : null;
    }

    /**
     * 获取原始自提价总额（供货商）
     */
    public BigDecimal getOriginalTotalPickUpPrice() {
        return originalTotalPickUpPrice != null ? NumberUtil.round(originalTotalPickUpPrice, 2) : null;
    }

    /**
     * 设置原始自提价总额（供货商）
     */
    public void setOriginalTotalPickUpPrice(BigDecimal originalTotalPickUpPrice) {
        this.originalTotalPickUpPrice = originalTotalPickUpPrice != null ? NumberUtil.round(originalTotalPickUpPrice, 2) : null;
    }

    /**
     * 获取原始代发价总额（供货商）
     */
    public BigDecimal getOriginalTotalDropShippingPrice() {
        return originalTotalDropShippingPrice != null ? NumberUtil.round(originalTotalDropShippingPrice, 2) : null;
    }

    /**
     * 设置原始代发价总额（供货商）
     */
    public void setOriginalTotalDropShippingPrice(BigDecimal originalTotalDropShippingPrice) {
        this.originalTotalDropShippingPrice = originalTotalDropShippingPrice != null ? NumberUtil.round(originalTotalDropShippingPrice, 2) : null;
    }

    /**
     * 获取原始应付总金额（供货商）
     */
    public BigDecimal getOriginalPayableTotalAmount() {
        return originalPayableTotalAmount != null ? NumberUtil.round(originalPayableTotalAmount, 2) : null;
    }

    /**
     * 设置原始应付总金额（供货商）
     */
    public void setOriginalPayableTotalAmount(BigDecimal originalPayableTotalAmount) {
        this.originalPayableTotalAmount = originalPayableTotalAmount != null ? NumberUtil.round(originalPayableTotalAmount, 2) : null;
    }

    /**
     * 获取原始已预付总金额（供货商）
     */
    public BigDecimal getOriginalPrepaidTotalAmount() {
        return originalPrepaidTotalAmount != null ? NumberUtil.round(originalPrepaidTotalAmount, 2) : null;
    }

    /**
     * 设置原始已预付总金额（供货商）
     */
    public void setOriginalPrepaidTotalAmount(BigDecimal originalPrepaidTotalAmount) {
        this.originalPrepaidTotalAmount = originalPrepaidTotalAmount != null ? NumberUtil.round(originalPrepaidTotalAmount, 2) : null;
    }

    /**
     * 获取原始实际支付总金额（供应商）
     */
    public BigDecimal getOriginalActualTotalAmount() {
        return originalActualTotalAmount != null ? NumberUtil.round(originalActualTotalAmount, 2) : null;
    }

    /**
     * 设置原始实际支付总金额（供应商）
     */
    public void setOriginalActualTotalAmount(BigDecimal originalActualTotalAmount) {
        this.originalActualTotalAmount = originalActualTotalAmount != null ? NumberUtil.round(originalActualTotalAmount, 2) : null;
    }

    /**
     * 获取原始售后可执行总金额（供货商）
     */
    public BigDecimal getOriginalRefundExecutableAmount() {
        return originalRefundExecutableAmount != null ? NumberUtil.round(originalRefundExecutableAmount, 2) : null;
    }

    /**
     * 设置原始售后可执行总金额（供货商）
     */
    public void setOriginalRefundExecutableAmount(BigDecimal originalRefundExecutableAmount) {
        this.originalRefundExecutableAmount = originalRefundExecutableAmount != null ? NumberUtil.round(originalRefundExecutableAmount, 2) : null;
    }

    /**
     * 获取平台商品单价总额（平台、分销商）
     */
    public BigDecimal getPlatformTotalProductAmount() {
        return platformTotalProductAmount != null ? NumberUtil.round(platformTotalProductAmount, 2) : null;
    }

    /**
     * 设置平台商品单价总额（平台、分销商）
     */
    public void setPlatformTotalProductAmount(BigDecimal platformTotalProductAmount) {
        this.platformTotalProductAmount = platformTotalProductAmount != null ? NumberUtil.round(platformTotalProductAmount, 2) : null;
    }

    /**
     * 获取平台操作费总金额（平台、分销商）
     */
    public BigDecimal getPlatformTotalOperationFee() {
        return platformTotalOperationFee != null ? NumberUtil.round(platformTotalOperationFee, 2) : null;
    }

    /**
     * 设置平台操作费总金额（平台、分销商）
     */
    public void setPlatformTotalOperationFee(BigDecimal platformTotalOperationFee) {
        this.platformTotalOperationFee = platformTotalOperationFee != null ? NumberUtil.round(platformTotalOperationFee, 2) : null;
    }

    /**
     * 获取平台尾程派送费总额
     */
    public BigDecimal getPlatformTotalFinalDeliveryFee() {
        return platformTotalFinalDeliveryFee != null ? NumberUtil.round(platformTotalFinalDeliveryFee, 2) : null;
    }

    /**
     * 设置平台尾程派送费总额
     */
    public void setPlatformTotalFinalDeliveryFee(BigDecimal platformTotalFinalDeliveryFee) {
        this.platformTotalFinalDeliveryFee = platformTotalFinalDeliveryFee != null ? NumberUtil.round(platformTotalFinalDeliveryFee, 2) : null;
    }

    /**
     * 获取平台自提价总额（平台、分销商）
     */
    public BigDecimal getPlatformTotalPickUpPrice() {
        return platformTotalPickUpPrice != null ? NumberUtil.round(platformTotalPickUpPrice, 2) : null;
    }

    /**
     * 设置平台自提价总额（平台、分销商）
     */
    public void setPlatformTotalPickUpPrice(BigDecimal platformTotalPickUpPrice) {
        this.platformTotalPickUpPrice = platformTotalPickUpPrice != null ? NumberUtil.round(platformTotalPickUpPrice, 2) : null;
    }

    /**
     * 获取平台代发价总额（平台、分销商）
     */
    public BigDecimal getPlatformTotalDropShippingPrice() {
        return platformTotalDropShippingPrice != null ? NumberUtil.round(platformTotalDropShippingPrice, 2) : null;
    }

    /**
     * 设置平台代发价总额（平台、分销商）
     */
    public void setPlatformTotalDropShippingPrice(BigDecimal platformTotalDropShippingPrice) {
        this.platformTotalDropShippingPrice = platformTotalDropShippingPrice != null ? NumberUtil.round(platformTotalDropShippingPrice, 2) : null;
    }

    /**
     * 获取平台应付总金额（平台、分销商）
     */
    public BigDecimal getPlatformPayableTotalAmount() {
        return platformPayableTotalAmount != null ? NumberUtil.round(platformPayableTotalAmount, 2) : null;
    }

    /**
     * 设置平台应付总金额（平台、分销商）
     */
    public void setPlatformPayableTotalAmount(BigDecimal platformPayableTotalAmount) {
        this.platformPayableTotalAmount = platformPayableTotalAmount != null ? NumberUtil.round(platformPayableTotalAmount, 2) : null;
    }

    /**
     * 获取平台已预付总金额（平台、分销商）
     */
    public BigDecimal getPlatformPrepaidTotalAmount() {
        return platformPrepaidTotalAmount != null ? NumberUtil.round(platformPrepaidTotalAmount, 2) : null;
    }

    /**
     * 设置平台已预付总金额（平台、分销商）
     */
    public void setPlatformPrepaidTotalAmount(BigDecimal platformPrepaidTotalAmount) {
        this.platformPrepaidTotalAmount = platformPrepaidTotalAmount != null ? NumberUtil.round(platformPrepaidTotalAmount, 2) : null;
    }

    /**
     * 获取平台实际支付总金额（平台、分销商）
     */
    public BigDecimal getPlatformActualTotalAmount() {
        return platformActualTotalAmount != null ? NumberUtil.round(platformActualTotalAmount, 2) : null;
    }

    /**
     * 设置平台实际支付总金额（平台、分销商）
     */
    public void setPlatformActualTotalAmount(BigDecimal platformActualTotalAmount) {
        this.platformActualTotalAmount = platformActualTotalAmount != null ? NumberUtil.round(platformActualTotalAmount, 2) : null;
    }

    /**
     * 获取平台售后可执行总金额（平台、分销商）
     */
    public BigDecimal getPlatformRefundExecutableAmount() {
        return platformRefundExecutableAmount != null ? NumberUtil.round(platformRefundExecutableAmount, 2) : null;
    }

    /**
     * 设置平台售后可执行总金额（平台、分销商）
     */
    public void setPlatformRefundExecutableAmount(BigDecimal platformRefundExecutableAmount) {
        this.platformRefundExecutableAmount = platformRefundExecutableAmount != null ? NumberUtil.round(platformRefundExecutableAmount, 2) : null;
    }

    /**
     * 获取原始产品单价（供货商）
     */
    public BigDecimal getOriginalUnitPrice() {
        return originalUnitPrice != null ? NumberUtil.round(originalUnitPrice, 2) : null;
    }

    /**
     * 设置原始产品单价（供货商）
     */
    public void setOriginalUnitPrice(BigDecimal originalUnitPrice) {
        this.originalUnitPrice = originalUnitPrice != null ? NumberUtil.round(originalUnitPrice, 2) : null;
    }

    /**
     * 获取原始操作费（供货商）
     */
    public BigDecimal getOriginalOperationFee() {
        return originalOperationFee != null ? NumberUtil.round(originalOperationFee, 2) : null;
    }

    /**
     * 设置原始操作费（供货商）
     */
    public void setOriginalOperationFee(BigDecimal originalOperationFee) {
        this.originalOperationFee = originalOperationFee != null ? NumberUtil.round(originalOperationFee, 2) : null;
    }

    /**
     * 获取原始尾程派送费（供货商）
     */
    public BigDecimal getOriginalFinalDeliveryFee() {
        return originalFinalDeliveryFee != null ? NumberUtil.round(originalFinalDeliveryFee, 2) : null;
    }

    /**
     * 设置原始尾程派送费（供货商）
     */
    public void setOriginalFinalDeliveryFee(BigDecimal originalFinalDeliveryFee) {
        this.originalFinalDeliveryFee = originalFinalDeliveryFee != null ? NumberUtil.round(originalFinalDeliveryFee, 2) : null;
    }

    /**
     * 获取平台产品单价（平台、分销商）
     */
    public BigDecimal getPlatformUnitPrice() {
        return platformUnitPrice != null ? NumberUtil.round(platformUnitPrice, 2) : null;
    }

    /**
     * 设置平台产品单价（平台、分销商）
     */
    public void setPlatformUnitPrice(BigDecimal platformUnitPrice) {
        this.platformUnitPrice = platformUnitPrice != null ? NumberUtil.round(platformUnitPrice, 2) : null;
    }

    /**
     * 获取平台操作费（平台、分销商）
     */
    public BigDecimal getPlatformOperationFee() {
        return platformOperationFee != null ? NumberUtil.round(platformOperationFee, 2) : null;
    }

    /**
     * 设置平台操作费（平台、分销商）
     */
    public void setPlatformOperationFee(BigDecimal platformOperationFee) {
        this.platformOperationFee = platformOperationFee != null ? NumberUtil.round(platformOperationFee, 2) : null;
    }

    /**
     * 获取平台尾程派送费（平台、分销商）
     */
    public BigDecimal getPlatformFinalDeliveryFee() {
        return platformFinalDeliveryFee != null ? NumberUtil.round(platformFinalDeliveryFee, 2) : null;
    }

    /**
     * 设置平台尾程派送费（平台、分销商）
     */
    public void setPlatformFinalDeliveryFee(BigDecimal platformFinalDeliveryFee) {
        this.platformFinalDeliveryFee = platformFinalDeliveryFee != null ? NumberUtil.round(platformFinalDeliveryFee, 2) : null;
    }
}
