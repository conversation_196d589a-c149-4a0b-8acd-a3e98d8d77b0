<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zsmall-order</artifactId>
        <groupId>com.zsmall</groupId>
        <version>${zsmall.version}</version>
    </parent>
    <dependencies>
        <dependency>
            <groupId>org.dromara.easy-es</groupId>
            <artifactId>easy-es-annotation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dromara.easy-es</groupId>
            <artifactId>easy-es-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall.extend.wms</groupId>
            <artifactId>zsmall-wms-thebizark</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-warehouse-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall.extend.payment</groupId>
            <artifactId>zsmall-payment-payoneer</artifactId>
        </dependency>

    </dependencies>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zsmall-order-entity</artifactId>
    <groupId>com.zsmall</groupId>
    <name>ZS-Mall订单实体模块</name>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

</project>
