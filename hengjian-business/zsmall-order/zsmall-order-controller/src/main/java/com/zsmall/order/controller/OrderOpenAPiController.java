package com.zsmall.order.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.domain.entity.request.OpenApiRequestEntity;
import com.hengjian.openapi.domain.entity.response.OpenApiOrderListResponse;
import com.hengjian.openapi.service.IOpenApiService;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.domain.dto.*;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.order.biz.service.OrderRefundService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.ThirdOrdersApiService;
import com.zsmall.order.entity.domain.OrderAttachment;
import com.zsmall.order.entity.domain.OrderItemShippingRecord;
import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.convert.OrderConvert;
import com.zsmall.order.entity.domain.openapi.OpenApiOrderSearchDTO;
import com.zsmall.order.entity.domain.openapi.OpenApiOrdersDeliveryDTO;
import com.zsmall.order.entity.domain.vo.OrderDetailsAttachmentVo;
import com.zsmall.order.entity.domain.vo.order.OrderPageVo;
import com.zsmall.order.entity.domain.vo.order.OrderPageVoOpenAPI;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.order.entity.mapper.OrdersMapper;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/open/api")
@Slf4j
public class OrderOpenAPiController {
    private static final Logger log = LoggerFactory.getLogger(OrderOpenAPiController.class);
    private final HttpServletRequest request;
    private final OrdersService ordersService;
    private final IOrdersService iOrdersService;
    private final IOpenApiService openApiService;
    private final ThirdOrdersApiService thirdOrdersApiService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IOrderAttachmentService iOrderAttachmentService;
  private final ISysTenantService sysTenantService;
  private final IOrderItemService iOrderItemService;
  private final IOrderItemShippingRecordService iOrderItemShippingRecordService;
  private final IOrderItemProductSkuService iOrderItemProductSkuService;
  private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
  private final RabbitTemplate rabbitTemplate;
  private final IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;
  private final ITenantSalesChannelService iTenantSalesChannelService;
  private final IWarehouseService iWarehouseService;
  private final OrdersMapper ordersMapper;
    private final OrderRefundService orderRefundService;


    /**
     * 获取订单信息API
     */
    @GetMapping("/order/getOrderDetail")
    public R getOrderDetailByOrderNos(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        try {
            //校验请求参数/签名
            openApiService.checkAPISign(openApiRequestEntity);
            List<String> orderNoList = JSONObject.parseArray(openApiRequestEntity.getParam(), String.class);
            if (orderNoList.size() > 1) {
                return R.fail("非法的请求! 订单编号数量单次查询不能超过1个");
            }
      SysTenantVo sysTenantVo =
          sysTenantService.queryByTenantId(openApiRequestEntity.getTenantId());
      if (ObjectUtil.isEmpty(sysTenantVo)) {
        return R.fail("非法的请求！租户不存在");
      }
      OpenApiOrderSearchDTO apiOrderSearchDTO = new OpenApiOrderSearchDTO();
      // 供应商
      if (TenantType.Supplier.name().equals(sysTenantVo.getTenantType())) {
        apiOrderSearchDTO.setSupplierTenantId(openApiRequestEntity.getTenantId());
      }
      // 分销商
      if (TenantType.Distributor.name().equals(sysTenantVo.getTenantType())) {
        apiOrderSearchDTO.setDistributorTenantId(openApiRequestEntity.getTenantId());
      }
      apiOrderSearchDTO.setOrderExtendIdList(orderNoList);
      List<Orders> orders =
          iOrdersService.getBaseMapper().selectOrderDetailWithOpenApi(apiOrderSearchDTO);
            if (CollUtil.isEmpty(orders)){
        apiOrderSearchDTO.setOrderExtendIdList(null);
        apiOrderSearchDTO.setChannelOrderNoList(orderNoList);
        orders = iOrdersService.getBaseMapper().selectOrderDetailWithOpenApi(apiOrderSearchDTO);
                if (CollUtil.isEmpty(orders)){
                    return R.ok(List.of());
                }
            }
      List<OrderPageVo> ordersByOrderNosAndTenantId = new ArrayList<>();
      orders.forEach(
          order -> {
            List<OrderPageVo> ordersByOrderNosAndTenantId1 =
                ordersService.getOrdersByOrderNosAndTenantId(
                    Set.of(order.getOrderNo()), order.getTenantId());
            ordersByOrderNosAndTenantId.addAll(ordersByOrderNosAndTenantId1);
          });
      List<OrderPageVoOpenAPI> orderPageVoOpenApis = BeanUtil.copyToList(ordersByOrderNosAndTenantId, OrderPageVoOpenAPI.class);
      // 处理每个订单数据
      orderPageVoOpenApis.forEach(
          s -> {
                //查询物流轨迹信息
            List<OrderItemTrackingRecord> listByOrderNo =
                TenantHelper.ignore(
                    () -> iOrderItemTrackingRecordService.getListByOrderNo(s.getOrderId()));
            List<OrderPageVoOpenAPI.OrderOpenAPITrackingRecord> orderOpenApiTrackingRecords =
                OrderConvert.INSTANCE.convertTrackingRecordToOpenAPITrackingRecord(listByOrderNo);
            s.setOrderItemTrackingRecords(orderOpenApiTrackingRecords);
                //查询附件信息
                LambdaQueryWrapper<OrderAttachment> oo = new LambdaQueryWrapper<>();
                oo.eq(OrderAttachment::getOrderNo,s.getOrderId());
            List<OrderAttachment> orderAttachments =
                TenantHelper.ignore(() -> iOrderAttachmentService.getBaseMapper().selectList(oo));
                List<OrderDetailsAttachmentVo> orderDetailsAttachmentVos = BeanUtil.copyToList(orderAttachments, OrderDetailsAttachmentVo.class);
                s.setOrderDetailsAttachmentVos(orderDetailsAttachmentVos);

            });
      return R.ok(orderPageVoOpenApis);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

  /**
   * 分页批量获取订单
   */
  @GetMapping("/order/getOrderList")
  public R getOrderList(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
    openApiRequestEntity.setUrl(request.getServletPath());
    try {
      // 校验请求参数/签名
      openApiService.checkAPISign(openApiRequestEntity);
      OpenApiOrderSearchDTO apiOrderSearchDTO =
          JSONObject.parseObject(openApiRequestEntity.getParam(), OpenApiOrderSearchDTO.class);
      if (apiOrderSearchDTO.getCurrent() == 0) {
        return R.fail("非法的请求! 页码不能为0");
      }
      if (apiOrderSearchDTO.getSize() > 50) {
        return R.fail("非法的请求! 每页条数不能超过50");
      }
      if (ObjectUtil.isNull(apiOrderSearchDTO.getCurrent())
          || ObjectUtil.isNull(apiOrderSearchDTO.getSize())) {
        return R.fail("非法的请求! 请检查分页参数是否正确");
      }
      if (ObjectUtil.isNull(apiOrderSearchDTO.getCreateTimeStart())||
          ObjectUtil.isNull(apiOrderSearchDTO.getCreateTimeEnd())){
          return R.fail(" 非法的请求! 请检查时间参数是否正确");
      }

      DateTime date1PlusOneMonth = DateUtil.offsetMonth(apiOrderSearchDTO.getCreateTimeStart(), 1);
      boolean isTrue=  apiOrderSearchDTO.getCreateTimeEnd().compareTo(date1PlusOneMonth) > 0;
      if (isTrue){
          return R.fail("非法的请求! 开始时间和结束时间跨度不能超过一个月");
      }

      int offset = (apiOrderSearchDTO.getCurrent() - 1) * apiOrderSearchDTO.getSize();
      apiOrderSearchDTO.setOffset(offset);
      SysTenantVo sysTenantVo =
          sysTenantService.queryByTenantId(openApiRequestEntity.getTenantId());
      if (ObjectUtil.isEmpty(sysTenantVo)) {
        return R.fail("非法的请求！租户不存在");
      }
      // 供应商
      if (TenantType.Supplier.name().equals(sysTenantVo.getTenantType())) {
        apiOrderSearchDTO.setSupplierTenantId(openApiRequestEntity.getTenantId());
      }
      // 分销商
      if (TenantType.Distributor.name().equals(sysTenantVo.getTenantType())) {
        apiOrderSearchDTO.setDistributorTenantId(openApiRequestEntity.getTenantId());
      }
      int count = iOrdersService.getBaseMapper().selectOrderCountWithOpenApi(apiOrderSearchDTO);
      if (count == 0) {
        return R.ok(TableDataInfo.build(null, count));
      }
      List<String> orderExtendIds = iOrdersService.getBaseMapper().selectPageOrderWithOpenApi(apiOrderSearchDTO);
      LambdaQueryWrapper<Orders> q = new LambdaQueryWrapper<>();
      q.in(Orders::getOrderExtendId,orderExtendIds);
      List<Orders> orders = TenantHelper.ignore(()->iOrdersService.getBaseMapper().selectList(q));
        List<OpenApiOrderListResponse> openApiOrderListResponses = BeanUtil.copyToList(orders, OpenApiOrderListResponse.class);
        List<Map<String, Object>> list = openApiOrderListResponses.stream()
            .collect(Collectors.groupingBy(OpenApiOrderListResponse::getOrderExtendId))
            .entrySet().stream()
            .map(entry -> {
                Map<String, Object> map = new HashMap<>();
                map.put("orderExtendId", entry.getKey());
                map.put("orderList", entry.getValue());
                return map;
            })
            .collect(Collectors.toList());

      return R.ok(TableDataInfo.build(list, count));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 功能描述：创建发货任务
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/11
     */
    @PostMapping("/order/createOrderFlow")
    public R createOrderFlow(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) throws InterruptedException {

//        String tenantId = dto.getTenantId();
        openApiRequestEntity.setUrl(request.getServletPath());
        //校验请求参数/签名
        openApiService.checkAPISign(openApiRequestEntity);
        String param = openApiRequestEntity.getParam();
        OpenApiOrderReceiveDTO thirdDTO = new OpenApiOrderReceiveDTO();
        OrderReceiveFromThirdDTO dto = new OrderReceiveFromThirdDTO();
        R r;
        try {
            thirdDTO = JSON.parseObject(param, OpenApiOrderReceiveDTO.class);
            BeanUtil.copyProperties(thirdDTO, dto);
            if(ObjectUtil.isEmpty(dto)){
                return R.fail("非法的请求! 请检验参数格式是否正确");
            }

            r = thirdOrdersApiService.tripartiteBatchEnterForOpenApi(dto, openApiRequestEntity.getTenantId());

        } catch (Exception e) {
            log.info("open-api创建发货任务返回参数:{}",JSON.toJSONString(R.fail(e.getMessage())));
            return R.fail(e.getMessage());
        }
        log.info("open-api创建发货任务返回:{}",JSON.toJSONString(r));
        return r;

    }

    @PostMapping("/order/receiveOrderAttachment")
    public R receiveOrderAttachment(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {

        openApiRequestEntity.setUrl(request.getServletPath());
        //校验请求参数/签名
        openApiService.checkAPISign(openApiRequestEntity);
        String param = openApiRequestEntity.getParam();
        List<OpenApiOrderAttachmentDTO> thirdDTO = new ArrayList<>();
        List<OrderReceiveAttachmentDTO> dtos = new ArrayList<>();
        R r;
        try {
            thirdDTO = JSON.parseArray(param, OpenApiOrderAttachmentDTO.class);
            dtos = BeanUtil.copyToList(thirdDTO, OrderReceiveAttachmentDTO.class);
            if(ObjectUtil.isEmpty(dtos)){
                return R.fail("非法的请求! 请检验参数格式是否正确");
            }
            r = thirdOrdersApiService.receiveOrderAttachment(dtos, openApiRequestEntity.getTenantId());

        } catch (Exception e) {
            log.info("open-api创建发货任务返回参数:{}",JSON.toJSONString(R.fail(e.getMessage())));
            return R.fail(e.getMessage());
        }
        log.info("open-api创建发货任务返回:{}",JSON.toJSONString(r));
        return r;

    }

    /**
     * 功能描述：接收所有订单附件
     *
     * @param openApiRequestEntity 开放api请求实体
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/07/02
     */
    @PostMapping("/order/receiveAllOrderAttachment")
    public R receiveAllOrderAttachment(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {

        openApiRequestEntity.setUrl(request.getServletPath());
        //校验请求参数/签名
        openApiService.checkAPISign(openApiRequestEntity);
        String param = openApiRequestEntity.getParam();
        List<OpenApiOrderAttachmentDTO> thirdDTO = new ArrayList<>();
        List<OrderReceiveAttachmentDTO> dtos = new ArrayList<>();
        R r;
        try {
            thirdDTO = JSON.parseArray(param, OpenApiOrderAttachmentDTO.class);
            dtos = BeanUtil.copyToList(thirdDTO, OrderReceiveAttachmentDTO.class);
            if(ObjectUtil.isEmpty(dtos)){
                return R.fail("非法的请求! 请检验参数格式是否正确");
            }
            r = thirdOrdersApiService.receiveAllOrderAttachment(dtos, openApiRequestEntity.getTenantId());

        } catch (Exception e) {
            log.info("open-api推送附件任务返回参数:{}",JSON.toJSONString(R.fail(e.getMessage())));
            return R.fail(e.getMessage());
        }
        log.info("open-api推送附件任务返回:{}",JSON.toJSONString(r));
        return r;

    }

    @PostMapping("/getSign/MnkIBVIk2hwjeyut63DP3twwNmiNfPDl/{apiSecretkey}")
    public R getSign(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity,String apiSecretkey) throws NoSuchAlgorithmException {
        return   R.ok(openApiService.getSign(openApiRequestEntity,apiSecretkey));
    }
    @PostMapping("/order/getTrackingRecord")
    public R getTrackingRecord(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        R r;
        try {
            //校验请求参数/签名
            openApiService.checkAPISign(openApiRequestEntity);
            //查询订单
            String param = openApiRequestEntity.getParam();
            List<TrackingMsgDTO> thirdDTO = new ArrayList<>();

            try {
                thirdDTO = JSON.parseArray(param, TrackingMsgDTO.class);
                if(ObjectUtil.isEmpty(thirdDTO)){
                    return R.fail("非法的请求! 请检验参数格式是否正确");
                }
                List<String> channelNos = thirdDTO.stream().map(TrackingMsgDTO::getChannelOrderNo)
                                                  .collect(Collectors.toList());
                r = thirdOrdersApiService.getTrackingRecord(channelNos, openApiRequestEntity.getTenantId());

            } catch (Exception e) {
                log.error("open-api创建发货任务返回参数",e);
                return R.fail(e.getMessage());
            }
            return r;
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    /**
     * 功能描述：取消订单流
     *
     * @param openApiRequestEntity 开放api请求实体
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/14
     */
    @PostMapping("/order/cancelOrderFlow")
    public R cancelOrderFlow(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) throws InterruptedException {

        openApiRequestEntity.setUrl(request.getServletPath());
        //校验请求参数/签名
        openApiService.checkAPISign(openApiRequestEntity);
        String param = openApiRequestEntity.getParam();
        List<OpenApiCancelOrderDTO> thirdDTO = new ArrayList<>();
        R r;
        try {
            thirdDTO = JSON.parseArray(param, OpenApiCancelOrderDTO.class);
            List<String> orderNos = thirdDTO.stream().map(OpenApiCancelOrderDTO::getOrderNo).collect(Collectors.toList());
            if(ObjectUtil.isEmpty(thirdDTO)){
                return R.fail("非法的请求! 请检验参数格式是否正确");
            }
            r = thirdOrdersApiService.cancelOrderFlow(orderNos, openApiRequestEntity.getTenantId());

        } catch (Exception e) {
            log.info("open-api取消订单返回参数:{}",JSON.toJSONString(R.fail(e.getMessage())));
            return R.fail(e.getMessage());
        }
        log.info("open-api取消订单返回:{}",JSON.toJSONString(r));
        return r;
    }

  /** 供应商订单发货*/
  @Transactional
  @PostMapping("/order/orderDelivery")
  public R orderDelivery(@Validated @RequestBody OpenApiRequestEntity openApiRequestEntity) {
      openApiRequestEntity.setUrl(request.getServletPath());
      try {
          // 校验请求参数/签名
          openApiService.checkAPISign(openApiRequestEntity);
          SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(openApiRequestEntity.getTenantId());
          if (ObjectUtil.notEqual(sysTenantVo.getTenantType(), TenantType.Supplier.name())) {
              return R.fail("非法的请求! 订单发货操作只能由供应商操作");
          }

          OpenApiOrdersDeliveryDTO ordersDeliveryDTO = JSONObject.parseObject(openApiRequestEntity.getParam(), OpenApiOrdersDeliveryDTO.class);
          if (StrUtil.isEmpty(ordersDeliveryDTO.getOrderNo())){
              return R.fail("非法的请求! 订单号不能为空");
          }
          if (StrUtil.isEmpty(ordersDeliveryDTO.getShippingState())){
              return R.fail("非法的请求! 订单状态不能为空");
          }
          ShippingStateEnum shippingState = ShippingStateEnum.valueOfByBizArk(ordersDeliveryDTO.getShippingState());
          if (ObjectUtil.notEqual(shippingState,ShippingStateEnum.Picked)&&
              ObjectUtil.notEqual(shippingState,ShippingStateEnum.Shipped)){
              return   R.fail("非法的请求! 订单状态只能为picked或shipped");
}

          if (CollUtil.isEmpty(ordersDeliveryDTO.getOrderShipments())){
              return R.fail("非法的请求! 发货信息不能orderShipments为空");
          }
          for (OpenApiOrdersDeliveryDTO.OpenApiOrderOutShipment s : ordersDeliveryDTO.getOrderShipments()) {
              if (StrUtil.isEmpty(s.getTrackingNo())){
                  return R.fail(  "非法的请求! trackingNo不能为空");
              }
              if (StrUtil.isEmpty(s.getCarrier())){
                  return R.fail(  "非法的请求! carrier不能为空");
              }
              if (StrUtil.isEmpty(s.getProductSkuCode())){
                  return  R.fail(  "非法的请求! productSkuCode不能为空");
              }
          }
          // 查询订单号
          List<Orders> orderNoByOrderExtendId = ordersMapper.getOrderNoByOrderExtendId(ordersDeliveryDTO.getOrderNo(), openApiRequestEntity.getTenantId());
          if (CollUtil.isEmpty(orderNoByOrderExtendId)) {
              return R.fail("非法的请求，订单号:{}未匹配到订单", ordersDeliveryDTO.getOrderNo());
          }
          List<OrderItemShippingRecord> shippingRecordList =TenantHelper.ignore(()->iOrderItemShippingRecordService.queryByOrderExtendId(ordersDeliveryDTO.getOrderNo())) ;
          if (CollUtil.isEmpty(shippingRecordList)){
              return R.fail("非法的请求，订单号:{}订单出货单信息不存在",ordersDeliveryDTO.getOrderNo());
          }
          ordersService.openApiOrderDelivery(shippingRecordList, ordersDeliveryDTO);
          return R.ok("发货成功");
      } catch (Exception e) {
          return R.fail("[发货失败]"+e.getMessage());
      }
  }
    @PostMapping("/order/getCancelOrderStatus")
    public R getCancelOrderStatus(@RequestBody OpenApiRequestEntity openApiRequestEntity) {

        openApiRequestEntity.setUrl(request.getServletPath());
        //校验请求参数/签名
        openApiService.checkAPISign(openApiRequestEntity);
        String param = openApiRequestEntity.getParam();
        OpenApiOrderCancelDTO thirdDTO = new OpenApiOrderCancelDTO();

        R r;
        try {
            thirdDTO = JSON.parseObject(param, OpenApiOrderCancelDTO.class);

            if(ObjectUtil.isEmpty(thirdDTO)){
                return R.fail("非法的请求! 请检验参数格式是否正确");
            }
            r = thirdOrdersApiService.getCancelOrderStatus(thirdDTO, openApiRequestEntity.getTenantId());

        } catch (Exception e) {
            log.info("open-api订单取消结果返回参数:{}",JSON.toJSONString(R.fail(e.getMessage())));
            return R.fail(e.getMessage());
        }
        log.info("open-api订单取消结果返回:{}",JSON.toJSONString(r));
        return r;

    }

    /**
     * 功能描述：供应商订单审核流程
     *
     * @param openApiRequestEntity 开放api请求实体
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/06/27
     */
    @PostMapping("/order/orderReviewFlow")
    public R orderReviewFlowForSupplier(@RequestBody OpenApiRequestEntity openApiRequestEntity) {
        openApiRequestEntity.setUrl(request.getServletPath());
        //校验请求参数/签名
        openApiService.checkAPISign(openApiRequestEntity);
        String param = openApiRequestEntity.getParam();
        OpenApiOrderReviewDTO openApiOrderReviewDTO = new OpenApiOrderReviewDTO();
        R r = null;
        try {
            //校验请求参数/签名

            try {
                openApiOrderReviewDTO = JSON.parseObject(param, OpenApiOrderReviewDTO.class);
                r = thirdOrdersApiService.orderReviewFlowForSupplier(openApiOrderReviewDTO,openApiOrderReviewDTO.getTenantId());

            } catch (Exception e) {
                log.error("open-api供应商订单审核流程",e);
                if(ObjectUtil.isNotEmpty(openApiOrderReviewDTO)&&ObjectUtil.isNotEmpty(openApiOrderReviewDTO.getOrderReviewOpinion())){
                    if(openApiOrderReviewDTO.getOrderReviewOpinion()==1){
                        return R.fail(ZSMallStatusCodeEnum.OPEN_ORDER_REVIEW_REFUSE_FAILED,e.getMessage());
                    }
                    if(openApiOrderReviewDTO.getOrderReviewOpinion()==0){
                        return R.fail(ZSMallStatusCodeEnum.OPEN_ORDER_REVIEW_CONSENT_FAILED,e.getMessage());
                    }
                }else {
                    return R.fail(e.getMessage());
                }

            }

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
        return r;
    }
    @PostMapping("/attachment/getAttachmentUrlByBase64")
    public R getAttachmentUrlByBase64(@RequestBody OpenApiRequestEntity openApiRequestEntity) {

        openApiRequestEntity.setUrl(request.getServletPath());
        //校验请求参数/签名
        openApiService.checkAPISign(openApiRequestEntity);
        String param = openApiRequestEntity.getParam();
        OpenApiAttachmentConvertDTO thirdDTO = new OpenApiAttachmentConvertDTO();

        R r;
        try {
            thirdDTO = JSON.parseObject(param, OpenApiAttachmentConvertDTO.class);

            if(ObjectUtil.isEmpty(thirdDTO)){
                return R.fail("非法的请求! 请检验参数格式是否正确");
            }
            r = thirdOrdersApiService.getAttachmentUrlByBase64(thirdDTO, openApiRequestEntity.getTenantId());

        } catch (Exception e) {
            log.info("open-api订单取消结果返回参数:{}",JSON.toJSONString(R.fail(e.getMessage())));
            return R.fail(e.getMessage());
        }
        log.info("open-api订单取消结果返回:{}",JSON.toJSONString(r));
        return r;

    }
}


