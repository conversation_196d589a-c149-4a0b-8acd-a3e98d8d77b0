package com.zsmall.order.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.hengjian.common.core.domain.R;
import com.zsmall.common.domain.dto.*;
import com.zsmall.common.enums.orderRefund.RefundApplyType;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.order.biz.service.OrderRefundService;
import com.zsmall.order.biz.service.ThirdOrdersApiService;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.refund.RefundApplyHandleBo;
import com.zsmall.order.entity.domain.vo.order.OrderPageVo;
import com.zsmall.order.entity.iservice.IOrderRefundService;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.warehouse.entity.domain.vo.warehouse.ThirdWarehouseVo;
import io.swagger.annotations.ApiImplicitParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * open-api
 *
 * <AUTHOR> Theo
 * @create 2024/5/11 11:48
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/open/api/test/order")
public class OpenApiTestController {

    private final ThirdOrdersApiService thirdOrdersApiService;
    private final OrderRefundService orderRefundService;
    private final IOrdersService iOrdersService;
    private final IOrderRefundService iOrderRefundService;
    /**
     * 功能描述：创建发货任务
     *
     * @param dto 到
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/11
     */
    @PostMapping("/createOrderFlow")
    public R createOrderFlow(@RequestBody OpenApiOrderReceiveDTO dto,String tenantId) throws InterruptedException {
        OrderReceiveFromThirdDTO thirdDTO = new OrderReceiveFromThirdDTO();
        BeanUtil.copyProperties(dto, thirdDTO);

        // 临时先for循环用一下,先接接口,后续更改为线程池
        // 线程池批处理 录入失败的返回录入订单编号 测试
        log.info("三方订单,批量录入,入参:{}", JSONObject.toJSONString(dto));
        // 后续query里面拿
//        String tenantId = dto.getTenantId();
        R r = thirdOrdersApiService.tripartiteBatchEnterForOpenApi(thirdDTO,tenantId);

        return r;

    }
    @PostMapping("/refundApplyHandle")
    public R refundApplyHandle(String orderExtendId) throws Exception {
        RefundApplyHandleBo bo = new RefundApplyHandleBo();
        bo.setIsAuto(Boolean.TRUE);
        List<Orders> orders = iOrdersService.getByOrderExtendId(orderExtendId);
        List<String> orderNos = orders.stream().map(Orders::getOrderNo).collect(Collectors.toList());

        List<String> refundNos = iOrderRefundService.queryByOrderNo(orderNos);
        bo.setOrderRefundNoList(refundNos);
        bo.setPass(Boolean.TRUE);
        bo.setRefundApplyType(RefundApplyType.RefundReturn.getValue());
        try {
            orderRefundService.refundApplyHandle(bo);
            iOrdersService.cancelSuccess(orderExtendId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return R.ok();

    }

    /**
     * 功能描述：接收所有订单附件
     *
     * @param
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/07/08
     */
    @PostMapping("/order/receiveAllOrderAttachment")
    public R receiveAllOrderAttachment(@RequestBody List<OrderReceiveAttachmentDTO> dtos,String tenantId) {

//        openApiRequestEntity.setUrl(request.getServletPath());
        //校验请求参数/签名
//        openApiService.checkAPISign(openApiRequestEntity);

        R r;
        try {
            if(ObjectUtil.isEmpty(dtos)){
                return R.fail("非法的请求! 请检验参数格式是否正确");
            }
            r = thirdOrdersApiService.receiveAllOrderAttachment(dtos, tenantId);

        } catch (Exception e) {
            log.info("open-api推送附件返回参数:{}",JSON.toJSONString(R.fail(e.getMessage())));
            return R.fail(e.getMessage());
        }
        log.info("open-api推送附件返回:{}",JSON.toJSONString(r));
        return r;

    }

    /**
     * 功能描述：取消订单流
     *
     * @param nos      订单号
     * @param tenantId 租户id
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/14
     */
    @PostMapping("/cancelOrderFlow")
    public R cancelOrderFlow(@RequestBody List<String> nos,String tenantId) throws InterruptedException {

        //校验请求参数/签名
//        openApiService.checkAPISign(openApiRequestEntity);
        R r;
        try {
            r = thirdOrdersApiService.cancelOrderFlow(nos, tenantId);

        } catch (Exception e) {
            log.error("取消异常",e);
            log.info("open-api创建发货任务返回参数:{}", JSON.toJSONString(R.fail(e.getMessage())));
            return R.fail(e.getMessage());
        }
        log.info("open-api创建发货任务返回:{}",JSON.toJSONString(r));
        return r;

    }
    /**
     * 功能描述：接收订单附件
     *
     * @param dtos     dto
     * @param tenantId 租户id
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/01/13
     */
    @PostMapping("/receiveOrderAttachment")
    public R receiveOrderAttachment(@RequestBody List<OrderReceiveAttachmentDTO> dtos,String tenantId) {


        //校验请求参数/签名
//        openApiService.checkAPISign(openApiRequestEntity);
//        String param = openApiRequestEntity.getParam();
        List<OpenApiOrderAttachmentDTO> thirdDTO = new ArrayList<>();
//        List<OrderReceiveAttachmentDTO> dtos = new ArrayList<>();
        R r;
        try {
//            thirdDTO = JSON.parseArray(param, OpenApiOrderAttachmentDTO.class);
//            dtos = BeanUtil.copyToList(thirdDTO, OrderReceiveAttachmentDTO.class);
//            if(ObjectUtil.isEmpty(dtos)){
//                return R.fail("非法的请求! 请检验参数格式是否正确");
//            }
            r = thirdOrdersApiService.receiveOrderAttachment(dtos, tenantId);

        } catch (Exception e) {
            log.info("open-api创建发货任务返回参数:{}",JSON.toJSONString(R.fail(e.getMessage())));
            return R.fail(e.getMessage());
        }
        log.info("open-api创建发货任务返回:{}",JSON.toJSONString(r));
        return r;

    }


    /**
     * 功能描述：获取订单的详细信息
     *
     * @param orderNo 订单号
     * @return {@link R }<{@link OrderPageVo }>
     * <AUTHOR>
     * @date 2024/05/11
     */
    @GetMapping("/getOrderDetail")
    @ApiImplicitParam(name = "orderNo", value = "订单号", required = true, dataType = "String")
    public R<OrderPageVo> getOrderDetail(String orderNo) throws InterruptedException {
        return R.ok();
    }

    /**
     * 功能描述：获取库存信息
     *
     * @param orderNos 订单号
     * @return {@link R }<{@link List }<{@link ThirdWarehouseVo }>>
     * <AUTHOR>
     * @date 2024/05/11
     */
    @GetMapping("/getInventoryInformation")
    @ApiImplicitParam(name = "skus", value = "sku no,max size 10", required = true, dataType = "array")
    public R<List<ThirdWarehouseVo>> getInventoryInformation(List<String> orderNos) throws InterruptedException {
        return R.ok();
    }

    @GetMapping("/jsonSerialization")
    public R<String> jsonSerialization(@RequestBody String json) throws InterruptedException {
        Gson gson = new Gson();
        String json1 = gson.toJson(json);
        log.info("jsonSerialization:{}",json1);
        return R.ok(json1);
    }
    @PostMapping("/getTrackingRecord")
    public R getTrackingRecord(@RequestBody List<TrackingMsgDTO> openApiRequestEntity) {

        R r;
        try {
            //校验请求参数/签名
//            openApiService.checkAPISign(openApiRequestEntity);
            //查询订单

            List<TrackingMsgDTO> thirdDTO = new ArrayList<>();

            try {
                if(ObjectUtil.isEmpty(thirdDTO)){
                    return R.fail("非法的请求! 请检验参数格式是否正确");
                }
                List<String> channelNos = thirdDTO.stream().map(TrackingMsgDTO::getChannelOrderNo)
                                                  .collect(Collectors.toList());
                r = thirdOrdersApiService.getTrackingRecord(channelNos, openApiRequestEntity.get(0).getTenantId());

            } catch (Exception e) {
                log.error("open-api创建发货任务返回参数",e);
                return R.fail(e.getMessage());
            }
            return r;
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 功能描述：供应商订单审核流程
     *
     * @param openApiOrderReviewDTO 开放api订单审核dto
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/06/13
     */
    @PostMapping("/orderReviewFlow")
    public R orderReviewFlowForSupplier(@RequestBody OpenApiOrderReviewDTO openApiOrderReviewDTO) {

        R r;
        try {
            //校验请求参数/签名

            try {
                String tenantId =null;
                r = thirdOrdersApiService.orderReviewFlowForSupplier(openApiOrderReviewDTO,openApiOrderReviewDTO.getTenantId());

            } catch (Exception e) {
                log.error("open-api供应商订单审核流程",e);
                return R.fail(ZSMallStatusCodeEnum.OPEN_ORDER_REVIEW_REFUSE_FAILED,e.getMessage());
            }

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
        return r;
    }

}
