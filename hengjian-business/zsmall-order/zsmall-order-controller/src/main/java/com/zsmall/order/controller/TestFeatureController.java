package com.zsmall.order.controller;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.utils.JacksonUtils;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.vo.SysOssVo;
import com.hengjian.system.mapper.SysOssMapper;
import com.zsmall.common.domain.resp.tiktok.fulfillment.SplittableGroupBody;
import com.zsmall.common.domain.vo.XxlJobSearchVO;
import com.zsmall.common.enums.SalesChannelInterfaceStatusEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.extend.wms.kit.ThebizarkDelegate;
import com.zsmall.extend.wms.kit.ThebizarkKit;
import com.zsmall.extend.wms.model.ThebizarkBean;
import com.zsmall.order.biz.test.service.WarehouseServiceV2;
import com.zsmall.order.biz.test.test.ITestFeatureService;
import com.zsmall.order.entity.domain.OrderItemShippingRecord;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.order.OrderPayBo;
import com.zsmall.order.entity.iservice.IOrderItemShippingRecordService;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.mapper.TenantSalesChannelMapper;
import com.zsmall.system.entity.util.AuzTiktokUtil;
import com.zsmall.warehouse.entity.domain.WarehouseBizArkConfig;
import com.zsmall.warehouse.entity.domain.event.ThirdWarehouseEvent;
import com.zsmall.warehouse.entity.iservice.IWarehouseBizArkConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * lty notes 功能测试
 *
 * <AUTHOR> Theo
 * @create 2024/3/6 15:19
 */
@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/test/feature")
public class TestFeatureController {

    @Resource
    private ITestFeatureService iTestFeatureService;
    private final IWarehouseBizArkConfigService iWarehouseBizArkConfigService;
    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final TenantSalesChannelMapper baseMapper;
    private final WarehouseServiceV2 warehouseServiceV2;
    private final IOrderItemShippingRecordService iOrderItemShippingRecordService;
    private final IOrdersService ordersService;
    @Resource
    private AuzTiktokUtil auzTiktokUtil;
    @Value("${distribution.tenant.sales.channel.tiktok.appkey}")
    private String appkey;

    @Value("${distribution.tenant.sales.channel.tiktok.appSecret}")
    private String appSecret;
    @Resource
    private SysOssMapper sysOssMapper;
    @PostMapping("/testLimit")
    public void testLimit(@RequestBody OrderPayBo vo) {
        iTestFeatureService.testLimitIsAble(vo);
    }
    @PostMapping("/testMapper")
    public void testMapper(String orderNo) {
        SysOssVo sysOssVo = sysOssMapper.selectByLineOrderItemId(orderNo);
        log.info("sysOssVo:{}",JSONObject.toJSONString(sysOssVo));

    }
    @PostMapping("/testAuthorization")
    public void testAuthorization(@RequestBody String body,String channelFlag) {

        TenantSalesChannel tenantSalesChannel =iTenantSalesChannelService.getOne((new LambdaQueryWrapper<TenantSalesChannel>().eq(TenantSalesChannel::getThirdChannelFlag,channelFlag).last("limit 1")));

        String refreshToken = "";
        //拿到refreshToken的值
        Map<String, Object> responseMap = JacksonUtils.jsonToMap(body);
        Map<String, Object> dataMap = (Map<String, Object>) responseMap.get("data");
        if (dataMap.containsKey("refresh_token")) {
            refreshToken = (String) dataMap.get("refresh_token");
        }

        String accessToken = "";
        if (dataMap.containsKey("access_token")) {
            accessToken = (String) dataMap.get("access_token");
        }

        String openId = "";
        if (dataMap.containsKey("open_id")) {
            openId = (String) dataMap.get("open_id");
        }

        //找到渠道对应的参数，并更新
        if (ObjectUtil.isNotEmpty(tenantSalesChannel)) {
            String connectStr = tenantSalesChannel.getConnectStr();
            Map<String, Object> connectMap =null;
            if(StrUtil.isEmpty(connectStr)){
                connectMap = new HashMap<>() {};

            }else{
                connectMap = JacksonUtils.jsonToMap(connectStr);
            }

            connectMap.put("refreshToken", refreshToken);
            connectMap.put("accessToken", accessToken);
            connectMap.put("openId", openId);
            String newConnectStr = JacksonUtils.toJson(connectMap);
            tenantSalesChannel.setConnectStr(newConnectStr);


            if (!SalesChannelInterfaceStatusEnum.RUNING.getValue().equals(tenantSalesChannel.getStatus())
                &&
                !SalesChannelInterfaceStatusEnum.INACTIVE.getValue().equals(tenantSalesChannel.getStatus())) {
                tenantSalesChannel.setStatus(SalesChannelInterfaceStatusEnum.RUNING.getValue());
                tenantSalesChannel.setAuthorizationAt(LocalDate.now());
            }

            baseMapper.insertOrUpdate(tenantSalesChannel);
            if(!connectMap.containsKey("shopId")||(connectMap.containsKey("shopId")&&connectMap.get("shopId")==null)){
                auzTiktokUtil.getTikTokShopInfo(appkey,appSecret,accessToken,tenantSalesChannel);
            }
        } else {
            log.info("请求tiktok的渠道flag:{} 有问题,没有此账号", channelFlag);
        }


    }

    @GetMapping("/testStock")
    public void testStock(String productSkuCode) {
        StockManagerEnum stockManager = StockManagerEnum.BizArk;
        ThirdWarehouseEvent.queryStock(stockManager, productSkuCode);
    }
    private ThebizarkDelegate initDelegate(String supplierId) {
        WarehouseBizArkConfig bizArkConfig = iWarehouseBizArkConfigService.queryBySupplierId(supplierId);
        return ThebizarkKit.create(new ThebizarkBean(bizArkConfig.getBizArkApiUrl(), bizArkConfig.getBizArkSecretKey(), bizArkConfig.getBizArkChannelAccount()));
    }
    @GetMapping("/testStream")
    public void testStream() {
//        XxlJobSearchVO xxlBody = JSON.parseObject(jobParam, XxlJobSearchVO.class);
        //linux 查找 zsmall-xxl-job.jar 文件

        XxlJobSearchVO xxlJobSearchVO = new XxlJobSearchVO();
        // 复制属性
//        BeanUtils.copyProperties(xxlBody, xxlJobSearchVO);

//        xxlJobSearchVO.setPageSize("100");
        // 当前时间-15分钟 到 当前时间
        LocalDateTime currentDateTime = LocalDateTime.now();

        // 减去5天
        LocalDateTime dateTimeFiveDaysAgo = currentDateTime.minusMinutes(15);
        Instant nowDays = currentDateTime.atZone(ZoneId.systemDefault()).toInstant();

        // 转换为Instant对象（以UTC为基准）
        Instant instantFiveDaysAgo = dateTimeFiveDaysAgo.atZone(java.time.ZoneId.systemDefault()).toInstant();

        // 转换为秒时间戳
        long timestampInSeconds = Math.floorDiv(instantFiveDaysAgo.getEpochSecond(), 1);

        long nowTime = Math.floorDiv(nowDays.getEpochSecond(), 1);
        xxlJobSearchVO.setUpdateTimeGe(timestampInSeconds);
        xxlJobSearchVO.setUpdateTimeLt(nowTime);

        String jsonString = JSONObject.toJSONString(xxlJobSearchVO);
    }

    @PostMapping("/testPost")
    public void testPost(@RequestBody String vo) {

        XxlJobSearchVO xxlBody = JSON.parseObject(vo, XxlJobSearchVO.class);
        String jsonString = JSONObject.toJSONString(xxlBody);
        log.info(jsonString);
    }

    @PostMapping("/testRedisLua")
    public void testRedisLua() {
        // 帮我写一个lua脚本,脚本的内容是:如果key存在,就使key的value值加1并且刷新key的持续时间;如果key不存在,就创建一个key,并且设置key的值为1,并且key的过期时间为2分钟


        String lua = "local key = KEYS[1]\n" +
            "local result = redis.call('EXISTS', key)\n" +
            "if result == 1 then\n" +
            "    redis.call('INCR', key)\n "+
            "    return redis.call('EXPIRE', key, 360) \n" +
            "else\n" +
            "    redis.call('SET', key, 1)\n" +
            "    return redis.call('EXPIRE', key, 360)\n" +
            "end";
        RedissonClient client = RedisUtils.getClient();
        // 2. 执行lua脚本
        Long result = client.getScript().eval(RScript.Mode.READ_WRITE, lua, RScript.ReturnType.INTEGER, Collections.singletonList("toErp:"));
        log.info(result.toString());

        // 帮我写一个lua脚本,脚本的内容是:如果存在key,并且key的value大于1,就将该value减1,并且返回false;如果该key的value=1则返回true;
    }

    @GetMapping("/testWarehouse")
    public void testWarehouse() {

        log.info("仓库库存拉取任务开始");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        warehouseServiceV2.pullInventoryJob();

        stopWatch.stop();
        log.info("仓库库存拉取任务结束,用时信息:{}",stopWatch.prettyPrint());
    }

    @PostMapping("/testSpilt")
    public void testSpilt(@RequestBody SplittableGroupBody vo) {
        iTestFeatureService.testSpilt(vo);
    }
    @PostMapping("/pullOrder")
    public void pullOrder(@RequestBody XxlJobSearchVO vo) {
        String jsonString = JSONObject.toJSONString(vo);
        iTestFeatureService.pullOrder(ChannelTypeEnum.TikTok.name(), jsonString);
    }

    /**
     * 功能描述：订单数据清洗
     *
     * <AUTHOR>
     * @date 2024/07/17
     */
    @GetMapping("/testDataCleaning")
    public void testDataCleaning(String tenantId) {
//        iTestFeatureService.testDataCleaning(tenantId);
    }

    @GetMapping("/testsplit")
    public void testsplit() {
        test();
    }
    public void test(){
        Random random = new Random();
        List<Integer> list = new ArrayList<>();
//        for (int i = 0; i < 48; i++) {
//            list.add(random.nextInt(1000)); // 添加一个0到999之间的随机数
//        }
        Map<Integer, List<Integer>> groupedList = splitListIntoGroups(list,50);
        System.out.println(com.alibaba.fastjson.JSONObject.toJSON(groupedList));
    }

    public <T> Map<Integer, List<T>> splitListIntoGroups(List<T> list, int size) {
        Map<Integer, List<T>> result = new HashMap<>();
        for (int i = 0; i < list.size(); i += size) {
            int groupKey = i / size; // 使用整除来生成组键
            int end = Math.min(i + size, list.size()); // 确保不会超出列表长度
            List<T> subList = list.subList(i, end);
            result.put(groupKey, subList);
        }
        return result;
    }
    @GetMapping
    public void test(int i) {
//        模拟并发,看看orderService是否会有线程问题
        TenantHelper.ignore(()->iTestFeatureService.testIgonre(i));

    }
}
