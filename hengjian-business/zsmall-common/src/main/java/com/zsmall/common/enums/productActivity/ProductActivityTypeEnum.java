package com.zsmall.common.enums.productActivity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.zsmall.common.enums.BusinessCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品活动类型
 */
@Getter
@AllArgsConstructor
public enum ProductActivityTypeEnum implements IEnum<String> {

    /**
     * 锁货
     */
    StockLock("Stock-Lock", "锁货"),

    /**
     * 圈货
     */
    Buyout("Buyout", "圈货");

    private final String en_US;
    private final String zh_CN;

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }

    /**
     * 根据活动编号识别活动类型
     * @param activityCode
     * @return
     */
    public static ProductActivityTypeEnum identifyByActivityCode(String activityCode) {
        if (StrUtil.contains(activityCode, BusinessCodeEnum.ProductActivityStockLock.getValue())) {
            return ProductActivityTypeEnum.StockLock;
        } else if (StrUtil.contains(activityCode, BusinessCodeEnum.ProductActivityBuyout.getValue())) {
            return ProductActivityTypeEnum.Buyout;
        } else {
            return null;
        }
    }

    /**
     * 根据code获取名称
     * @param code
     * @return
     */
    public static String getNameByCode(String code) {
        for (ProductActivityTypeEnum value : values()) {
            if (value.name().equals(code)) {
                return value.zh_CN;
            }
        }
        return null;
    }
}
