package com.zsmall.common.enums.order;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024年6月18日  10:35
 * @description:
 */
@Getter
public enum OrderExceptionEnum {

    normal(0,"无异常"),
    product_mapping_exception(1,"商品映射异常"),
    order_pay_exception(2,"订单支付异常"),
    out_of_stock_exception(3,"库存异常"),
    Delivery_exception(4,"发货方式异常"),
    final_delivery_fee_exception(5,"最终运费异常"),
    measurement_anomaly(6,"测算异常"),
    stock_same_warehouse_code_exists(7,"仓库编号异常"),
    warehouse_mapping_exception(8,"仓库映射异常"),
    logistics_attachment_exception(9,"获取面单异常"),
    abnormal_exception(10,"创建发货单异常"),
    activity_exception(11,"活动异常")
    ;


    private Integer value;

    private String name;

    OrderExceptionEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static OrderExceptionEnum getByName(Integer value){
        for(OrderExceptionEnum orderExceptionEnum:OrderExceptionEnum.values()){
            if(orderExceptionEnum.getValue().equals(value)){
                return orderExceptionEnum;
            }
        }
        return null;
    }
}
