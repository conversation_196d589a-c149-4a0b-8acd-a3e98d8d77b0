package com.zsmall.common.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hengjian.common.log.enums.BusinessType;
import com.zsmall.common.domain.tiktok.domain.dto.base.TikTokRespBaseEntity;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.tiktok.TikTokApiEnums;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;


/**
 * lty notes 此抽象模版方法为三方录入入口,只处理三方数据的接入,不处理三方数据的转换
 *
 * <AUTHOR> Theo
 * @create 2024/1/30 10:29
 */
@Slf4j
public abstract class AbstractOrderHandler<T,E> {

    /**
     * 功能描述：是按渠道需要
     *
     * @param json json格式
     * @return boolean
     * <AUTHOR>
     * @date 2024/01/29
     */
    public abstract boolean isNeedByChannel(JSONObject json,TikTokApiEnums enums);

    public abstract boolean isNeedExpressSheetFlow(T targetData);


    /**
     * 功能描述：获取订单列表
     *
     * @param json json格式
     * <AUTHOR>
     * @date 2024/01/29
     */
    public abstract T getProductList(JSONObject json, String channel, String shopId, Class<T> targetClass, String tiktokAppKey,
                                     String tiktokAppSecret);

    /**
     * 功能描述：三方列表
     *
     * @param json        json
     * @param channel     通道
     * @param shopId      店铺id
     * @param targetClass 目标类
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/09/12
     */
    public abstract T getOrderList(JSONObject json, String channel, String shopId, Class<T> targetClass, String tiktokAppKey,
                                   String tiktokAppSecret);

    public abstract T getOrderListSpecify(JSONObject json, String channel, String shopId, Class<T> targetClass, String tiktokAppKey,
                                          String tiktokAppSecret);


    /**
     * 功能描述：插入商家数据
     *
     * @param
     * @param type            类型
     * @param channelTypeEnum 通道类型枚举
     * @param data
     * <AUTHOR>
     * @date 2024/01/29
     */
    public abstract void insertBusinessData(BusinessType type, ChannelTypeEnum channelTypeEnum , JSONObject json, Class<T> targetData,T data);

    /**
     * 功能描述：数据筛选
     *
     * @param type            类型
     * @param channelTypeEnum 通道类型枚举
     * @param json            json
     * @param targetData      目标数据
     * @param data            数据
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/02/28
     */
    public abstract T dataScreening(BusinessType type, ChannelTypeEnum channelTypeEnum , JSONObject json, Class<T> targetData,T data);

    /**
     * 功能描述：数据筛选 防腐层设计
     *
     * @param type            类型
     * @param channelTypeEnum 通道类型枚举
     * @param json            json
     * @param targetData      目标数据
     * @param data            数据
     * @param shopId
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/02/28
     */
    public abstract List<E> dataScreeningV2(BusinessType type, ChannelTypeEnum channelTypeEnum , JSONObject json, Class<T> targetData, T data,
                                            String shopId);

    /**
     * 功能描述：数据平台流程 for order
     * 优化方向:新流程去除了商品获取的逻辑,所以此处不进行复用
     *
     * @param type            类型
     * @param json            json格式
     * @param channelTypeEnum 通道类型枚举
     * @return {@link List }<{@link ? }>
     * <AUTHOR>
     * @date 2024/01/29
     */
    public T dataPlatformFlow(BusinessType type, JSONObject json, ChannelTypeEnum channelTypeEnum, TikTokApiEnums enums, String shopId,Class<T> targetClass, String tiktokAppKey,String tiktokAppSecret) {

        if(isNeedByChannel(json,enums)){
            log.info("当前线程:{},渠道:{}---订单数据:{},准备进入订单数据分析------->",Thread.currentThread(),channelTypeEnum.name(),json+"\n");
            // webhook数据分析 ,map内包含:逆向resp shopId webhookJson
            T targetData ;
            if(BusinessType.ORDER_V2.equals(type)){
                targetData = getOrderList(json, channelTypeEnum.name(), shopId, targetClass,tiktokAppKey,tiktokAppSecret);
                if(!isExists(targetData, type.name())){
                    return null;
                }
            }else{
                targetData = getProductList(json, channelTypeEnum.name(), shopId, targetClass,tiktokAppKey,tiktokAppSecret);
                if(isExists(targetData, type.name())){
                    return null;
                }
                insertBusinessData(type ,channelTypeEnum, json,targetClass,targetData);
                return (T)targetData;
            }

            log.info("当前线程:{},渠道:{}---订单数据:{},准备数据存储------->",Thread.currentThread(),channelTypeEnum.name(), JSON.toJSON(json));

            // 数据筛选过滤 防腐层
            targetData = dataScreening(type ,channelTypeEnum, json,targetClass,targetData);
            // data 为null则直接返回
            if(!dataValidation(targetData)){
                return targetData;
            }

            T finalTargetData = targetData;
            if(isNeedExpressSheetFlow(finalTargetData)){
                expressSheet(finalTargetData, shopId,tiktokAppKey,tiktokAppSecret);
            }
            insertBusinessData(type ,channelTypeEnum, json,targetClass,finalTargetData);
            // 进行面单的绑定
            expandBusiness(targetData, type.name());
            // 支付
            payOrder(targetData,type.name());
            return (T)targetData;
        }
        return null;
    }

    /**
     * 功能描述：指定数据平台流 数据捕捉流程
     *
     * @param type            类型
     * @param json            json
     * @param channelTypeEnum 通道类型枚举
     * @param enums           枚举
     * @param shopId          店铺id
     * @param targetClass     目标类
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/04/17
     */
    public T dataPlatformFlowForSpecify(BusinessType type, JSONObject json, ChannelTypeEnum channelTypeEnum, TikTokApiEnums enums, String shopId,Class<T> targetClass, String tiktokAppKey,String tiktokAppSecret) {

        if(isNeedByChannel(json,enums)){
            log.info("当前线程:{},渠道:{}---订单数据:{},准备进入订单数据分析------->",Thread.currentThread(),channelTypeEnum.name(),json+"\n");
            // webhook数据分析 ,map内包含:逆向resp shopId webhookJson
            T targetData ;
            if(BusinessType.ORDER_V2.equals(type)){
                targetData = getOrderListSpecify(json, channelTypeEnum.name(), shopId, targetClass,tiktokAppKey,tiktokAppSecret);
                if(!isExists(targetData, type.name())){
                    return null;
                }
            }else{
                targetData = getProductList(json, channelTypeEnum.name(), shopId, targetClass,tiktokAppKey,tiktokAppSecret);
                if(isExists(targetData, type.name())){
                    return null;
                }
            }

            log.info("当前线程:{},渠道:{}---订单数据:{},准备数据存储------->",Thread.currentThread(),channelTypeEnum.name(), JSON.toJSON(json));

            // 这部要判断商品是否存在/是否建立映射来路由到不同的方法
            // 1.没映射-直接生成订单 2.映射了-走老逻辑,拿商品映射 3.映射了部分-没映射的走新逻辑,直接生成订单 映射了的走老逻辑
            // 数据筛选过滤
            targetData = dataScreening(type ,channelTypeEnum, json,targetClass,targetData);
            // data 为null则直接返回
            if(!dataValidation(targetData)){
                return targetData;
            }

            T finalTargetData = targetData;
            if(isNeedExpressSheetFlow(finalTargetData)){
                expressSheet(finalTargetData, shopId,tiktokAppKey,tiktokAppSecret);
            }
            insertBusinessData(type ,channelTypeEnum, json,targetClass,finalTargetData);
//            // 进行面单的绑定
            expandBusiness(targetData, type.name());
//            // 支付
            payOrder(targetData,type.name());
            return (T)targetData;
        }
        return null;
    }


    /**
     * 功能描述：指定数据平台流 数据捕捉流程 多线程版本,移除了商品拉取的逻辑,后续商品的逻辑走商品导入处理
     *
     * @param type            类型
     * @param json            json  vo任务参数
     * @param channelTypeEnum 通道类型枚举
     * @param enums           枚举
     * @param shopId          店铺id
     * @param targetClass     目标类
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/04/17
     */
    public T dataPlatformFlowV2(BusinessType type, JSONObject json, ChannelTypeEnum channelTypeEnum, TikTokApiEnums enums, String shopId,Class<T> targetClass, String tiktokAppKey,String tiktokAppSecret) {

        if(isNeedByChannel(json,enums)){
            log.info("当前线程:{},渠道:{}---订单数据:{},准备进入订单数据分析------->",Thread.currentThread(),channelTypeEnum.name(),json+"\n");
            T targetData = null;
            if(BusinessType.ORDER_V2.equals(type)){
                targetData = getOrderList(json, channelTypeEnum.name(), shopId, targetClass,tiktokAppKey,tiktokAppSecret);
                if(!isExists(targetData, type.name())){
                    return null;
                }
            }

            log.info("当前线程:{},渠道:{}---订单数据:{},准备数据存储------->",Thread.currentThread(),channelTypeEnum.name(), JSON.toJSON(json));

            // 校验api走向,判断订单内的商品是否有映射
            commodityRouting(type, json, channelTypeEnum, shopId, targetClass, targetData,tiktokAppKey,tiktokAppSecret);

            return (T)targetData;
        }
        return null;
    }

    /**
     * 功能描述：由商品映射决定业务路由路线 ,还是在模版方法内做异步逻辑处理
     *  业务枚举
     *  key 为业务标识 value 为对应的数据
     * @param type       类型
     * @param targetData 目标数据
     * @param shopId     店铺id
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/07/01
     */
    public void commodityRouting(BusinessType type, JSONObject json, ChannelTypeEnum channelTypeEnum, String shopId,Class<T> targetClass,T targetData, String tiktokAppKey,
                                 String tiktokAppSecret){
        // 防腐层 拆单/清洗筛除 这里会对targetData的副本进行操作,所以不会影响到原数据
        List<E> list = dataScreeningV2(type, channelTypeEnum, json, targetClass, targetData,shopId);
        if (!dataValidation(list)) {
            log.error("缺少有效订单,订单流程跳出");
            return ;
        }
        // 测试阶段 不拆单,测试数据不拆单
        if (isNeedExpressSheetFlow(targetData)) {
            // 获取面单 如果因为异常导致面单没有获取到,后续通过补偿任务来进行补偿
            expressSheet(targetData, shopId,tiktokAppKey,tiktokAppSecret);
        }

        try {
            noProductMappingFlow(type, json, channelTypeEnum, targetClass, targetData, shopId, list,BusinessTypeMappingEnum.NO_MAPPING);
        } catch (Exception e) {
            log.error("noProductMappingFlow:", e);
        }

        try {
            haveProductMappingFlow(type, json, channelTypeEnum, targetClass, targetData, shopId, list,BusinessTypeMappingEnum.HAVE_MAPPING);
        } catch (Exception e) {
            log.error("haveProductMappingFlow:", e);
        }

    }
//    public void commodityRouting(BusinessType type, JSONObject json, ChannelTypeEnum channelTypeEnum, String shopId,Class<T> targetClass,T targetData){
//        // 拆单/清洗筛除 这里会对targetData的副本进行操作,所以不会影响到原数据
//        List<E> list = dataScreeningV2(type, channelTypeEnum, json, targetClass, targetData);
//        if (!dataValidation(list)) {
//            log.error("数据异常");
//            return ;
//        }
//        // 测试阶段 不拆单,测试数据不拆单
//        if (isNeedExpressSheetFlow(targetData)) {
//            // 获取面单 如果因为异常导致面单没有获取到,后续通过补偿任务来进行补偿
//            expressSheet(targetData, shopId);
//        }
//
//        CompletableFuture<Void> noMappingFuture = CompletableFuture.runAsync(() -> {
//            try {
//                noProductMappingFlow(type, json, channelTypeEnum, targetClass, targetData, shopId, list,BusinessTypeMappingEnum.NO_MAPPING);
//            } catch (Exception e) {
//                log.error("noProductMappingFlow:", e);
//            }
//        });
//
//        CompletableFuture<Void> haveMappingFuture = CompletableFuture.runAsync(() -> {
//            try {
//                haveProductMappingFlow(type, json, channelTypeEnum, targetClass, targetData, shopId, list,BusinessTypeMappingEnum.HAVE_MAPPING);
//            } catch (Exception e) {
//                log.error("haveProductMappingFlow:", e);
//            }
//        });
//
//        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(noMappingFuture, haveMappingFuture);
////        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(noMappingFuture);
//
//        try {
//            combinedFuture.get();
//        } catch (InterruptedException e) {
//            // 处理线程中断异常
//            Thread.currentThread().interrupt(); // 保留中断状态
//            log.error("CompletableFuture execution interrupted", e);
//        } catch (ExecutionException e) {
//            // 处理执行异常，获取并打印原始异常
//            Throwable cause = e.getCause();
//            if (cause instanceof CompletionException) { // 如果CompletableFuture的runAsync内部抛出了异常，会被包装成CompletionException
//                cause = cause.getCause(); // 再取一次原因，因为CompletionException也封装了原始异常
//            }
//            log.error("CompletableFuture execution failed", cause);
//        }
//
//    }
    /**
     * 功能描述：无产品映射业务流
     * 未来和haveProductMappingFlow合并,目前需要先实现业务,在此处先进行异步与多线程优化 ❌ 无需合并
     *
     * @param targetData 目标数据
     * @param shopId     店铺id
     * <AUTHOR>
     * @date 2024/06/30
     */
    public void noProductMappingFlow(BusinessType type, JSONObject json, ChannelTypeEnum channelTypeEnum,Class<T> targetClass,T targetData, String shopId,List<E> list,BusinessTypeMappingEnum mappingEnum){

        // 业务数据操作
        operationalDataForBusiness(type ,channelTypeEnum, json,targetClass,list,mappingEnum);
        // 绑定面单
//        expandBusiness(targetData, type.name());
        // 支付 后续
//        if(BusinessTypeMappingEnum.HAVE_MAPPING.equals(mappingEnum)){
//            if (isNeedPay(list)){
//                payOrder(targetData,type.name());
//            }
//
//        }
    }
    public abstract Boolean isNeedPay(List<E> list);
    /**
     * 功能描述：插入业务数据 多线程优化版v2
     *
     * @param type            类型
     * @param channelTypeEnum 通道类型枚举
     * @param json            json
     * @param targetClass     目标类
     * <AUTHOR>
     * @date 2024/07/02
     */
    public abstract void operationalDataForBusiness(BusinessType type, ChannelTypeEnum channelTypeEnum, JSONObject json, Class<T> targetClass, List<E> list, BusinessTypeMappingEnum mappingEnum);


    /**
     * 功能描述：获取业务数据
     *
     * @param type 类型
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/07/02
     */
    public abstract T getBusinessData(List<E> list, BusinessTypeMappingEnum type);

    /**
     * 功能描述：具有产品映射业务流
     *
     * @param targetData 目标数据
     * @param shopId     店铺id
     * <AUTHOR>
     * @date 2024/06/30
     */
    public void haveProductMappingFlow(BusinessType type, JSONObject json, ChannelTypeEnum channelTypeEnum,Class<T> targetClass,T targetData, String shopId,List<E> list ,BusinessTypeMappingEnum mappingEnum){
        //
//        T businessData = getBusinessData(list, mappingEnum);
//        if(isNeedExpressSheetFlow(targetData)){
//            expressSheet(targetData, shopId);
//        }
        operationalDataForBusiness(type ,channelTypeEnum, json,targetClass,list,mappingEnum);
//        insertBusinessData(type ,channelTypeEnum, json,targetClass,targetData);
        // 进行面单的绑定
        expandBusiness(targetData, type.name());
        // 支付 实际需要支付的targetData要小 例如 映射的订单 10个 没映射的订单3个 一共13个 此处的targetData应该需要过滤掉
        payOrder(targetData,type.name());
    }




    /**
     * 功能描述：部分商品映射业务流
     *
     * @param noMapping  没有地图
     * @param haveMapping 具有映射
     * @param shopId      店铺id
     * <AUTHOR>
     * @date 2024/06/30
     */
    public CompletableFuture<Void> partialProductMappingFlow(BusinessType type, JSONObject json, ChannelTypeEnum channelTypeEnum,T noMapping,T haveMapping, String shopId){
//        if(ObjectUtil.isNotEmpty(noMappeing)){
//            noProductMappingFlow(noMappeing,shopId);
//        }
//        if(ObjectUtil.isNotEmpty(haveMapping)){
//            haveProductMappingFlow(haveMapping,shopId);
//        }
        return null;
    }

    /**
     * 功能描述：产生业务单号集合
     *
     * @param map 数据集合 通过 BusinessTypeMappingEnum 获取对应的数据
     * @return {@link ConcurrentHashMap }
     * <AUTHOR>
     * @date 2024/06/30
     */
    public abstract ConcurrentHashMap<String,ConcurrentHashMap<String,String>> generateBusinessNo(ConcurrentHashMap<String, T> map);



    /**
     * 功能描述：数据验证,为null,返回false
     *
     * @param targetData 目标数据
     * <AUTHOR>
     * @date 2024/04/16
     */
    public abstract boolean dataValidation(T targetData);
    public abstract boolean dataValidation(List<E> list);

    /**
     * 功能描述：支付指令
     *
     * @param targetData 目标数据
     * @param name       名称
     * <AUTHOR>
     * @date 2024/04/16
     */
    public abstract void payOrder(T targetData, String name);

    /**
     * 功能描述：扩展的业务操作
     *
     * @param targetData 目标数据
     * @param name       名称
     * <AUTHOR>
     * @date 2024/04/09
     */
    public abstract void expandBusiness(T targetData, String name);


    public abstract void expressSheet(T target, String shopId, String tiktokAppKey,
                                      String tiktokAppSecret);

    public abstract void expressSheet(TikTokRespBaseEntity target, String shopId, String tiktokAppKey, String tiktokAppSecret);

    public abstract T getOrderListForTest(String msg, Class<T> targetClass);


    public abstract boolean isExists(T targetData, String type);

    public T  dataPlatformFlowV2ForTest(BusinessType type, JSONObject json, ChannelTypeEnum channelTypeEnum, TikTokApiEnums enums, String shopId, Class<T> targetClass,String msg, String tiktokAppKey,
                                        String tiktokAppSecret) {
        if(isNeedByChannel(json,enums)){
            log.info("当前线程:{},渠道:{}---订单数据:{},准备进入订单数据分析------->",Thread.currentThread(),channelTypeEnum.name(),json+"\n");
            T targetData = null;
            if(BusinessType.ORDER_V2.equals(type)){
                targetData = getOrderListForTest(msg, targetClass);

            }

            log.info("当前线程:{},渠道:{}---订单数据:{},准备数据存储------->",Thread.currentThread(),channelTypeEnum.name(), JSON.toJSON(json));

            // 校验api走向,判断订单内的商品是否有映射
            commodityRouting(type, json, channelTypeEnum, shopId, targetClass, targetData,tiktokAppKey,tiktokAppSecret);

            return (T)targetData;
        }
        return null;
    }
}
