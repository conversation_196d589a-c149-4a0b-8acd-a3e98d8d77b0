package com.zsmall.common.enums.order;

import com.baomidou.mybatisplus.annotation.IEnum;


/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/11 16:15
 */
public enum OrderCancelStateEnum implements IEnum<Integer> {
    None(0),
    <PERSON><PERSON><PERSON>(1),
    <PERSON>cel<PERSON>(2),
    Failed(3),
    A<PERSON><PERSON><PERSON>(4),

    Mixed(5),
    ;
    OrderCancelStateEnum(Integer code) {
        this.code = code;
    }
    /**
     * 获取可以查询到的订单状态
     *
     * @return
     */
    private final int code;

    public static String getDisplayName(Integer cancelStatus) {
        for (OrderCancelStateEnum orderCancelStateEnum : OrderCancelStateEnum.values()) {
            if (orderCancelStateEnum.getValue().equals(cancelStatus)) {
                return orderCancelStateEnum.name();
            }
        }
        return null;
    }
    public static OrderCancelStateEnum fromCode(int code) {
        for (OrderCancelStateEnum state : values()) {
            if (state.code == code) {
                return state;
            }
        }
        throw new IllegalArgumentException("无效状态码: " + code);
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public Integer getValue() {
        return this.code;
    }
}
