package com.zsmall.common.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hengjian.common.core.validate.TripartiteEntryGroup;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * lty notes  三方录入dto
 *
 * <AUTHOR> Theo
 * @create 2024/1/9 14:35
 */
@Data
@Accessors(chain = true)
public class OrderReceiveFromThirdDTO {
    /**
     * 行订单项目id
     */// @NotNull(message = "",groups = {TripartiteEntryGroup.class})
    // @ApiModelProperty(value = "",required = true)
    private String lineOrderItemId;
    /**
     * 租户 ID
     */
    @NotNull(message = "租户id", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "租户id", required = true)
    private String tenantId;

    private String supTenantId;

    /**
     * 渠道店铺标识
     */
    @NotNull(message = "三方标识不能为空,渠道店铺标识", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "渠道店铺标识", required = true)
    private String thirdChannelFlag;

    /**
     * 订单号
     */
    @NotNull(message = "渠道订单号不能为空", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String orderNo;

    /**
     * 订单状态
     */
    @NotNull(message = "订单状态", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "订单状态,订单状态:", required = true)
    private String orderStatus;

    /**
     * 通道类型
     */
    private ChannelTypeEnum channelType;

    /**
     * 总量
     */
    @NotNull(message = "商品总数", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "商品总数", required = true)
    private int totalQuantity;

    /**
     * 币种
     */
    @NotNull(message = "币种", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "币种", required = false)
    private String currencyCode;
//
//    @NotNull(message = "总金额", groups = {TripartiteEntryGroup.class})
//    @ApiModelProperty(value = "金额", required = false)
//    private BigDecimal itemAmount;

    /**
     * 下单日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


    /**
     * 最迟交货时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date latestDeliveryTime;

    /**
     * 下单日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date paidTime;

    /**
     * 发货方式：PickUp-自提，DropShipping-代发
     */
    @ApiModelProperty(value = "发货方式：PickUp-自提，DropShipping-代发", required = true)
    private String logisticsType;


    /**
     * 是否需要贴标签 0:是 1:否
     */
    private Integer isNeedLabeling;

    /**
     * 调拨订单.采购订单号
     */
    private String remark;

    /**
     * 销售订单详细信息
     */
    private SaleOrderDetailDTO saleOrderDetails;

    /**
     * 产品明细
     */
    private List<SaleOrderItemDTO> saleOrderItemsList;

    private TikTokRecipientAddress address;

    /**
     * 税前总额
     */
    private String subTotal;

    /**
     * 税后
     */
    private String totalAmount;

    /**
     * 店铺id-商品映射用得到
     */
    private Long shopId;
    /**
     * 是否为一个订单多个sku的 true：是  false和null：不是
     */
    private Boolean isMultiple;
    // 行id
    private String orderExtendId;
    // 分销新版订单ID
    private String distributionOrderNo;
    public List<SaleOrderItemDTO> pushSaleOrderItemsList(List<SaleOrderItemDTO> saleOrderItemsList) {
        this.saleOrderItemsList = saleOrderItemsList;
        return this.saleOrderItemsList;
    }
    // 订单-》下单了多个产品/多个地址
}
