package com.zsmall.common.enums.statuscode;

import com.hengjian.common.core.domain.RStatusCodeBase;

/**
 * 订单相关错误码枚举
 *
 * <AUTHOR>
 * @date 2023/6/10
 */
public enum OrderStatusCodeEnum implements RStatusCodeBase {

    /**
     * 仅支持代发
     */
    ONLY_SUPPORTS_DROP_SHIPPING("", "zsmall.orders.onlySupportsDropShipping"),

    /**
     * 仅支持自提
     */
    ONLY_SUPPORTS_PICK_UP("", "zsmall.orders.onlySupportsPickUp"),

    /**
     * 商品不支持从某地运送到某地
     */
    NOT_SUPPORTED_SHIP_TO("", "zsmall.orders.notSupportedShipTo"),

    /**
     * 订单支付时出现未知错误，请重试
     * An unknown error occurred during order payment. Please try again
     */
    ORDER_PAY_UNKNOWN_ERROR("", "zsmall.orders.orderPayUnknownError"),

    /**
     * 订单支付失败，附加错误信息
     */
    ORDER_PAY_ERROR_MESSAGE("", "zsmall.orders.orderPayError.message"),

    /**
     * 订单库存调整时出现未知错误，请重试
     * An Unknown error occurred during order stock adjustment. Please try again
     */
    ORDER_STOCK_ADJUST_UNKNOWN_ERROR("", "zsmall.orders.orderStockAdjustUnknownError"),

    /**
     * 订单金额不匹配，无法支付
     * The order amount does not match and cannot be paid
     */
    ORDER_AMOUNT_NOT_MATCH("", "zsmall.orders.orderAmountNotMatch"),

    /**
     * 订单状态已变更，请重新支付
     * The Order state has changed. Please pay again.
     */
    ORDER_STATE_HAS_CHANGED("", "zsmall.orders.orderStateHasChanged"),

    /**
     * 订单状态异常，请重新支付，若多次出现请联系平台员工
     * The order state is abnormal. Please pay again. If it occurs multiple times, please contact the platform staff.
     */
    ORDER_STATE_EXCEPTION("", "zsmall.orders.orderStateException"),

    /**
     * 订单运输标签不存在
     * Order shipping label not exists
     */
    ORDER_SHIPPING_LABEL_NOT_EXISTS("", "zsmall.orders.orderShippingLabelNotExists"),

    /**
     * 运输标签与物流单号数量不一致
     * The quantity of shipping label is inconsistent with that of tracking No.
     */
    INCONSISTENT_SHIPPING_LABEL_AND_TRACKING_NO("", "zsmall.orders.inconsistentShippingLabelAndTrackingNo"),

    /**
     * 第三方仓库创建发货单时发生未知错误
     * An unknown error occurred during third-party warehouse create shipping order
     */
    THIRD_WAREHOUSE_CREATE_SHIPPING_ORDER_ERROR("", "zsmall.orders.thirdWarehouseCreateShippingOrderError"),

    /**
     * Wayfair订单登记发货失败，Wayfair API未返回相关数据，请稍候重试
     * Wayfair register shipping failed. Wayfair API did not return relevant data, please try again later.
     */
    WAYFAIR_REGISTER_SHIPPING_FAILED("", "zsmall.orders.wayfairRegisterShippingFailed"),

    /**
     * Wayfair订单登记发货出现未知错误，原因：{0}
     * Wayfair register shipping encountered an unknown error, reason: {0}
     */
    WAYFAIR_REGISTER_SHIPPING_ENCOUNTERED_ERROR("", "zsmall.orders.wayfairRegisterShippingEncounteredError"),

    ORDER_NO_TRACKING_INFO("","zamall.orders.orderNoTrackingInfo"),
    /**
     * 订单无法匹配仓库（仓库的可配送国家不包含订单的国家）
     */
    ORDER_CANNOT_MATCH_WAREHOUSE("","zamall.orders.orderCannotMatchWarehouse"),

    /**
     * sku地区价格未维护
     */
    SKU_REGION_PRICE_NOT_MAINTAINED("","zsmall.orders.skuRegionPriceNotMaintained"),

    /**
     * 订单活动异常 ERP拉库存导致的异常
     */
    ORDER_ACTIVITY_EXCEPTION_STOCK_PULL_LOCK_EXCEPTION("","zsmall.orders.orderActivityException.stockPullLockException"),
    /**
     * 订单活动异常 ERP锁库存接口异常
     */
    ORDER_ACTIVITY_EXCEPTION_ERP_LOCK_EXCEPTION("","zsmall.orders.orderActivityException.erpLockException"),
    /**
     * 订单活动异常 ERP释放锁货库存接口异常
     */
    ORDER_ACTIVITY_EXCEPTION_ERP_RELEASE_EXCEPTION("","zsmall.orders.orderActivityException.erpReleaseException"),
    /**
     * 活动订单释放库存失败
     */
    ORDER_EXCEPTION_RELEASE_EXCEPTION("","zsmall.orders.orderException.releaseException")

    ;

    /**
     * 响应子编号
     */
    private String subCode;

    /**
     * 信息编号，用于在messages.properties等文件中获取国际化信息
     */
    private String messageCode;

    private Object[] args;

    OrderStatusCodeEnum(String subCode, String messageCode) {
        this.subCode = subCode;
        this.messageCode = messageCode;
    }

    public OrderStatusCodeEnum args(Object... args) {
        this.args = args;
        return this;
    }

    /**
     * 获取响应子编号
     * @return
     */
    @Override
    public String getSubCode() {
        return this.subCode;
    }

    /**
     * 获取信息编号，用于在messages.properties等文件中获取国际化信息
     * @return
     */
    @Override
    public String getMessageCode() {
        return this.messageCode;
    }

    @Override
    public Object[] getArgs() {
        return this.args;
    }
}
