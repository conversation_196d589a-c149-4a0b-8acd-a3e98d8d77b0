package com.zsmall.common.enums.productActivity;

import com.hengjian.common.core.domain.RStatusCodeBase;

/**
 * <AUTHOR>
 * @date 2025年5月28日  17:35
 * @description:
 */
public enum ProductActiveImportErrorEnum implements RStatusCodeBase {

    /**
     * xx字段不能为空
     */
    FIELD_CANNOT_EMPTY("", "zsmall.productActiveImport.fieldCannotEmpty"),

    /**
     * xx字段重复
     */
    FIELD_REPEAT("", "zsmall.productActiveImport.fieldRepeat"),

    /**
     * 仓库名称重复
     */
//    WAREHOUSE_NAME_REPEAT("", "zsmall.productActiveImport.warehouseNameRepeat"),
//
//    /**
//     * 仓库编码重复
//     */
//    WAREHOUSE_CODE_REPEAT("", "zsmall.productActiveImport.warehouseCodeRepeat"),

    /**
     * xx字段长度最大xx个字符
     */
    FIELD_LENGTH_MAX("", "zsmall.productActiveImport.fieldLengthMax"),
    /**
     * 仓库名称长度最大20个字符
     */
//    WAREHOUSE_NAME_LENGTH_MAX("", "zsmall.productActiveImport.warehouseNameLengthMax"),
//
//    /**
//     * 仓库编码长度最大20个字符
//     */
//    WAREHOUSE_CODE_LENGTH_MAX("", "zsmall.productActiveImport.warehouseCodeLengthMax"),

    /**
     * xx字段不存在
     */
    FIELD_NOT_EXIST("", "zsmall.productActiveImport.fieldNotExist"),
    /**
     * xx对象不存在
     */
    OBJECT_NOT_EXIST("", "zsmall.productActiveImport.objectNotExist"),
    /**
     * 国家/地区不存在
     */
//    COUNTRY_NOT_EXIST("", "zsmall.productActiveImport.countryNotExist"),

    /**
     * 国家/地区长度最大50个字符
     */
//    COUNTRY_LENGTH_MAX("", "zsmall.productActiveImport.countryLengthMax"),

    /**
     * 州/省/地区不存在
     */
//    STATE_NOT_EXIST("", "zsmall.productActiveImport.stateNotExist"),

    /**
     * 州/省/地区长度最大50个字符
     */
//    STATE_LENGTH_MAX("", "zsmall.productActiveImport.stateLengthMax"),

    /**
     * 城市长度最大20个字符
     */
//    CITY_LENGTH_MAX("", "zsmall.productActiveImport.cityLengthMax"),

    /**
     * 街道地址长度最大50个字符
     */
//    STREET_LENGTH_MAX("", "zsmall.productActiveImport.streetLengthMax"),

    /**
     * 详细地址长度最大50个字符
     */
//    DETAIL_LENGTH_MAX("", "zsmall.productActiveImport.detailLengthMax"),
    /**
     * 邮编长度最大10个字符
     */
//    ZIP_CODE_LENGTH_MAX("", "zsmall.productActiveImport.zipCodeLengthMax"),

    /**
     * xx字段格式不正确
     */
    FIELD_FORMAT_ERROR("", "zsmall.productActiveImport.fieldFormatError"),

    /**
     * 邮编格式不正确
     */
//    ZIP_CODE_FORMAT_ERROR("", "zsmall.productActiveImport.zipCodeFormatError"),
    /**
     * 仓管姓名长度最大50个字符
     */
//    MANAGER_NAME_LENGTH_MAX("", "zsmall.productActiveImport.managerNameLengthMax"),
    /**
     * 仓管姓名格式不正确
     */
//    MANAGER_NAME_FORMAT_ERROR("", "zsmall.productActiveImport.managerNameFormatError"),
    /**
     * 仓管电话格式不正确
     */
//    MANAGER_PHONE_FORMAT_ERROR("", "zsmall.productActiveImport.managerPhoneFormatError"),
    /**
     * 仓管电话最大支持12个字符
     */
//    MANAGER_PHONE_LENGTH_MAX("", "zsmall.productActiveImport.managerPhoneLengthMax"),

    /**
     * 支持可配送的国家不存在
     */
//    DELIVERY_COUNTRY_NOT_EXIST("", "zsmall.productActiveImport.deliveryCountryNotExist"),

    /**
     * 支持可配送的国家长度最大50个字符
     */
//    DELIVERY_COUNTRY_LENGTH_MAX("", "zsmall.productActiveImport.deliveryCountryLengthMax"),

    /**
     * 字段值不一致
     */
    FIELD_VALUE_INCONSISTENT("", "zsmall.productActiveImport.fieldValueInconsistent"),

    /**
     * 仓库库存数不一致
     */
    WAREHOUSE_STOCK_INCONSISTENT("", "zsmall.productActiveImport.warehouseStockInconsistent"),
    ;

    /**
     * 响应子编号
     */
    private String subCode;

    /**
     * 信息编号，用于在messages.properties等文件中获取国际化信息
     */
    private String messageCode;

    private Object[] args;

    ProductActiveImportErrorEnum(String subCode, String messageCode) {
        this.subCode = subCode;
        this.messageCode = messageCode;
    }

    public ProductActiveImportErrorEnum args(Object... args) {
        this.args = args;
        return this;
    }

    /**
     * 获取响应子编号
     * @return
     */
    @Override
    public String getSubCode() {
        return this.subCode;
    }

    /**
     * 获取信息编号，用于在messages.properties等文件中获取国际化信息
     * @return
     */
    @Override
    public String getMessageCode() {
        return this.messageCode;
    }

    @Override
    public Object[] getArgs() {
        return this.args;
    }
}
