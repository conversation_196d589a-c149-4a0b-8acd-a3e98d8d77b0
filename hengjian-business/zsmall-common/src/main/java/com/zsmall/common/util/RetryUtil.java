package com.zsmall.common.util;

import lombok.extern.log4j.Log4j2;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2025年8月20日  15:28
 * @description: 重试工具类
 */
@Log4j2
public class RetryUtil {
    /**
     * 执行第三方接口调用
     * @param apiCall 接口调用逻辑（返回true表示成功）
     * @param maxRetries 最大重试次数
     * @param retryIntervalMillis 重试间隔（毫秒）
     * @return 是否最终成功
     */
    public static boolean executeWithRetry(Supplier<Boolean> apiCall, int maxRetries, long retryIntervalMillis) {
        int attempt = 0;
        while (attempt <= maxRetries) {
            try {
                boolean result = apiCall.get();
                if (result) {
                    return true;
                }
                log.warn("接口调用失败，准备第{}次重试...", attempt + 1);
            } catch (Exception e) {
                log.error("接口调用异常", e);
            }

            if (++attempt > maxRetries) {
                break;
            }

            try {
                Thread.sleep(retryIntervalMillis);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("重试等待中断", e);
                return false;
            }
        }
        return false;
    }

}
