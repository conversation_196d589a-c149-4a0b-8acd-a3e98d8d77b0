package com.zsmall.common.enums.bill;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 账单关系类型枚举
 *
 * <AUTHOR>
 * @date 2022/11/29
 */
public enum RelationTypeEnum implements IEnum<String> {

    /**
     * 主订单
     */
    Order(5),

    /**
     * 子订单
     */
    OrderItem(10),

    /**
     * 退款单
     */
    OrderRefund(20),

    /**
     * 分销商促销活动-仓储费
     */
    ActivityStorageFee(30),

    /**
     * 分销商促销活动-违约金（违约订金）
     */
    ActivityPenaltyFee(40),

    /**
     * 清货订单
     */
    LiquidationOrder(50),

    /**
     * 清货佣金
     */
    LiquidationCommission(60),

    /**
     * 平台扣款
     */
    PlatformDeduct(70),

    /**
     * 平台充值
     */
    PlatformRemit(80),

    ;

    private int sort;

    RelationTypeEnum(int sort) {
        this.sort = sort;
    }

    public int getSort() {
        return sort;
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }
}
