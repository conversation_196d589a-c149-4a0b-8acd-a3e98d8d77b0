package com.zsmall.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/1/9 14:33
 */
@Getter
@AllArgsConstructor
public enum OpenApiEnum implements IEnum<String> {
    CREATE_ORDER("createOrderFlow"),

    RECEIVE_ORDER_ATTACHMENT("receiveOrderAttachment"),;

    private String apiName;
    @Override
    public String getValue() {
        return this.name();
    }

    public static String getApiName(String apiName) {
        for (OpenApiEnum openApiEnum : OpenApiEnum.values()) {
            if (openApiEnum.getApiName().equals(apiName)) {
                return openApiEnum.apiName;
            }
        }
        return null;
    }
}
