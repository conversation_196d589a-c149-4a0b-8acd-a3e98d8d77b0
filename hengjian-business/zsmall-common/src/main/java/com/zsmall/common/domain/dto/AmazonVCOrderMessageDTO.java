package com.zsmall.common.domain.dto;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年10月21日  17:06
 * @description:亚马逊VC订单XML对应的实体类
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
public class AmazonVCOrderMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String account_id;
    private String ordernum;
    private BigDecimal amount;
    private String currency_code;
    private String status;
    private String shipping_status;
    private String buy_name;
    private String buy_email;
    private String display_seller;
    private String fulfillment_channel;
    private String shipping_method_code;
    private String ship_service_level;
    private String shipping_name;
    private String addressline1;
    private String addressline2;
    private String city;
    private String state_or_region;
    private String country;
    private String country_code;
    private String postal_code;
    private String phone;
    private String created;
    private String carrier;
    private String payment_date;
    private String updated;
    private String latest_ship_date;
    private String earliest_ship_date;
    private String is_business_order;
    private String po_number;
    private String warehouse_code;
    private String address_type;
    private String carrier_addition;
    private String is_prime;
//    private String platform_ship_time;
    // 2025-02-12 新增字段
    private String sales_channel;
    private String purchase_order_state;
    private String customer_refer_no;
    private String ship_to_party;
    private String fob_shipment_payment_method;
    private String address_code;

    private List<AmazonVCOrderItemMessageDTO> items;

    private String tenantId;

    private LogisticsTypeEnum logisticsType;

    /**
     * 订单类型 1:有地址的订单 2:没有地址的订单
     */
    private Integer orderType;

    /**
     * 异常code 0:无异常 1:商品映射异常 2:订单支付异常 3:库存不足异常 4:发货方式异常
     */
    private Integer exceptionCode;

    /**
     * 错误信息
     */
    private JSONObject payErrorMessage;

    /**
     * 渠道 id
     */
    private Long channelId;

    /**
     * 订单来源 1: 接口接入 2: excel导入 3: 商城下单 4: openApi
     */
    private Integer orderSource;

    /**
     * 标识唯一订单的id
     */
    private String channel_order_item_id;

    /**
     * 渠道类型
     */
    private ChannelTypeEnum channelType;

    /**
     * 是否为一个订单多个sku的 true：是  false和null：不是
     */
    private Boolean isMultiple;

    private List<Object> distributorProductActivityList;
    public List<AmazonVCOrderItemMessageDTO> pushAmazonVCOrderItemDTOList(List<AmazonVCOrderItemMessageDTO> items){
        this.items = items;
        return items;
    }

}
