package com.zsmall.common.enums.storageFee;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025年7月3日  14:36
 * @description:
 */
@Getter
public enum FeeStateEnum {

    /**
     * 待确认
     */
    WAIT_CONFIRM(0, "待确认"),
    /**
     * 确认中
     */
    CONFIRMING(1, "确认中"),
    /**
     * 已确认
     */
    CONFIRMED(2, "已确认");
    private final Integer value;

    private final String name;

    FeeStateEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 根据value获取枚举
     */
    public static FeeStateEnum getByValue(Integer value) {
        for (FeeStateEnum item : values()) {
            if (item.value == value) {
                return item;
            }
        }
        return null;
    }
}
