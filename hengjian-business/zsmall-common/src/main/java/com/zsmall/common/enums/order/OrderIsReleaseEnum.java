package com.zsmall.common.enums.order;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025年8月20日  16:09
 * @description: 订单是否释放库存枚举
 */
@Getter
public enum OrderIsReleaseEnum {

    Failed(0,"释放库存失败"),
    Success(1,"释放库存成功");

    private Integer value;

    private String name;

    OrderIsReleaseEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static OrderIsReleaseEnum getByName(Integer value){
        for(OrderIsReleaseEnum orderIsReleaseEnum:OrderIsReleaseEnum.values()){
            if(orderIsReleaseEnum.getValue().equals(value)){
                return orderIsReleaseEnum;
            }
        }
        return null;
    }
}
