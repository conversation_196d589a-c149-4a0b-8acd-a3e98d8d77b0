package com.zsmall.common.domain.dto.stock;

import com.zsmall.common.enums.order.LogisticsTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 调整库存DTO
 *
 * <AUTHOR>
 * @date 2023/6/14
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdjustStockDTO {

    /**
     * 商品SKU唯一编号
     */
    private String productSkuCode;

    /**
     * 物流方式（仅扣减库存时需要）
     */
    private LogisticsTypeEnum logisticsType;

    /**
     * 活动编号
     */
    private String activityCode;

    /**
     * 指定仓库（使用仓库唯一系统编号，归还库存时必须）
     */
    private String specifyWarehouse;

    /**
     * 判断仓库是否支持第三方物流账户
     */
    private Boolean logisticsAccount;

    /**
     * 目的地国家二位代号
     */
    private String destCountry;

    /**
     * 目的地邮编
     */
    private String destZipCode;

    /**
     * 调整数量（正数时为增加库存，负数时为减少库存）
     */
    private Integer adjustQuantity;
}
