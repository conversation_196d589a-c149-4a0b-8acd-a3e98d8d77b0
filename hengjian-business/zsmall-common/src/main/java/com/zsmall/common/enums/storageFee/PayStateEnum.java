package com.zsmall.common.enums.storageFee;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025年7月4日  14:34
 * @description:
 */
@Getter
public enum PayStateEnum {

    /**
     * 未支付
     */
    UNPAID(0, "未支付"),
    /**
     * 已支付
     */
    PAID(1, "已支付"),

    /**
     * 支付失败
     */
    FAILED(2, "支付失败")
    ;
    private final Integer value;

    private final String name;

    PayStateEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 根据value获取枚举
     */
    public static PayStateEnum getByValue(Integer value) {
        for (PayStateEnum item : values()) {
            if (item.value == value) {
                return item;
            }
        }
        return null;
    }
}
