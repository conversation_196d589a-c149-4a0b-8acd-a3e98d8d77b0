package com.zsmall.common.enums.productActivity;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品活动类型
 */
@Getter
@AllArgsConstructor
public enum ProductActivityExceptionEnum implements IEnum<String> {

    /**
     * 无异常
     */
    NOT_EXCEPTION(0, "无异常"),

    /**
     * ERP拉库存导致的异常
     */
    STOCK_PULL_LOCK_EXCEPTION(1, "ERP拉库存导致的异常"),

    /**
     * ERP锁货接口异常
     */
    ERP_LOCK_EXCEPTION(2, "ERP锁库存接口异常"),

    /**
     * ERP释放库存接口异常
     */
    ERP_RELEASE_EXCEPTION(3, "ERP释放锁货库存接口异常");

    /**
     * -- GETTER --
     *  获取异常代码
     */
    private final int code;
    private final String errMessage;

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }


}
