package com.zsmall.common.enums.bill;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 生成状态枚举
 *
 * <AUTHOR>
 * @date 2023/6/30
 */
public enum GenerateStateEnum implements IEnum<Integer> {

    /**
     * 未生成
     */
    NotGenerated(0),

    /**
     * 生成中
     */
    Generating(10),

    /**
     * 已生成
     */
    Generated(20),

    /**
     * 生成失败
     */
    Failed(30),

    ;

    private Integer code;

    GenerateStateEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public Integer getValue() {
        return this.code;
    }

}
