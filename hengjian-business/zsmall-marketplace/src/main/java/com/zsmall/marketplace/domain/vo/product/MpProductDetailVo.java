package com.zsmall.marketplace.domain.vo.product;

import cn.hutool.json.JSONArray;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 响应体-Marketplace商品详情
 *
 * <AUTHOR>
 * @date 2023/9/8
 */
@Data
public class MpProductDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 发货时间
     */
    private String deliverGoodsTime;


    /**
     * 送达时间
     */
    private String deliveryTime;

    /**
     * 国家
     */
    private String country;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 商品主图Url
     */
    private String firstImageShowUrl;

    /**
     * 货架状态
     */
    private String shelfState;

    /**
     * 商品须知
     */
    private String productNotice;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品分类集合
     */
    private List<MpProductDetailCategoryVo> categoryList;

    /**
     * 支持的物流方式
     */
    private String supportedLogistics;

    /**
     * 商品其他附件
     */
    private MpAttachmentVo otherAttachment;

    /**
     * 是否已加入收藏夹（已登录才会有）
     */
    private Boolean favorites;

    /**
     * 已铺货渠道集合（已登录才会有）
     */
    private List<String> distributionChannels;

    /**
     * 最低价格
     */
    private BigDecimal minPrice;

    /**
     * 最高价格
     */
    private BigDecimal maxPrice;

    /**
     * 商品Sku集合
     */
    private List<MpProductSkuDetailVo> skuList;

    /**
     * 禁售渠道集合
     */
    private JSONArray forbiddenChannels;

    /**
     * 商品可选规格集合
     */
    private List<MpProductAttributeVo> productOptionalSpecList = new ArrayList<>();

    /**
     * 商品通用规格集合
     */
    private List<MpProductAttributeVo> productGenericSpecList = new ArrayList<>();

    /**
     * 商品特色集合
     */
    private List<MpProductAttributeVo> productFeatureList = new ArrayList<>();

    /**
     * 可选锁货
     */
//    private List<MpProductActivitySelectVo> StockLock;
//
//    /**
//     * 可选圈货
//     */
//    private List<MpProductActivitySelectVo> Buyout;

    /**
     * 添加可选规格数据
     */
    public MpProductDetailVo addOptionalSpec(MpProductAttributeVo attributeVo) {
        productOptionalSpecList.add(attributeVo);
        return this;
    }

    /**
     * 添加通用规格数据
     */
    public MpProductDetailVo addGenericSpec(MpProductAttributeVo attributeVo) {
        productGenericSpecList.add(attributeVo);
        return this;
    }

    /**
     * 添加商品特色数据
     */
    public MpProductDetailVo addFeature(MpProductAttributeVo attributeVo) {
        productFeatureList.add(attributeVo);
        return this;
    }

}
