package com.zsmall.marketplace.domain.vo.product;

import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityDetails;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 响应体-Marketplace商品Sku详情
 *
 * <AUTHOR>
 * @date 2023/9/8
 */
@Data
public class MpProductSkuDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * product_sku id
     */
    private Long productSkuId;

    /**
     * sku
     */
    private String sku;

    /**
     * upc
     */
    private String upc;

    /**
     * Item No.
     */
    private String productSkuCode;

    /**
     * 规格组成名称，示例：尺寸-大;颜色-白色
     */
    private String specComposeName;

    /**
     * 规格值名称，示例：大/白色
     */
    private String specValName;

    /**
     * 规格值名称数组
     */
    private List<String> specValNames;

    /**
     * 自提仓库库存总数
     */
    private Integer pickUpStockTotal;
    /**
     * 代发仓库库存总数
     */
    private Integer dropShippingStockTotal;

    /**
     * 发货国家
     */
    private String shippingFrom;

    /**
     * 运输方式
     */
    private String transportMethod;

    /**
     * 代发价
     */
    private BigDecimal dropShippingPrice;

    /**
     * pick-up会员价格
     */
    private BigDecimal memberPrice;

    /**
     * 代发会员价格
     */
    private BigDecimal dropMemberPrice;

    /**
     * 自提价
     */
    private BigDecimal pickUpPrice;

    /**
     * 建议零售价
     */
    private BigDecimal msrp;

    /**
     * 是否允许销售（若SKU开启了全渠道管控，只有指定的用户才能销售）
     */
    private boolean allowSale = true;

    /**
     * 订单处理时效
     */
    private Long processingTime;

    /**
     * 商品尺寸
     */
    private String size;

    /**
     * 商品重量
     */
    private String weight;

    /**
     * 商品打包尺寸
     */
    private String packSize;

    /**
     * 商品打包重量
     */
    private String packWeight;

    /**
     * 首图Url
     */
    private String imageShowUrl;

    /**
     * 附件集合
     */
    private List<MpAttachmentVo> attachmentList;

    /**
     * 视频附件集合
     */
    private List<MpAttachmentVo> videoAttachmentList;
    /**
     * 分仓库存集合
     */
    private List<HashMap<String,Object>>  skuStockList;
    /**
     * 是否收藏
     */
    private Boolean isTenantFavorite;
    /**
     * 活动代发价
     */
    private BigDecimal pickUpActivePrice;
    /**
     * 活动代发
     */
    private BigDecimal dropShippingActivePrice;
    /**
     * 商品活动信息
     */
    private Map<String,List<SupplierProductActivityDetails>> productActivitieMap;
}
