package com.zsmall.marketplace.service;

import com.zsmall.marketplace.domain.bo.AddToFavoritesBo;
import com.zsmall.marketplace.domain.bo.MpProductSearchBo;
import com.zsmall.marketplace.domain.bo.mpWholesale.MpWholesaleProductBo;
import com.zsmall.marketplace.domain.bo.product.RecentlyProductBo;
import com.zsmall.marketplace.domain.vo.mpWholesale.MpWholesaleProductPageVo;
import com.zsmall.marketplace.domain.vo.mpWholesale.MpWholesaleProductDetailVo;
import com.zsmall.marketplace.domain.vo.product.*;
import com.zsmall.product.entity.domain.vo.product.ProductSkuSimpleVo;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * Marketplace商品相关接口
 *
 * <AUTHOR>
 * @date 2023/7/25
 */
public interface MpProductService {

    /**
     * 初始化ElasticSearch商品数据
     */
    R<Void> initEsProduct();

    /**
     * 搜索最近商品
     */
    R<List<MpProductVo>> searchRecentlyProduct(RecentlyProductBo bo);

    /**
     * 查询商品
     */
    R<MpProductListVo> searchProductPage(MpProductSearchBo bo) throws Exception;

    /**
     * 查询批发商品
     */
    MpWholesaleProductPageVo searchWholesaleProductPage(MpWholesaleProductBo bo);

    /**
     * 查询分类树
     */
    R<List<MpProductCategoryVo>> searchProductCategoryTree();

    /**
     * 获取商品详情
     */
    R<MpProductDetailVo> getProductDetail(String productCode,String site) throws Exception;

    /**
     * 获取批发商品详情
     */
    R<MpWholesaleProductDetailVo> getWholesaleProductDetail(String productCode);

    /**
     * 获取商品问答列表
     */
    TableDataInfo<MpProductQuestionVo> getProductQAPage(String productSkuCode, String question, PageQuery pageQuery) throws Exception;

    /**
     * 分销商参加商品活动
     */
//    R<Void> participateProductActivity(ParticipateActivityBo bo);
//
//    /**
//     * 查询商品可参与的活动
//     */
//    R<MpProductActivityVo> queryMpActivity(String productSkuCode);

    /**
     * 获取商品Sku简单详情
     */
    ProductSkuSimpleVo getProductSkuSimpleDetail(String productSkuCode);

    /**
     * 加入收藏夹
     */
    R<Void> addToFavorites(AddToFavoritesBo bo);
    R<Void> addToFavoritesV2(AddToFavoritesBo bo);

    void dealTenantFavoritesData();

}
