package com.hengjian.common.tenant.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.hengjian.common.tenant.helper.TenantHelper;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;

import java.io.Serializable;
import java.util.List;

/**
 * 自定义 Mapper 接口, 实现 自定义扩展
 *
 * @param <T> table 泛型
 * @param <V> vo 泛型
 * <AUTHOR> Li
 * @since 2021-05-13
 */
@SuppressWarnings("unchecked")
public interface TenantMapperPlus<T, V> extends BaseMapperPlus<T, V> {

    Log log = LogFactory.getLog(TenantMapperPlus.class);

    default T selectByIdNotTenant(Long id) {
        return TenantHelper.ignore(() -> this.selectById(id));
    }

    default List<T> selectListNotTenant(Wrapper<T> wrapper) {
        return TenantHelper.ignore(() -> this.selectList(wrapper));
    }

    default IPage<T> selectPageNotTenant(IPage<T> page, Wrapper<T> wrapper) {
        return TenantHelper.ignore(() -> this.selectPage(page, wrapper));
    }

    default T selectOneNotTenant(Wrapper<T> wrapper) {
        return TenantHelper.ignore(() -> this.selectOne(wrapper));
    }

    default V selectVoByIdNotTenant(Serializable id) {
        return TenantHelper.ignore(() -> selectVoById(id, this.currentVoClass()));
    }

    default V selectVoOneNotTenant(Wrapper<T> wrapper) {
        return TenantHelper.ignore(() -> selectVoOne(wrapper, this.currentVoClass()));
    }

    default List<V> selectVoListNotTenant() {
        return TenantHelper.ignore(() -> selectVoList(new QueryWrapper<>()));
    }

    default List<V> selectVoListNotTenant(Wrapper<T> wrapper) {
        return TenantHelper.ignore(() -> selectVoList(wrapper));
    }

    default <P extends IPage<V>> P selectVoPageNotTenant(IPage<T> page, Wrapper<T> wrapper) {
        return TenantHelper.ignore(() -> selectVoPage(page, wrapper));
    }

}
