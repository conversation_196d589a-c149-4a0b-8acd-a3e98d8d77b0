<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.hengjian</groupId>
    <artifactId>hengjian-distribution</artifactId>
    <version>${revision}</version>

    <name>HengJian-Distribution</name>
    <url>https://distribution.ehengjian.com/</url>
    <description>恒健分销管理系统</description>

    <properties>
        <revision>5.0.0-BETA</revision>
        <zsmall.version>1.0.0</zsmall.version>
        <spring-boot.version>2.7.11</spring-boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>11</java.version>
        <spring-boot.mybatis>2.3.0</spring-boot.mybatis>
        <springdoc.version>1.6.15</springdoc.version>
        <therapi-javadoc.version>0.15.0</therapi-javadoc.version>
        <poi.version>5.2.3</poi.version>
        <mq-amqp-client.version>1.0.4</mq-amqp-client.version>
        <easyexcel.version>3.3.2</easyexcel.version>
        <velocity.version>2.3</velocity.version>
        <satoken.version>1.35.0.RC</satoken.version>
        <mybatis-plus.version>*******</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <hutool.version>5.8.18</hutool.version>
        <okhttp.version>4.10.0</okhttp.version>
        <spring-boot-admin.version>2.7.10</spring-boot-admin.version>
        <redisson.version>3.20.1</redisson.version>
        <lock4j.version>2.2.4</lock4j.version>
        <dynamic-ds.version>4.1.1</dynamic-ds.version>
        <alibaba-ttl.version>2.14.2</alibaba-ttl.version>
        <xxl-job.version>2.4.0</xxl-job.version>
        <mapstruct-plus.version>1.3.1</mapstruct-plus.version>
        <mapstruct-plus.lombok.version>0.2.0</mapstruct-plus.lombok.version>
        <lombok.version>1.18.26</lombok.version>
        <bouncycastle.version>1.72</bouncycastle.version>
        <!-- 离线IP地址定位库 -->
        <ip2region.version>2.7.0</ip2region.version>

        <!-- 临时修复 snakeyaml 漏洞 -->
        <snakeyaml.version>1.33</snakeyaml.version>

        <!-- OSS 配置 -->
        <aws-java-sdk-s3.version>1.12.554</aws-java-sdk-s3.version>
        <!-- SMS 配置 -->
<!--	    <sms4j.version>2.2.0</sms4j.version>-->
        <sms4j.version>2.2.1</sms4j.version>
        <vonage-client.version>7.4.0</vonage-client.version>

        <!-- sendgrid-java 配置 -->
        <sendgrid-java.version>4.9.3</sendgrid-java.version>

        <!-- es配置 -->
        <!--<easy-es.version>2.0.0-beta2</easy-es.version>
        <elasticsearch.version>7.17.9</elasticsearch.version>-->
        <easy-es.version>2.1.0</easy-es.version>
        <elasticsearch.version>7.17.8</elasticsearch.version>

        <!-- payment 支付配置 -->
        <ijapy.version>2.9.7</ijapy.version>

        <!-- gson -->
        <gson-fire.version>1.8.4</gson-fire.version>
        <gson.version>2.8.9</gson.version>

        <!-- common-io -->
        <commons-io.version>2.12.0</commons-io.version>

        <!-- amazon -->
        <sellingpartner-api.version>1.0.0</sellingpartner-api.version>

        <!-- 插件版本 -->
        <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
        <maven-war-plugin.version>3.2.2</maven-war-plugin.version>
        <maven-compiler-plugin.verison>3.11.0</maven-compiler-plugin.verison>
        <maven-surefire-plugin.version>3.0.0</maven-surefire-plugin.version>
        <flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>
    </properties>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>local</profiles.active>
                <logging.level>debug</logging.level>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <logging.level>debug</logging.level>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <logging.level>debug</logging.level>
            </properties>
        </profile>
        <profile>
            <id>test-hj</id>
            <properties>
                <profiles.active>test-hj</profiles.active>
                <logging.level>debug</logging.level>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod-hj</id>
            <properties>
                <profiles.active>prod-hj</profiles.active>
                <logging.level>info</logging.level>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>stage-hj</id>
            <properties>
                <profiles.active>stage-hj</profiles.active>
                <logging.level>info</logging.level>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod-ek</id>
            <properties>
                <profiles.active>prod-ek</profiles.active>
                <logging.level>debug</logging.level>
            </properties>
        </profile>
    </profiles>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.mq-amqp</groupId>
                <artifactId>mq-amqp-client</artifactId>
                <version>${mq-amqp-client.version}</version>
            </dependency>
            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- common 的依赖配置-->
            <dependency>
                <groupId>com.hengjian</groupId>
                <artifactId>hengjian-common-bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hengjian-business 的依赖配置-->
            <dependency>
                <groupId>com.hengjian</groupId>
                <artifactId>hengjian-business-bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-webmvc-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-javadoc</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-conf-core</artifactId>
                <version>1.6.1</version>
            </dependency>
            <dependency>
                <groupId>com.github.therapi</groupId>
                <artifactId>therapi-runtime-javadoc</artifactId>
                <version>${therapi-javadoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>
            <!-- Sa-Token 整合 jwt -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${satoken.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-core</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- dynamic-datasource 多数据源-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${spring-boot.mybatis}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- sql性能分析插件 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>

            <!-- sendGrid email -->
            <dependency>
                <groupId>com.sendgrid</groupId>
                <artifactId>sendgrid-java</artifactId>
                <version>${sendgrid-java.version}</version>
            </dependency>
            <!--短信sms4j-->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j.version}</version>
            </dependency>
	    <!-- vonage sms  -->
            <dependency>
                <groupId>com.vonage</groupId>
                <artifactId>client</artifactId>
                <version>${vonage-client.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
            </dependency>

            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${alibaba-ttl.version}</version>
            </dependency>

            <!-- 临时修复 snakeyaml 漏洞 -->
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>

            <!-- 加密包引入 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15to18</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.linpeilie</groupId>
                <artifactId>mapstruct-plus-spring-boot-starter</artifactId>
                <version>${mapstruct-plus.version}</version>
            </dependency>

            <!-- 离线IP地址定位库 ip2region -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <!-- es 相关 -->
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <!-- 引入easy-es最新版本的依赖-->
            <!-- <dependency>
                <groupId>com.hengjian.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy-es.version}</version>
            </dependency>-->
            <dependency>
                <groupId>org.dromara.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy-es.version}</version>
            </dependency>

            <!-- IJPay -->
            <dependency>
                <groupId>com.github.javen205</groupId>
                <artifactId>IJPay-Core</artifactId>
                <version>${ijapy.version}</version>
            </dependency>

            <!-- payment payoneer -->
            <dependency>
                <groupId>com.github.javen205</groupId>
                <artifactId>IJPay-Payoneer</artifactId>
                <version>${ijapy.version}</version>
            </dependency>

            <!-- gson -->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>io.gsonfire</groupId>
                <artifactId>gson-fire</artifactId>
                <version>${gson-fire.version}</version>
            </dependency>
            <!-- commons-io -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <!-- hengjian -->
            <dependency>
                <groupId>com.hengjian</groupId>
                <artifactId>hengjian-system</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.hengjian</groupId>
                <artifactId>hengjian-system-entity</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.hengjian</groupId>
                <artifactId>hengjian-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.hengjian</groupId>
                <artifactId>hengjian-generator</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.hengjian</groupId>
                <artifactId>hengjian-demo</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.hengjian</groupId>
                <artifactId>hengjian-extend-emailsms</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.hengjian</groupId>
                <artifactId>hengjian-extend-excel-translation</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.hengjian</groupId>
                <artifactId>hengjian-event-system</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.hengjian</groupId>
                <artifactId>hengjian-stream-mq</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.hengjian</groupId>
                <artifactId>hengjian-stream-mq-producer</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- amazon api -->
            <dependency>
                <groupId>com.amazon.sellingpartnerapi</groupId>
                <artifactId>sellingpartner-api</artifactId>
                <version>${sellingpartner-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazon.sellingpartnerapi</groupId>
                <artifactId>sellingpartnerapi-aa-java</artifactId>
                <version>${sellingpartner-api.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>hengjian-admin</module>
        <module>hengjian-common</module>
        <module>hengjian-extend</module>
        <module>hengjian-modules</module>
        <module>hengjian-business</module>
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.verison}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.github.therapi</groupId>
                            <artifactId>therapi-runtime-javadoc-scribe</artifactId>
                            <version>${therapi-javadoc.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                        <path>
                            <groupId>io.github.linpeilie</groupId>
                            <artifactId>mapstruct-plus-processor</artifactId>
                            <version>${mapstruct-plus.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${mapstruct-plus.lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!-- 单元测试使用 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <!-- 根据打包环境执行对应的@Tag测试方法 -->
                    <groups>${profiles.active}</groups>
                    <!-- 排除标签 -->
                    <excludedGroups>exclude</excludedGroups>
                </configuration>
            </plugin>
            <!-- 统一版本号管理 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 关闭过滤 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 引入所有 匹配文件进行过滤 -->
                <includes>
                    <include>application*</include>
                    <include>bootstrap*</include>
                    <include>banner*</include>
                </includes>
                <!-- 启用过滤 即该资源中的变量将会被过滤器中的值替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
		  <id>hlnexus</id>
		  <name>maven-public</name>
		  <url>http://maven.ehengjian.com/nexus/repository/maven-public/</url>
		  <snapshots>
			<enabled>true</enabled>
		  </snapshots>
		  <releases>
			<enabled>true</enabled>
		  </releases>
		</repository>
    </repositories>

    <pluginRepositories>
		<pluginRepository>
		  <id>hlnexus</id>
		  <name>maven-public</name>
		  <url>http://maven.ehengjian.com/nexus/repository/maven-public/</url>
		  <snapshots>
			<enabled>true</enabled>
		  </snapshots>
		  <releases>
			<enabled>true</enabled>
		  </releases>
		</pluginRepository>
    </pluginRepositories>

    <distributionManagement>
		<repository>
		  <id>hlnexus</id>
		  <name>nexus-maven-releases</name>
		  <url>http://maven.ehengjian.com/nexus/repository/maven-releases/</url>
		</repository>
		<snapshotRepository>
		  <id>hlnexus</id>
		  <name>nexus-maven-snapshots</name>
		  <url>http://maven.ehengjian.com/nexus/repository/maven-snapshots/</url>
		</snapshotRepository>
    </distributionManagement>

</project>


